import { Key, useCallback, useEffect, useMemo, useState } from 'react';
import Fuse from 'fuse.js';

import { AdminAuditLogApi } from '@/api';
import { debounce } from '@/utils';

const { getData } = AdminAuditLogApi;

export const getListHook = (props: any) => {
  const { dateRange, setLoading, setAllRecord, messageApi } = props;

  const startTime = Math.floor(dateRange[0].valueOf() / 1000);
  const endTime = Math.ceil(dateRange[1].valueOf() / 1000);

  return debounce(async () => {
    setLoading(true);
    try {
      const result = await getData({ startTime, endTime, triggeredBy: null, requestPath: null, ipAddress: null }).then(
        (res: any) => {
          return res;
        },
      );
      setAllRecord(result.result);
    } catch (error) {
      messageApi.error('Failed to Retrieve Data!');
    } finally {
      setLoading(false);
    }
  }, 100);
};

export const fuzzySearchHook = (props: any) => {
  const { allRecord, nameFilter, threshold, keys, setLoading, setTotal } = props;

  return useMemo(() => {
    const fuseInstance = new Fuse(allRecord, { keys, threshold, ignoreLocation: true });

    if (!nameFilter) {
      setLoading(false);
      setTotal(allRecord.length);
      return allRecord;
    }
    setLoading(true);
    const result = fuseInstance.search(nameFilter).map((item) => item.item);
    setTotal(result.length);
    setLoading(false);
    return result;
  }, [allRecord, nameFilter]);
};

