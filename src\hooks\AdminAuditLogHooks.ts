import { Key, useCallback, useEffect, useMemo, useState } from 'react';
import Fuse from 'fuse.js';

import { AdminAuditLogApi } from '@/api';
import { debounce } from '@/utils';

const { getData } = AdminAuditLogApi;

export const getListHook = (props: any) => {
  const { dateRange, setLoading, setAllRecord, messageApi } = props;

  const startTime = Math.floor(dateRange[0].startOf('day').valueOf() / 1000);
  const endTime = Math.ceil(dateRange[1].endOf('day').add(1, 'days').valueOf() / 1000);

  return debounce(async () => {
    setLoading(true);
    try {
      const result = await getData({ startTime, endTime, triggeredBy: null, requestPath: null, ipAddress: null }).then(
        (res: any) => {
          return res;
        },
      );
      setAllRecord(result.result);
    } catch (error) {
      messageApi.error('Failed to Retrieve Data!');
    } finally {
      setLoading(false);
    }
  }, 100);
};

export const handleTimeChangeHook = (props: any) => {
  const { form, dateTime } = props;
  if (dateTime) {
    const start = dateTime[0].startOf('day').unix();
    const end = dateTime[1].endOf('day').unix();
    form.setFieldsValue({
      startTime: start,
      endTime: end,
      displayPeriod: dateTime,
    });
  } else {
    // Handle null case when clearing the picker
    form.setFieldsValue({
      startTime: null,
      endTime: null,
      displayPeriod: null,
    });
  }
};

export const fuzzySearchHook = (props: any) => {
  const { allRecord, nameFilter, threshold, keys, setLoading, setTotal } = props;

  return useMemo(() => {
    const fuseInstance = new Fuse(allRecord, { keys, threshold, ignoreLocation: true });

    if (!nameFilter) {
      setLoading(false);
      setTotal(allRecord.length);
      return allRecord;
    }
    setLoading(true);
    const result = fuseInstance.search(nameFilter).map((item) => item.item);
    setTotal(result.length);
    setLoading(false);
    return result;
  }, [allRecord, nameFilter]);
};

export const handleStartTimeChangeHook = (props: any) => {
  const { date, setStartTime } = props;
  setStartTime(!date ? null : date.unix());
};

// /**
//  * Hooks to reset the state of AdModal component when modal is visible/invisible
//  * @param props:
//  */
// export const modalResetHook = (props: any) => {
//   const {
//     isModalVisible,
//     form,
//     recordId,
//     setRecordData,
//     setImageUrl,
//     setCountry,
//     setNewCountryList,
//     setIsCommonAd,
//     setPlatform,
//     setPosition,
//     messageApi,
//   } = props;
//   useEffect(() => {
//     if (!isModalVisible) {
//       form.resetFields();
//       setRecordData(null);
//       setImageUrl('');
//       setCountry([]);
//       setNewCountryList([]);
//       setIsCommonAd(false);
//       setPlatform('');
//       setPosition('');
//     }
//     if (recordId) {
//       getSingleData(recordId)
//         .then((res: any) => {
//           const selectedRecord: AdListDataType = res.result;
//           setRecordData(selectedRecord);
//         })
//         .catch((error) => {
//           messageApi.open({ type: 'error', content: 'Error Updating Ad!' });
//         });
//     }
//   }, [isModalVisible]);
// };
