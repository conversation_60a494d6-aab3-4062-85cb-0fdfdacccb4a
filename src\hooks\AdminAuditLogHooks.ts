import { Key, useCallback, useMemo } from 'react';
import Fuse from 'fuse.js';

import { AdminAuditLogApi } from '@/api';
import { debounce } from '@/utils';
import { AxiosResponse } from 'axios';

const { getData, downloadCSV } = AdminAuditLogApi;

export const getListHook = (props: any) => {
  const { dateRange, setLoading, setAllRecord, messageApi } = props;

  const startTime = Math.floor(dateRange[0].valueOf() / 1000);
  const endTime = Math.ceil(dateRange[1].valueOf() / 1000);

  return debounce(async () => {
    setLoading(true);
    try {
      const result = await getData({ startTime, endTime, triggeredBy: null, requestPath: null, ipAddress: null }).then(
        (res: any) => {
          return res;
        },
      );
      setAllRecord(result.result);
    } catch (error) {
      messageApi.error('Failed to Retrieve Data!');
    } finally {
      setLoading(false);
    }
  }, 100);
};

export const fuzzySearchHook = (props: any) => {
  const { allRecord, nameFilter, threshold, keys, setLoading, setTotal } = props;

  return useMemo(() => {
    const fuseInstance = new Fuse(allRecord, { keys, threshold, ignoreLocation: true });

    if (!nameFilter) {
      setLoading(false);
      setTotal(allRecord.length);
      return allRecord;
    }
    setLoading(true);
    const result = fuseInstance.search(nameFilter).map((item) => item.item);
    setTotal(result.length);
    setLoading(false);
    return result;
  }, [allRecord, nameFilter]);
};

export const downloadCSVHook = (props: any) => {
  const { dateRange, messageApi } = props;

  return useCallback(async () => {
    const startTime = Math.floor(dateRange[0].valueOf() / 1000);
    const endTime = Math.ceil(dateRange[1].valueOf() / 1000);

    try {
      messageApi.loading({ content: 'Preparing CSV download...', key: 'download' });

      const response: any = await downloadCSV({
        startTime,
        endTime,
        triggeredBy: null,
        requestPath: null,
        ipAddress: null,
      });

      if (!response) {
        throw new Error('No Data Available for Download!');
      }

      const url = window.URL.createObjectURL(response);
      const link = document.createElement('a');
      link.href = url;
      
      const currentDate = new Date().toISOString().split('T')[0];
      link.download = `admin_audit_log_${currentDate}.csv`;

      document.body.appendChild(link);
      link.click();

      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      messageApi.success({ content: 'CSV downloaded successfully!', key: 'download' });
    } catch (error) {
      console.error('Download failed:', error);
      messageApi.error({
        content: error instanceof Error ? error.message : 'Failed to download CSV!',
        key: 'download',
      });
    }
  }, [dateRange, messageApi]);
};
