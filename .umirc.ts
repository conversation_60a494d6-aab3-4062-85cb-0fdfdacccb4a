import { defineConfig } from 'umi';
import routes from './routes';

export default defineConfig({
  define: {
    API_KEY: '33d6ae11-5793-4769-aa49-a24cd847f0e0',
    DEVICE_TYPE: 'back_office',
    APP_ENV: process.env.APP_ENV ? process.env.APP_ENV : 'production',
  },
  nodeModulesTransform: {
    type: 'none',
  },
  routes,
  fastRefresh: {},
  hash: true,
  dynamicImport: {
    loading: '@/components/Common/UmiLoading',
  },
  links: [
    {
      rel: 'icon',
      href: process.env.APP_ENV === 'staging' ? '/img/favicon_stg.ico' : '/img/favicon.ico',
    },
  ],
});
