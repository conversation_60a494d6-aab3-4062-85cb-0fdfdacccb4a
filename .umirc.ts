import { defineConfig } from 'umi';
import routes from './routes';
import dotenv from 'dotenv';
dotenv.config();

export default defineConfig({
  define: {
    API_KEY: process.env.API_KEY || 'no_key',
    DEVICE_TYPE: 'back_office',
    APP_ENV: process.env.APP_ENV || 'production',
    ENCRYPTION_SECRET_KEY: process.env.ENCRYPTION_SECRET_KEY || 'no_secret',
  },
  nodeModulesTransform: {
    type: 'none',
  },
  routes,
  fastRefresh: {},
  hash: true,
  dynamicImport: {
    loading: '@/components/Common/UmiLoading',
  },
  links: [
    {
      rel: 'icon',
      href: process.env.APP_ENV === 'staging' ? '/img/favicon_stg.ico' : '/img/favicon.ico',
    },
  ],
});
