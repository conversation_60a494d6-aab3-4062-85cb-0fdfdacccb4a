import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Modal, Form, Input, Typography, Select, notification, message, InputNumber } from 'antd';
import { PopularCompetitionDataType } from '@/model';
import { igscoreSiteCompetitionUrlRegex } from '@/constant';
import { PopularCompetitionApi } from '@/api';
import { debounce, urlStringCheck } from '@/utils';

import './Modal.less';
import { PopularCompetitionHooks } from '@/hooks';
import { generateSortingNumber } from '@/api/popularCompetitionApi';

const { Text } = Typography;
const { TextArea } = Input;
const { confirm } = Modal;
const { populateDataHook, modalResetHook, sportTypeListHook, sortingSettingHook } = PopularCompetitionHooks;
const { updateData, createData, checkDuplicate } = PopularCompetitionApi;
const pageDisplayLabel = 'Popular Competition';

interface PopularCompetitionModalProps {
  isModalVisible: boolean;
  onCancel: () => void;
  resetModal: () => void;
  actionType: string;
  recordId: number | null;
}

const PopularCompetitionModal: React.FC<PopularCompetitionModalProps> = (props) => {
  const { isModalVisible, onCancel, resetModal, actionType, recordId } = props;
  const [form] = Form.useForm();
  const [messageApi, contextHolder] = message.useMessage();

  const [recordData, setRecordData] = useState<PopularCompetitionDataType | null>(null);
  const [siteUrl, setSiteUrl] = useState<string>('');
  const [sportType, setSportType] = useState<number | null>(null);
  const [dataId, setDataId] = useState<string>('');
  const [sorting, setSorting] = useState<number | null>(null);

  const delayInMS = 100;

  useEffect(() => {
    if (recordData) {
      form.setFieldsValue(recordData);
      setSportType(recordData?.sportType ?? null);
      setDataId(recordData?.dataId || '');
      setSiteUrl(recordData?.siteUrl || '');
      setSorting(recordData?.sorting || null);
    }
  }, [recordData]);

  // hook to handle data population
  populateDataHook({ form, siteUrl, setSportType, setDataId, recordData });

  // reset state of modal
  modalResetHook({ isModalVisible, form, recordId, setRecordData, setSiteUrl, setSorting, setSportType, setDataId, messageApi });

  // hook to get auto incremental sorting number (under creat new popular competition condition)
  sortingSettingHook({ setSorting, form, sorting, sportType });

  const actionConfirm = (data: PopularCompetitionDataType) => {
    confirm({
      title: `Confirmation`,
      content:
        actionType === 'create' ? (
          <Text>Confirm Create New {pageDisplayLabel}?</Text>
        ) : (
          <Text>Confirm Update Selected {pageDisplayLabel}?</Text>
        ),
      onOk() {
        actionResult(data);
      },
      onCancel() {},
      maskClosable: true,
    });
  };

  const actionResult = (data: PopularCompetitionDataType) => {
    try {
      let result;
      if (actionType === 'create') {
        result = createData(data);
      } else {
        result = updateData(data);
      }
      notification.success({
        key: 'success',
        message: 'Success!',
        description:
          actionType === 'create' ? (
            <Text>New {pageDisplayLabel} Create Success!</Text>
          ) : (
            <Text>
              Update Success! {pageDisplayLabel} ID: <b> {recordData?.id || ''}</b>
            </Text>
          ),
      });
    } catch (error) {
      notification.error({
        key: 'error',
        message: 'Error!',
        description:
          actionType === 'create' ? (
            <Text>New {pageDisplayLabel} Create Failed!</Text>
          ) : (
            <Text>
              Update Failed! {pageDisplayLabel} ID: <b> {recordData?.id || ''}</b>
            </Text>
          ),
      });
    } finally {
      resetModal();
    }
  };

  const duplicateValidation = debounce((rule: any, value: string, callback: any) => {
    if (dataId !== null && sportType !== null) {
      checkDuplicate({ dataId, sportType, id: recordData?.id || null })
        .then((res: boolean) => {
          if (!res) {
            return callback();
          }
          return callback('Duplicate Data Found!');
        })
        .catch(() => {
          return callback('Duplicate Data Check Error!');
        });
    }
  }, delayInMS);

  const checkSiteUrl = debounce((rule: any, value: string, callback: any): string => {
    if (!urlStringCheck(value, new RegExp(igscoreSiteCompetitionUrlRegex))) {
      return callback('Not a valid URL!');
    }
    setSiteUrl(value);
    return callback();
  }, delayInMS);

  const checkMaxSorting = debounce((rule: any, value: number, callback: any) => {
    if (sportType !== null) {
      generateSortingNumber(sportType)
        .then((res: any) => {
          res.result = actionType === 'create' ? res.result : res.result - 1;
          if (value > res.result) {
            return callback(`Sorting Setting Exceeded! Max: ${res.result}`);
          }
          return callback();
        })
        .catch(() => {
          return callback('Max Sorting Setting Check Error!');
        });
    }
  }, delayInMS);

  return (
    <Modal
      title={
        actionType === 'create' ? `Create New ${pageDisplayLabel}` : `Update ${pageDisplayLabel} (ID: ${recordData?.id || ''})`
      }
      open={isModalVisible}
      onCancel={onCancel}
      onOk={() => form.submit()}
      width={700}
    >
      {contextHolder}
      <Form
        form={form}
        layout="horizontal"
        onFinish={actionConfirm}
        onFinishFailed={() =>
          messageApi.open({
            type: 'error',
            content: `Submission Error!`,
          })
        }
        scrollToFirstError
        initialValues={{ remember: true }}
        autoComplete="off"
        labelCol={{ span: 6 }}
        labelWrap={true}
        labelAlign="left"
        className="form-styling"
      >
        <Form.Item
          name="siteUrl"
          label="Site URL"
          hasFeedback={true}
          validateFirst
          rules={[
            { required: true, message: 'Missing Site URL!' },
            { validator: checkSiteUrl },
            { validator: duplicateValidation },
          ]}
          className="site-input"
        >
          <TextArea
            rows={4}
            placeholder="https://www.igscore.net/...."
            value={recordData?.siteUrl}
            allowClear
            onChange={(e) => setSiteUrl(e.target.value)}
          />
        </Form.Item>
        <Form.Item name="sportType" label="Sport Type" rules={[{ required: true, message: 'Missing Sport Type!' }]}>
          <Select
            options={sportTypeListHook()}
            value={sportType}
            placeholder="Sport Type"
            disabled
            className="custom-disabled-select"
          />
        </Form.Item>
        <Form.Item name="dataId" label="Data ID" rules={[{ required: true, message: 'Missing Data ID!' }]} validateFirst>
          <Input value={dataId} placeholder="Data ID..." disabled className="custom-disabled-input" />
        </Form.Item>
        <Form.Item
          name="sorting"
          label="Sorting Setting"
          rules={[{ required: true, message: 'Missing Sorting Setting!' }, { validator: checkMaxSorting }]}
        >
          <InputNumber placeholder="Sorting Setting..." value={sorting} />
        </Form.Item>
        <Form.Item name="description" label="Description:">
          <TextArea
            disabled
            value={recordData?.description}
            placeholder="Featured Section Description..."
            rows={3}
            className="custom-disabled-input"
          />
        </Form.Item>
        <Form.Item name="extraDescription" label="Extra Description:">
          <TextArea value={recordData?.description} placeholder="Please input the description" rows={3} />
        </Form.Item>
        <Form.Item name="startTime" hidden />
        <Form.Item name="endTime" hidden />
        <Form.Item name="status" hidden />
        <Form.Item name="id" hidden />
      </Form>
    </Modal>
  );
};

export default PopularCompetitionModal;
