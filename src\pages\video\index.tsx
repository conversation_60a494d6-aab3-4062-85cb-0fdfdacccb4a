// @ts-nocheck
// noinspection JSUnresolvedReference

import React, { useEffect, useState } from 'react';
import { Space, Table, Modal, Button, Select, DatePicker, Input, Form, message, Popover, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { DeleteOutlined, EditOutlined, InfoCircleOutlined, PlusOutlined, StopOutlined } from '@ant-design/icons';
import { banMatchVideo, createVideo, getVideoList } from '@/api';
import { SportTypeNameEnum, SportTypeLiveStatusEnum, GlobalSportPathnameSpellingStr, NotStartStatus } from '@/constant';
import dayjs from 'dayjs';
import { getGameIdFromUrl, getVideoParamsFromUrl, getGameType } from '@/utils/help';
import BlockCountries from '@/components/CountryComponent/BlockCountries';
import BlockedCountryName from '@/components/CountryComponent/BlockedCountryName';
import UploadFile from '@/components/UploadFile';
import videoLinkDemo from '../../../public/img/videolinkdemo.png';
import './index.less';
import EditVideo from '@/components/EditVideo';
import { CountryList } from '@/constant/country';

const { RangePicker } = DatePicker;
const { error, confirm } = Modal;

const format = 'YYYY-MM-DD';
const endDay = dayjs();
const startDay = dayjs().subtract(1, 'days');

const sportsArray = Object.keys(GlobalSportPathnameSpellingStr).map((key) => {
  return {
    value: key,
    // @ts-ignore
    label: GlobalSportPathnameSpellingStr[key],
  };
});
sportsArray.unshift({
  value: '',
  label: 'Sport Type',
});

interface DataType {
  awayTeamId: string;
  awayTeamName: string;
  competitionId: string;
  competitionName: string;
  countryCodes: string[];
  homeTeamId: string;
  homeTeamName: string;
  id: any;
  matchId: string;
  matchTime: number;
  sportType: number;
  statusId: number;
  videoId: string;
}

const getStatus = (statusId: number, sportType: string) => {
  if (statusId == NotStartStatus) {
    return 'prematch';
  }
  if (SportTypeLiveStatusEnum[sportType].includes(statusId)) {
    return 'running';
  }
  return 'ended';
};

const VideoList: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const [list, setList] = useState<DataType[]>([]);
  const [editItem, setEditItem] = useState<DataType>(null);
  const [blockGameList, setBlockGameList] = useState<DataType[]>([]);
  const [defaultCheckedList, setDefaultCheckedList] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [sportType, setSportType] = useState('');
  const [dateRange, setDateRange] = useState([startDay, endDay]);
  const [pageNum, setPageNum] = useState(0);
  const [pageSize, setPageSize] = useState(50);
  const [open, setOpen] = useState(false);
  const [openCountry, setOpenCountry] = useState(false);
  // https://www.igscore.net/football-match/y39mp1h07e6lmoj/MatchLive/
  const [gameLink, setGameLink] = useState('');
  //https://office.sportsmng.com/Channelschedule?ch=409&i=64c1251aa61a23ee9bfe26d2&s=&flv=undefined
  const [videoLink, setVideoLink] = useState('');
  const [total, setTotal] = useState(0);

  const onPageChange = (nextPageNumber, nextPageSize) => {
    // setPageNum(pageNumber - 1)
    if (pageSize !== nextPageSize) {
      setPageSize(nextPageSize);
      setPageNum(0);
    } else {
      setPageNum(nextPageNumber - 1);
    }
  };

  const getList = () => {
    setLoading(true);
    getVideoList({
      sportType,
      startTime: Math.floor(dateRange[0].valueOf() / 1000),
      endTime: Math.ceil(dateRange[1].add(1, 'days').valueOf() / 1000),
      pageNum,
      pageSize,
    })
      .then((d: any) => {
        setList(d.result.videoMatches);
        setTotal(d.result.total);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getList();
  }, [sportType, dateRange, pageSize, pageNum]);

  const block = (item: DataType) => {
    const { countryCodes = [] } = item;
    setDefaultCheckedList(countryCodes || []);
    setOpenCountry(true);
    setBlockGameList([item]);
  };

  const blockAll = () => {
    const selectedList = list.filter((item) => selectedRowKeys.includes(item.matchId));
    setDefaultCheckedList([]);
    setOpenCountry(true);
    setBlockGameList(selectedList);
  };

  const deleteAll = () => {
    confirm({
      title: `Are you sure to delete?`,
      type: 'warning',
      onOk: () => {
        const selectedList = list
          .filter((item) => selectedRowKeys.includes(item.matchId))
          .map((item) => {
            const { sportType, matchId } = item;
            return {
              matchId,
              videoId: null,
              sportType,
            };
          });
        uploadVideo(selectedList);
      },
    });
  };

  const onCloseBlockModal = () => {
    setDefaultCheckedList([]);
    setOpenCountry(false);
    setBlockGameList([]);
  };

  const onBlockCountriesConfirm = (countryCodes: string[]) => {
    onCloseBlockModal();
    const videoMatchBanList = blockGameList.map((item) => {
      return {
        matchId: item.matchId,
        countryCodes,
        sportType: item.sportType,
      };
    });
    banMatchVideo(videoMatchBanList).then((d) => {
      if (d.code === 'A00000') {
        const nextList = list.map((item) => {
          const { matchId, countryCodes } = item;
          const nextCountryCodes = videoMatchBanList.find((m) => m.matchId === matchId)?.countryCodes;
          return {
            ...item,
            countryCodes: nextCountryCodes || countryCodes,
          };
        });
        setList(nextList);
      } else {
        error({
          title: d.message,
        });
      }
    });
  };

  const onSelectChange = (newSelectedRowKeys: []) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const handleChangeSport = (v: string) => {
    setSportType(v);
  };

  const handleChangeDate = (v1: any, d) => {
    setDateRange(v1);
  };

  const uploadVideo = (gameVideoList) => {
    createVideo(gameVideoList).then((d) => {
      if (d.code === 'A00000') {
        setOpen(false);
        getList();
      } else {
        error({
          title: d.message,
        });
      }
    });
  };

  const importExcel = (list) => {
    let gameVideoList = [];
    let count = 0;
    for (let v of list) {
      try {
        const matchId = getGameIdFromUrl(v.matchlink);
        const sportType = getGameType(v.matchlink);
        const videoId = getVideoParamsFromUrl(v.videolink).ch;
        gameVideoList.push({
          matchId,
          videoId,
          sportType,
        });
      } catch (e) {
        console.error(e);
        count++;
      }
    }
    if (count > 0) {
      messageApi.open({
        type: 'error',
        content: `${count} items data valid`,
      });
    }
    uploadVideo(gameVideoList);
  };

  const addVideo = () => {
    if (!gameLink || !videoLink) return false;
    let matchId, sportType, videoId;
    try {
      matchId = getGameIdFromUrl(gameLink);
      sportType = getGameType(gameLink);
      videoId = videoLink.includes('https://') ? getVideoParamsFromUrl(videoLink).ch : videoLink;
    } catch (e) {
      error({
        title: e.toString(),
      });
      return false;
    }
    const createdBy = localStorage.getItem(localStorageKey.USERNAME);
    uploadVideo([
      {
        matchId,
        videoId,
        sportType,
        createdBy,
      },
    ]);
    setGameLink('');
    setVideoLink('');
  };

  const deleteItem = (item: DataType) => {
    confirm({
      title: 'Are you sure to delete this item',
      type: 'warn',
      onOk: () => {
        const { sportType, matchId } = item;
        uploadVideo([
          {
            matchId,
            videoId: null,
            sportType,
          },
        ]);
      },
    });
  };

  const columns: ColumnsType<DataType> = [
    {
      title: 'SportType',
      dataIndex: 'sportType',
      key: 'sportType',
      width: 105,
      render: (sportType) => <span>{SportTypeNameEnum[sportType]}</span>,
    },
    {
      title: 'StartTime',
      dataIndex: 'matchTime',
      key: 'matchTime',
      width: 180,
      defaultSortOrder: 'descend',
      sorter: (a, b) => {
        if (a.matchTime === b.matchTime) {
          const statusA = getStatus(a.statusId, a.sportType);
          const statusB = getStatus(b.statusId, b.sportType);
          if (statusA === statusB) {
            if (a.sportType === b.sportType) {
              return a.competitionName.localeCompare(b.competitionName);
            }
            return a.sportType - b.sportType;
          }
          return statusA.localeCompare(statusB);
        }
        return a.matchTime - b.matchTime;
      },
      render: (matchTime) => <span>{dayjs(matchTime * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
    },
    {
      title: 'Competition',
      dataIndex: 'competitionName',
      key: 'competitionName',
    },
    {
      title: 'HomeTeam',
      dataIndex: 'homeTeamName',
      key: 'homeTeamName',
    },
    {
      title: 'AwayTeam',
      dataIndex: 'awayTeamName',
      key: 'awayTeamName',
    },
    {
      title: 'Status',
      dataIndex: 'statusId',
      key: 'statusId',
      width: 100,
      filters: [
        { text: 'running', value: 'running' },
        { text: 'prematch', value: 'prematch' },
        { text: 'ended', value: 'ended' },
      ],
      // specify the condition of filtering result
      // here is that finding the name started with `value`
      onFilter: (value: string, record) => {
        return getStatus(record.statusId, record.sportType) === value;
      },
      render: (statusId, record) => {
        const status = getStatus(statusId, record.sportType);
        if (status === 'running') {
          return <Tag color="success">running</Tag>;
        }
        if (status === 'prematch') {
          return (
            <Tag color="warning" style={{ borderStyle: 'dashed' }}>
              prematch
            </Tag>
          );
        }
        return <Tag color="default">ended</Tag>;
      },
    },
    {
      title: 'createdBy',
      dataIndex: 'createdBy',
      key: 'createdBy',
      render: (createdBy) => createdBy || 'unknown',
    },
    {
      title: 'BlockedCountry',
      dataIndex: 'countryCodes',
      key: 'countryCodes',
      filters: CountryList.map((item) => ({ text: item.label, value: item.value })),
      onFilter: (value: any, record) => record.country.includes(value),
      render: (countryCodes) => {
        return <BlockedCountryName countryCodes={countryCodes} />;
      },
    },
    {
      title: 'Options',
      key: 'options',
      render: (_, record) => (
        <Space size="small">
          <Button size="small" onClick={() => setEditItem(record)}>
            <EditOutlined />
            Edit
          </Button>
          <Button size="small" onClick={() => block(record)}>
            <StopOutlined />
            Block
          </Button>
          <Button size="small" danger onClick={() => deleteItem(record)}>
            <DeleteOutlined />
            Delete
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {contextHolder}
      <Space wrap style={{ marginBottom: 16 }}>
        <span>Selected {selectedRowKeys.length} Items</span>
        <Select defaultValue={sportType} style={{ width: 150 }} onChange={handleChangeSport} options={sportsArray} />
        <RangePicker format={format} defaultValue={dateRange} onChange={handleChangeDate} clearIcon={false} />

        <Button type="primary" onClick={() => setOpen(true)}>
          <PlusOutlined />
          Create
        </Button>

        <UploadFile columns={['matchlink', 'videolink']} onConfirm={importExcel} isVideo={true} />

        <Button disabled={selectedRowKeys.length === 0} onClick={blockAll}>
          <StopOutlined />
          Block Country
        </Button>

        <Button type="primary" danger disabled={selectedRowKeys.length === 0} onClick={deleteAll}>
          <DeleteOutlined />
          Delete{selectedRowKeys.length > 0 ? ` ${selectedRowKeys.length} Items` : ''}
        </Button>
      </Space>
      <Table
        rowKey="matchId"
        size="small"
        rowSelection={{
          selectedRowKeys,
          onChange: onSelectChange,
        }}
        loading={loading}
        columns={columns}
        dataSource={list}
        pagination={{
          total,
          pageSize,
          defaultPageSize: 50,
          pageSizeOptions: [20, 50, 100],
          showSizeChanger: true,
          current: pageNum + 1,
          showQuickJumper: true,
          onChange: onPageChange,
          showTotal: (total, range) => (
            <text>
              Showing <b>{range[0]}-{range[1]}</b> of <b>{total}</b>
            </text>
          ),
        }}
      />
      {open && (
        <Modal
          title="Add Live stream"
          style={{ top: '100px' }}
          open={true}
          onOk={addVideo}
          okButtonProps={{
            disabled: !gameLink || !videoLink,
          }}
          onCancel={() => setOpen(false)}
        >
          <Form name="basic" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ maxWidth: 600 }} autoComplete="off">
            <Form.Item
              label="Igscore Match Link"
              name="game"
              rules={[{ required: true, message: 'Please input Igscore Match Link!' }]}
            >
              <Input placeholder="Please input Igscore Match Link!" onChange={(e) => setGameLink(e.target.value)} />
            </Form.Item>

            <Form.Item
              label="Live Link or ID"
              name="video"
              rules={[{ required: true, message: 'Please input Live Link or ID!' }]}
            >
              <div className="videoDemo">
                <Input placeholder="Please input Live stream Link or ID!" onChange={(e) => setVideoLink(e.target.value)} />
                <Popover
                  content={<img src={videoLinkDemo} style={{ width: '600px', height: 'auto' }} alt="" />}
                  title=""
                  placement="bottomLeft"
                  arrow={false}
                >
                  <InfoCircleOutlined style={{ background: '#fff', marginLeft: '10px' }} />
                </Popover>
              </div>
            </Form.Item>
          </Form>
        </Modal>
      )}
      {openCountry ? (
        <BlockCountries
          defaultCheckedList={defaultCheckedList}
          onBlockCoutriesConfirm={onBlockCountriesConfirm}
          onClose={onCloseBlockModal}
          title="Update: Country Setting for Live Stream Video"
        />
      ) : null}
      {editItem && (
        <EditVideo
          item={editItem}
          onCancel={() => setEditItem(null)}
          onConfirm={(nextItem) => {
            uploadVideo([nextItem]);
            setEditItem(null);
          }}
        />
      )}
    </div>
  );
};

export default VideoList;
