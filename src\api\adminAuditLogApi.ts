import igsRequest from './instance';
import { AdminAuditLogSearchRequest } from '@/model';

const urlPath = 'master/admin/audit';

export const getData = async (request: AdminAuditLogSearchRequest) => {
  return igsRequest.post(`${urlPath}/search`, request);
};

export const downloadCSV = async (request: AdminAuditLogSearchRequest) => {
  return igsRequest.post(`${urlPath}/download`, request, { responseType: 'blob' });
};
