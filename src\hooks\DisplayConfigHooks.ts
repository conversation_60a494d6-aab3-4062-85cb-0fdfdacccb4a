import { DisplayConfigApi } from '@/api';

const { getData } = DisplayConfigApi;

export const getDisplayConfigDataHook = (props: any) => {
  const { setData, setLoading, messageApi } = props;
  setLoading(true);
  return getData()
    .then((res: any) => {
      setData(res.result);
    })
    .catch(() => {
      messageApi.error('Failed to Retrieve Data!');
    })
    .finally(() => {
      setLoading(false);
    });
};
