import igsRequest from './instance';

const urlPath = 'user/popular/competition';

export const getData = async (request: any) => {
  return igsRequest.post(`${urlPath}/search`, request);
};

export const checkDuplicate = async (params: any) => {
  const response: any = await igsRequest.post(`${urlPath}/duplicate`, params);
  return response.result;
};

export const createData = async (params: any) => {
  return igsRequest.post(
    `${urlPath}/create`,
    Object.assign(params, {
      client: '123',
      createdBy: localStorage.getItem('username'),
    }),
  );
};

export const getDescription = async (params: any) => {
  return igsRequest.post(`${urlPath}/description`, params);
};

export const updateData = async (params: any) => {
  return igsRequest.post(
    `${urlPath}/update`,
    Object.assign(params, {
      client: '123',
      modifiedBy: localStorage.getItem('username'),
    }),
  );
};

export const getSingleData = async (id: number) => {
  return igsRequest.get(`${urlPath}/${id}`);
};

export const generateSortingNumber = async (sportType: number) => {
  return igsRequest.get(`${urlPath}/sorting`, { params: { sportType } });
};

export const deleteData = async (ids: number[]) => {
  return igsRequest.post(`${urlPath}/delete`, ids);
};
