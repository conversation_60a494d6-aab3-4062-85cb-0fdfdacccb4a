import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { message } from 'antd';
import { goLogin, goLogout } from '@/api/api';
import { Authority, UserData, AuthContextType, ResponseData } from '@/model';
import { localStorageKey } from '@/constant/config';
import { createCipheriv, createDecipheriv, randomBytes, scryptSync } from 'crypto';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Simple obfuscation functions (in production, use proper encryption)
const obfuscate = (data: string): string => {
  // return btoa(encodeURIComponent(data + Date.now().toString().slice(-4)));
  // const salt = randomBytes(16);
  // const key = scryptSync(ENCRYPTION_SECRET_KEY, salt, 32); // AES-256 key
  // const iv = randomBytes(16); // Unique per encryption
  // const cipher = createCipheriv('aes-256-gcm', key, iv);

  // const encrypted = Buffer.concat([cipher.update(text, 'utf8'), cipher.final()]);
  // const authTag = cipher.getAuthTag(); // For integrity check

  // return Buffer.concat([salt, iv, authTag, encrypted]).toString('base64');
  console.log("ASDDASDSA", ENCRYPTION_SECRET_KEY)
  const salt = randomBytes(16); // 16 bytes salt
  const key = scryptSync(ENCRYPTION_SECRET_KEY, salt, 32); // 32 bytes for AES-256
  const iv = randomBytes(16); // AES-GCM requires 12–16 bytes IV

  const cipher = createCipheriv('aes-256-gcm', key, iv);
  const encrypted = Buffer.concat([cipher.update(data, 'utf8'), cipher.final()]);
  const authTag = cipher.getAuthTag();

  // Combine all parts: salt + iv + authTag + encrypted
  const result = Buffer.concat([salt, iv, authTag, encrypted]);
  return result.toString('base64');
};

const deobfuscate = (encryptedData: string): string | null => {
  // try {
  //   const decoded = decodeURIComponent(atob(data));

  //   const result = decoded.slice(0, -4); // Remove timestamp

  //   return result;
  // } catch (error) {
  //   console.error('Deobfuscation error:', error);
  //   return null;
  // }
  try {
    // const data = Buffer.from(encryptedData, 'base64');
    // const salt = data.subarray(0, 16);
    // const iv = data.subarray(16, 32);
    // const authTag = data.subarray(32, 48);
    // const encrypted = data.subarray(48);

    // const key = scryptSync(ENCRYPTION_SECRET_KEY, salt, 32);
    // const decipher = createDecipheriv('aes-256-gcm', key, iv);
    // decipher.setAuthTag(authTag);

    // return Buffer.concat([decipher.update(encrypted), decipher.final()]).toString('utf8');
    const buffer = Buffer.from(encryptedText, 'base64');

    const salt = buffer.subarray(0, 16);
    const iv = buffer.subarray(16, 32);
    const authTag = buffer.subarray(32, 48);
    const encrypted = buffer.subarray(48);

    const key = scryptSync(ENCRYPTION_SECRET_KEY, salt, 32);
    const decipher = createDecipheriv('aes-256-gcm', key, iv);
    decipher.setAuthTag(authTag);

    const decrypted = Buffer.concat([decipher.update(encrypted), decipher.final()]);
    return decrypted.toString('utf8');
  } catch (error) {
    console.error('Deobfuscation error:', error);
    return null;
  }
};

// Generate a verification hash for roles
const generateRoleHash = (authorities: Authority[]): string => {
  const roleString = authorities
    .map((auth) => auth.authority)
    .sort()
    .join('|');
  return btoa(roleString + window.location.hostname);
};

// Verify role hash integrity
const verifyRoleHash = (authorities: Authority[], hash: string): boolean => {
  return generateRoleHash(authorities) === hash;
};

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [messageApi, contextHolder] = message.useMessage();

  // Initialize auth state from secure storage
  useEffect(() => {
    const initAuth = () => {
      try {
        const storedToken = localStorage.getItem(localStorageKey.AUTH_SESSION);
        const storedRoleHash = localStorage.getItem(localStorageKey.ROLE_HASH);

        if (storedToken && storedRoleHash) {
          const userData = deobfuscate(storedToken);
          if (userData) {
            const parsedUser: UserData = JSON.parse(userData);

            // Verify role hash integrity
            if (verifyRoleHash(parsedUser.authorities, storedRoleHash)) {
              setUser(parsedUser);
            } else {
              // Hash mismatch - possible tampering
              clearAuthData();
              messageApi.warning('Session integrity check failed. Please login again.');
            }
          }
        } else {
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        clearAuthData();
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  const clearAuthData = () => {
    localStorage.removeItem(localStorageKey.AUTH_SESSION);
    localStorage.removeItem(localStorageKey.ROLE_HASH);
    localStorage.removeItem(localStorageKey.USERNAME); // Clear existing username storage
    setUser(null);
  };

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      setLoading(true);

      const response: ResponseData<UserData> = await goLogin({ username, password });

      if (response.code === 'A00000' && response.result) {
        const userData: UserData = response.result;

        // Store user data with obfuscation
        const obfuscatedData = obfuscate(JSON.stringify(userData));
        const roleHash = generateRoleHash(userData.authorities);

        localStorage.setItem(localStorageKey.AUTH_SESSION, obfuscatedData);
        localStorage.setItem(localStorageKey.ROLE_HASH, roleHash);
        localStorage.setItem(localStorageKey.USERNAME, userData.username); // Keep for compatibility

        setUser(userData);

        messageApi.success('Login successful!');
        return true;
      } else {
        messageApi.error('Invalid credentials');
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      messageApi.error('Login failed. Please try again.');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await goLogout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      clearAuthData();
      messageApi.success('Logged out successfully');
    }
  };

  const hasRole = (role: string): boolean => {
    if (!user || !user.authorities) return false;

    // Re-verify role hash on each check to prevent tampering
    const storedRoleHash = localStorage.getItem(localStorageKey.ROLE_HASH);
    if (!storedRoleHash || !verifyRoleHash(user.authorities, storedRoleHash)) {
      clearAuthData();
      return false;
    }

    return user.authorities.some((auth) => auth.authority === role);
  };

  const isAuthenticated = !!user;

  const value: AuthContextType = {
    user,
    isAuthenticated,
    hasRole,
    login,
    logout,
    loading,
  };

  return (
    <AuthContext.Provider value={value}>
      {contextHolder}
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
