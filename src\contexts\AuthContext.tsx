import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { message } from 'antd';
import { goLogin, goLogout } from '@/api/api';
import { Authority, UserData, AuthContextType, ResponseData } from '@/model';
import { localStorageKey } from '@/constant/config';

// @ts-ignore
const secret_key = ENCRYPTION_SECRET_KEY;

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const obfuscate = async (data: string): Promise<string> => {
  try {
    const encoder = new TextEncoder();
    const keyMaterial = await crypto.subtle.importKey('raw', encoder.encode(secret_key), { name: 'AES-GCM' }, false, ['encrypt']);

    // Generate IV (Initialization Vector)
    const iv = crypto.getRandomValues(new Uint8Array(12));

    // Encrypt
    const ciphertext = await crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv: iv,
      },
      keyMaterial,
      encoder.encode(data),
    );

    // Combine IV and ciphertext
    const result = new Uint8Array(iv.length + ciphertext.byteLength);
    result.set(iv, 0);
    result.set(new Uint8Array(ciphertext), iv.length);

    return btoa(String.fromCharCode(...result));
  } catch (error) {
    console.error('Obfuscation error:', error);
    // Fallback to simple base64 encoding
    return btoa(encodeURIComponent(data + Date.now().toString().slice(-4)));
  }
};

const deobfuscate = async (encryptedData: string): Promise<string | null> => {
  try {
    const encoder = new TextEncoder();
    const keyMaterial = await crypto.subtle.importKey('raw', encoder.encode(secret_key), { name: 'AES-GCM' }, false, ['decrypt']);

    // Decode base64
    const data = Uint8Array.from(atob(encryptedData), (c) => c.charCodeAt(0));

    // Extract IV and ciphertext
    const iv = data.slice(0, 12);
    const encrypted = data.slice(12);

    // Decrypt
    const plaintext = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv: iv,
      },
      keyMaterial,
      encrypted,
    );

    return new TextDecoder().decode(plaintext);
  } catch (error) {
    // Fallback to simple base64 decoding
    try {
      const decoded = decodeURIComponent(atob(encryptedData));
      return decoded.slice(0, -4); // Remove timestamp
    } catch (fallbackError) {
      console.error('Deobfuscation error:', error, fallbackError);
      return null;
    }
  }
};

// Generate a verification hash for roles
const generateRoleHash = (authorities: Authority[]): string => {
  const roleString = authorities
    .map((auth) => auth.authority)
    .sort()
    .join('|');
  return btoa(roleString + window.location.hostname);
};

// Verify role hash integrity
const verifyRoleHash = (authorities: Authority[], hash: string): boolean => {
  return generateRoleHash(authorities) === hash;
};

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [messageApi, contextHolder] = message.useMessage();

  // Initialize auth state from secure storage
  useEffect(() => {
    const initAuth = async () => {
      try {
        const storedToken = localStorage.getItem(localStorageKey.AUTH_SESSION);
        const storedRoleHash = localStorage.getItem(localStorageKey.ROLE_HASH);

        if (storedToken && storedRoleHash) {
          const userData = await deobfuscate(storedToken);
          if (userData) {
            const parsedUser: UserData = JSON.parse(userData);

            // Verify role hash integrity
            if (verifyRoleHash(parsedUser.authorities, storedRoleHash)) {
              setUser(parsedUser);
            } else {
              // Hash mismatch - possible tampering
              clearAuthData();
              messageApi.warning('Session integrity check failed. Please login again.');
            }
          }
        } else {
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        clearAuthData();
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  const clearAuthData = () => {
    localStorage.removeItem(localStorageKey.AUTH_SESSION);
    localStorage.removeItem(localStorageKey.ROLE_HASH);
    localStorage.removeItem(localStorageKey.USERNAME); // Clear existing username storage
    setUser(null);
  };

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      setLoading(true);

      const response: ResponseData<UserData> = await goLogin({ username, password });

      if (response.code === 'A00000' && response.result) {
        const userData: UserData = response.result;

        // Store user data with obfuscation
        const obfuscatedData = await obfuscate(JSON.stringify(userData));
        const roleHash = generateRoleHash(userData.authorities);

        localStorage.setItem(localStorageKey.AUTH_SESSION, obfuscatedData);
        localStorage.setItem(localStorageKey.ROLE_HASH, roleHash);
        localStorage.setItem(localStorageKey.USERNAME, userData.username); // Keep for compatibility

        setUser(userData);

        messageApi.success('Login successful!');
        return true;
      } else {
        messageApi.error('Invalid credentials');
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      messageApi.error('Login failed. Please try again.');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await goLogout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      clearAuthData();
      messageApi.success('Logged out successfully');
    }
  };

  const hasRole = (role: string): boolean => {
    if (!user || !user.authorities) return false;

    // Re-verify role hash on each check to prevent tampering
    const storedRoleHash = localStorage.getItem(localStorageKey.ROLE_HASH);
    if (!storedRoleHash || !verifyRoleHash(user.authorities, storedRoleHash)) {
      clearAuthData();
      return false;
    }

    return user.authorities.some((auth) => auth.authority === role);
  };

  const isAuthenticated = !!user;

  const value: AuthContextType = {
    user,
    isAuthenticated,
    hasRole,
    login,
    logout,
    loading,
  };

  return (
    <AuthContext.Provider value={value}>
      {contextHolder}
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
