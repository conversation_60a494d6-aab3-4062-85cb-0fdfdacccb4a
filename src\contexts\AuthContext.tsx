import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { message } from 'antd';
import { goLogin, goLogout } from '@/api/api';
import { Authority, UserData, AuthContextType, ResponseData } from '@/model';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Secure token management with encryption-like obfuscation
const TOKEN_KEY = 'auth_session';
const ROLE_VERIFICATION_KEY = 'role_hash';

// Simple obfuscation functions (in production, use proper encryption)
const obfuscate = (data: string): string => {
  return btoa(encodeURIComponent(data + Date.now().toString().slice(-4)));
};

const deobfuscate = (data: string): string | null => {
  try {
    const decoded = decodeURIComponent(atob(data));
    return decoded.slice(0, -4); // Remove timestamp
  } catch {
    return null;
  }
};

// Generate a verification hash for roles
const generateRoleHash = (authorities: Authority[]): string => {
  const roleString = authorities
    .map((auth) => auth.authority)
    .sort()
    .join('|');
  return btoa(roleString + window.location.hostname);
};

// Verify role hash integrity
const verifyRoleHash = (authorities: Authority[], hash: string): boolean => {
  return generateRoleHash(authorities) === hash;
};

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  console.log("🚀 ~ AuthProvider ~ children:", children)
  const [user, setUser] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [messageApi, contextHolder] = message.useMessage();

  console.log("🚀 ~ initAuth ~ localStorage:", localStorage)
  // Initialize auth state from secure storage
  useEffect(() => {
    const initAuth = () => {
      try {
        const storedToken = localStorage.getItem(TOKEN_KEY);
        const storedRoleHash = localStorage.getItem(ROLE_VERIFICATION_KEY);

        if (storedToken && storedRoleHash) {
          const userData = deobfuscate(storedToken);
          if (userData) {
            const parsedUser: UserData = JSON.parse(userData);

            // Verify role hash integrity
            if (verifyRoleHash(parsedUser.authorities, storedRoleHash)) {
              setUser(parsedUser);
            } else {
              // Hash mismatch - possible tampering
              clearAuthData();
              messageApi.warning('Session integrity check failed. Please login again.');
            }
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        clearAuthData();
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  const clearAuthData = () => {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(ROLE_VERIFICATION_KEY);
    localStorage.removeItem('username'); // Clear existing username storage
    setUser(null);
  };

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      setLoading(true);
      const response: ResponseData<UserData> = await goLogin({ username, password });

      if (response.code === 'A00000' && response.result) {
        const userData: UserData = response.result;

        // Store user data with obfuscation
        const obfuscatedData = obfuscate(JSON.stringify(userData));
        const roleHash = generateRoleHash(userData.authorities);

        localStorage.setItem(TOKEN_KEY, obfuscatedData);
        localStorage.setItem(ROLE_VERIFICATION_KEY, roleHash);
        localStorage.setItem('username', userData.username); // Keep for compatibility

        setUser(userData);
        messageApi.success('Login successful!');
        return true;
      } else {
        messageApi.error('Invalid credentials');
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      messageApi.error('Login failed. Please try again.');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await goLogout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      clearAuthData();
      messageApi.success('Logged out successfully');
    }
  };

  const hasRole = (role: string): boolean => {
    if (!user || !user.authorities) return false;

    // Re-verify role hash on each check to prevent tampering
    const storedRoleHash = localStorage.getItem(ROLE_VERIFICATION_KEY);
    if (!storedRoleHash || !verifyRoleHash(user.authorities, storedRoleHash)) {
      clearAuthData();
      return false;
    }

    return user.authorities.some((auth) => auth.authority === role);
  };

  const isAuthenticated = !!user;

  const value: AuthContextType = {
    user,
    isAuthenticated,
    hasRole,
    login,
    logout,
    loading,
  };

  return (
    <AuthContext.Provider value={value}>
      {contextHolder}
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Role constants
export const ROLES = {
  MASTER: 'ROLE_MASTER',
  ADMIN: 'ROLE_ADMIN',
  USER: 'ROLE_USER',
} as const;
