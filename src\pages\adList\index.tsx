import { CountryList } from '@/constant/country';
import React, { Key, useEffect, useMemo, useState } from 'react';
import {
  Space,
  Table,
  Modal,
  Button,
  notification,
  Tag,
  Image,
  message,
  Typography,
  Switch,
  Tooltip,
  Input,
  Col,
  Select,
  DatePicker,
  Checkbox,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import {
  RedoOutlined,
  PlusOutlined,
  DeleteOutlined,
  FormOutlined,
  InfoCircleOutlined,
  StopOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import { PlatformEnum, PlatformList, statusFilterOption } from '@/constant';
import { debounce } from '@/utils/help';
import { history } from 'umi';
import dayjs from 'dayjs';

import './index.less';
import { dateTimeTagComponent } from '@/components/Common/CommonComponent';
import AdModal from '@/components/adListComponent/AdModal';
import { AdListDataType } from '@/model';
import { AdResourceHooks } from '@/hooks';
import BlockedCountryName from '@/components/CountryComponent/BlockedCountryName';

const { confirm } = Modal;
const { Text } = Typography;
const { fuzzySearchHook, getListHook, handleStartTimeChangeHook, handleDeleteHook, positionListHook, handleUpdateStatusHook } =
  AdResourceHooks;
const pageDisplayLabel = 'AD';

const AdList: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const [allRecord, setAllRecord] = useState<AdListDataType[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [statusUpdateLoad, setStatusUpdateLoad] = useState<{ [key: number]: boolean }>({});
  const [isModalVisible, setModalVisible] = useState(false);
  const [recordId, setRecordId] = useState<number | null>(null); // Store the id of record to be edited
  const [actionType, setActionType] = useState('');
  const [nameFilter, setNameFilter] = useState('');
  const [platformFilter, setPlatformFilter] = useState('');
  const [positionFilter, setPositionFilter] = useState<string[] | Key[]>([]);
  const [countryFilter, setCountryFilter] = useState<string[] | Key[]>([]);
  const [statusFilter, setStatusFilter] = useState<number[] | Key[]>([]);
  const [startTime, setStartTime] = useState<number | null>(-1);
  const [showCommonAd, setShowCommonAd] = useState<boolean>(false);
  const [pageNum, setPageNum] = useState(0);
  const [pageSize, setPageSize] = useState(15);
  const [total, setTotal] = useState(0);

  const platformArray = PlatformList.map((item) => ({ label: item.label, value: item.value }));
  platformArray.unshift({
    value: PlatformEnum.all,
    label: 'All Platform',
  });

  const positionArray = positionListHook({ platform: platformFilter });

  const hasSelected = useMemo(() => selectedRowKeys.length > 0, [selectedRowKeys]);
  const getList = getListHook({
    platform: platformFilter,
    position: positionFilter,
    country: countryFilter,
    status: statusFilter,
    commonAd: showCommonAd,
    startTime,
    setAllRecord,
    setLoading,
    messageApi,
  });

  // Reset page number when filters change
  useEffect(() => {
    setPageNum(0);
  }, [platformFilter, positionFilter, countryFilter, statusFilter, showCommonAd, startTime, nameFilter]);

  // initial load
  useEffect(() => {
    setLoading(true);
    getList();
  }, [platformFilter, positionFilter, countryFilter, statusFilter, showCommonAd, startTime]);

  const resetModal = () => {
    setModalVisible(false);
    setRecordId(null);
  };

  const onPageChange = (nextPageNumber: number, nextPageSize: number) => {
    if (pageSize !== nextPageSize) {
      setPageSize(nextPageSize);
      setPageNum(0);
    } else {
      setPageNum(nextPageNumber - 1);
    }
  };

  /**
   * lower threshold represents more `exact` search
   * total record is set through this hook
   * */
  const searchedData = fuzzySearchHook({
    allRecord,
    nameFilter,
    threshold: 0.1,
    keys: ['name'],
    setLoading,
    setTotal,
  });

  const handleDelete = (id: number) =>
    handleDeleteHook({
      confirm,
      id,
      selectedRowKeys,
      pageDisplayLabel,
      getList,
      messageApi,
    });

  const handleUpdateStatus = (dataToUpdate: { ids: number[]; newStatus: number }) =>
    handleUpdateStatusHook({
      confirm,
      dataToUpdate,
      pageDisplayLabel,
      setStatusUpdateLoad,
      getList,
      messageApi,
    });

  const columns: ColumnsType<AdListDataType> = useMemo(
    () => [
      {
        title: 'Id',
        dataIndex: 'id',
        key: 'id',
      },
      {
        title: 'Platform',
        dataIndex: 'platform',
        key: 'platform',
        render: (value) => {
          const tagColor = PlatformList.filter((item) => item.value === value)[0].color;
          return <Tag color={tagColor}>{PlatformList.filter((item) => item.value === value)[0].label}</Tag>;
        },
      },
      {
        title: 'Country',
        dataIndex: 'country',
        key: 'country',
        filterDropdown: ({ confirm, clearFilters }) => {
          const [searchText, setSearchText] = useState('');
          const [selectedValues, setSelectedValues] = useState(countryFilter || []);

          const filteredOptions = CountryList.filter((item) => item.label.toLowerCase().includes(searchText.toLowerCase()));

          // update the dropdown when the filter changes due to other component
          useEffect(() => {
            setSelectedValues(countryFilter || []);
          }, [countryFilter]);

          const handleSelect = (value: any) => {
            const newSelectedValues = selectedValues.includes(value)
              ? selectedValues.filter((v) => v !== value)
              : [...selectedValues, value];
            setSelectedValues(newSelectedValues);
          };

          const handleReset = () => {
            setSelectedValues([]);
            if (clearFilters) clearFilters();
          };

          const handleConfirm = () => {
            setCountryFilter(selectedValues);
            setSearchText('');
            confirm({ closeDropdown: true });
          };

          return (
            <div className="table-filter-dropdown">
              <Input
                placeholder="Search country..."
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                className="selected-filters"
                allowClear
              />

              {selectedValues.length > 0 && (
                <div className="selected-filters">
                  <div className="display-frame">
                    {selectedValues.map((value) => {
                      const selectedCountryFilter = CountryList.find((item) => item.value === value);
                      return (
                        <Tag key={value} closable onClose={() => handleSelect(value)}>
                          {selectedCountryFilter?.label}
                        </Tag>
                      );
                    })}
                  </div>
                </div>
              )}

              <div className="table-filter-options">
                {filteredOptions.length > 0 ? (
                  filteredOptions.map((item) => (
                    <div key={item.value} onClick={() => handleSelect(item.value)} className="table-filter-item">
                      <Checkbox checked={selectedValues.includes(item.value)} style={{ marginRight: 8 }} />
                      {item.label}
                    </div>
                  ))
                ) : (
                  <div className="filter-not-found">No countries found</div>
                )}
              </div>

              <div className="action-buttons">
                <Button disabled={selectedValues.length === 0} onClick={handleReset} size="small" className="button-styling">
                  Reset
                </Button>
                <Button type="primary" onClick={handleConfirm} size="small" className="button-styling">
                  OK
                </Button>
              </div>
            </div>
          );
        },
        render: (countryCodes) => {
          return (
            <BlockedCountryName
              countryCodes={countryCodes}
              itemDisplayLimit={3}
              modalTitle="Displayed in Following Country ({0})"
            />
          );
        },
      },
      {
        title: 'Start Date (UTC)',
        dataIndex: 'startTime',
        key: 'startTime',
        align: 'center',
        sorter: (a: any, b: any) => {
          return a.startTime - b.startTime;
        },
        render: (value) => {
          if (!value) {
            return <Tag>-</Tag>;
          }
          return dateTimeTagComponent(value, 'green');
        },
        width: '10%',
      },
      {
        title: 'End Date (UTC)',
        dataIndex: 'endTime',
        key: 'endTime',
        align: 'center',
        sorter: (a: any, b: any) => {
          return a.endTime - b.endTime;
        },
        render: (value) => {
          if (!value) {
            return <Tag>-</Tag>;
          }
          return dateTimeTagComponent(value, 'red');
        },
        width: '10%',
      },
      {
        title: (
          <Col>
            Name
            <Input
              placeholder="Search..."
              onChange={debounce((e: any) => {
                setLoading(true);
                setNameFilter(e.target.value);
              }, 500)}
              className="search-input"
            />
          </Col>
        ),
        dataIndex: 'name',
        key: 'name',
        width: '15%',
      },
      {
        title: 'Image',
        dataIndex: 'imageUrl',
        key: 'imageUrl',
        render: (_, record) => (
          <div style={{ textAlign: 'center' }}>
            <Image style={{ maxWidth: 120, maxHeight: 40 }} src={record.imageUrl} />{' '}
          </div>
        ),
      },
      {
        title: 'Position',
        dataIndex: 'position',
        key: 'position',
        filters: positionArray,
        sorter: (a: any, b: any) => {
          return a.position.localeCompare(b.position);
        },
        filterDropdown: ({ confirm, clearFilters }) => {
          const [searchText, setSearchText] = useState('');
          const [selectedValues, setSelectedValues] = useState(positionFilter || []);

          useEffect(() => {
            setSelectedValues(positionFilter || []);
          }, [positionFilter]);

          const filteredOptions = positionArray.filter((item: any) => item.text.toLowerCase().includes(searchText.toLowerCase()));

          const handleSelect = (value: any) => {
            const newSelectedValues = selectedValues.includes(value)
              ? selectedValues.filter((v) => v !== value)
              : [...selectedValues, value];
            setSelectedValues(newSelectedValues);
          };

          const handleReset = () => {
            setSelectedValues([]);
            if (clearFilters) clearFilters();
          };

          const handleConfirm = () => {
            setPositionFilter(selectedValues);
            setSearchText('');
            confirm({ closeDropdown: true });
          };

          return (
            <div className="table-filter-dropdown">
              <Input
                placeholder={platformFilter === '' ? 'Select Platform First' : 'Search position...'}
                disabled={platformFilter === ''}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                className="selected-filters"
                allowClear
              />

              {selectedValues.length > 0 && (
                <div className="selected-filters">
                  <div className="display-frame">
                    {selectedValues.map((value) => {
                      const selectedPositionFilter = positionArray.find((item: { value: any }) => item.value === value);
                      return (
                        <Tag key={value} closable onClose={() => handleSelect(value)}>
                          {selectedPositionFilter?.text}
                        </Tag>
                      );
                    })}
                  </div>
                </div>
              )}

              <div className="table-filter-options">
                {filteredOptions.length > 0 ? (
                  filteredOptions.map((item: { text: string; value: string }) => (
                    <div key={item.value} onClick={() => handleSelect(item.value)} className="table-filter-item">
                      <Checkbox checked={selectedValues.includes(item.value)} style={{ marginRight: 8 }} />
                      {item.text}
                    </div>
                  ))
                ) : platformFilter === '' ? (
                  <div className="filter-not-found">Please Select A Platform Filter First!</div>
                ) : (
                  <div className="filter-not-found">No Position Found.</div>
                )}
              </div>

              <div className="action-buttons">
                <Button disabled={selectedValues.length === 0} onClick={handleReset} size="small" className="button-styling">
                  Reset
                </Button>
                <Button type="primary" onClick={handleConfirm} size="small" className="button-styling">
                  OK
                </Button>
              </div>
            </div>
          );
        },
      },
      {
        title: 'Modified At',
        dataIndex: 'modifiedAt',
        key: 'modifiedAt',
        align: 'center',
        sorter: (a: any, b: any) => {
          return a.modifiedAt - b.modifiedAt;
        },
        render: (value) => dateTimeTagComponent(value, ''),
      },
      {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        align: 'center',
        filterDropdown: ({ confirm, clearFilters }) => {
          const [selectedValues, setSelectedValues] = useState(statusFilter || []);

          const handleSelect = (value: any) => {
            const newSelectedValues = selectedValues.includes(value)
              ? selectedValues.filter((v) => v !== value)
              : [...selectedValues, value];
            setSelectedValues(newSelectedValues);
          };

          useEffect(() => {
            setSelectedValues(statusFilter || []);
          }, [statusFilter]);

          const handleReset = () => {
            setSelectedValues([]);
            if (clearFilters) clearFilters();
          };

          const handleConfirm = () => {
            setStatusFilter(selectedValues);
            confirm({ closeDropdown: true });
          };

          return (
            <div className="table-filter-dropdown status-filter-dropdown">
              {selectedValues.length > 0 && (
                <div className="selected-filters">
                  <div className="display-frame">
                    {selectedValues.map((value) => {
                      const selectedStatusFilter = statusFilterOption.find((item) => item.value === value);
                      return (
                        <Tag key={value} closable onClose={() => handleSelect(value)}>
                          {selectedStatusFilter?.label}
                        </Tag>
                      );
                    })}
                  </div>
                </div>
              )}

              <div className="table-filter-options">
                {statusFilterOption.map((item) => (
                  <div key={item.value} onClick={() => handleSelect(item.value)} className="table-filter-item">
                    <Checkbox checked={selectedValues.includes(item.value)} style={{ marginRight: 8 }} />
                    {item.label}
                  </div>
                ))}
              </div>

              <div className="action-buttons">
                <Button disabled={selectedValues.length === 0} onClick={handleReset} size="small" className="button-styling">
                  Reset
                </Button>
                <Button type="primary" onClick={handleConfirm} size="small" className="button-styling">
                  OK
                </Button>
              </div>
            </div>
          );
        },
        render: (value, record) => {
          const checkStatus: boolean = value === 1 ? true : false;
          return (
            <Switch
              checkedChildren="Enable"
              unCheckedChildren="Disable"
              loading={statusUpdateLoad[record.id]}
              checked={checkStatus}
              onClick={() => {
                handleUpdateStatus({ ids: [record.id], newStatus: value === 1 ? 0 : 1 });
              }}
            />
          );
        },
      },
      {
        title: 'Options',
        key: 'action',
        align: 'center',
        render: (_, record) => (
          <Space size="middle">
            <Tooltip title="EDIT">
              <FormOutlined
                className="form-outline"
                onClick={() => {
                  setActionType('update');
                  setRecordId(record.id);
                  setModalVisible(true);
                }}
              />
            </Tooltip>
            <Tooltip title="DELETE">
              <DeleteOutlined
                className="form-outline"
                onClick={() => {
                  handleDelete(record.id);
                }}
              />
            </Tooltip>
          </Space>
        ),
      },
    ],
    [allRecord, statusUpdateLoad],
  );

  return (
    <div>
      {contextHolder}
      <div className="page-styling">
        <Space wrap style={{ marginBottom: 16 }}>
          <Button
            type="primary"
            onClick={() => {
              setActionType('create');
              setModalVisible(true);
            }}
            icon={<PlusOutlined />}
          >
            {' '}
            Create
          </Button>
          <Select
            value={platformFilter}
            style={{ width: 150 }}
            onChange={(value) => {
              setPlatformFilter(value);
              setPositionFilter([]);
              setCountryFilter([]);
              setStatusFilter([]);
            }}
            options={platformArray}
          />
          <DatePicker
            onChange={(date, dateString) => handleStartTimeChangeHook({ date: date, setStartTime })}
            format="YYYY-MM-DD"
            placeholder="Display Time Filter..."
            style={{ width: 200 }}
            disabled={showCommonAd}
            value={startTime === null || startTime < 0 ? undefined : dayjs.unix(startTime)}
          />
          <Button type="primary" icon={<RedoOutlined />} onClick={getList}>
            Refresh
          </Button>
          <Button type="primary" icon={<DeleteOutlined />} disabled={!hasSelected} danger onClick={() => handleDelete(-1)}>
            Delete
          </Button>
          <Button
            type="primary"
            icon={<CheckCircleOutlined />}
            disabled={!hasSelected}
            onClick={() => handleUpdateStatus({ ids: selectedRowKeys, newStatus: 1 })}
          >
            Enable
          </Button>
          <Button
            type="primary"
            disabled={!hasSelected}
            icon={<StopOutlined />}
            onClick={() => handleUpdateStatus({ ids: selectedRowKeys, newStatus: 0 })}
          >
            Disable
          </Button>
        </Space>
        <Switch
          checkedChildren="Show Common Ad(s)"
          unCheckedChildren="Show All Ad(s)"
          checked={showCommonAd}
          onChange={(checked) => {
            setStartTime(-1);
            setShowCommonAd(checked);
          }}
          className="show-common-ad-switch"
        />
        <div className="additional-note">
          <Tooltip title="Note">
            <InfoCircleOutlined className="label" />
          </Tooltip>
          <Text className="text">
            Ad(s) that does not have "Start Date" and "End Date" represents a "Common Ad" that displays at all times. (If there no
            other Ad(s) to be displayed)
          </Text>
        </div>{' '}
      </div>
      <Table
        rowKey="id"
        loading={loading}
        columns={columns}
        dataSource={searchedData}
        rowSelection={{
          selectedRowKeys,
          onChange: (newSelectedRowKeys: any) => setSelectedRowKeys(newSelectedRowKeys),
        }}
        pagination={{
          total,
          pageSize,
          defaultPageSize: 15,
          pageSizeOptions: [15, 30, 60],
          showSizeChanger: true,
          current: pageNum + 1,
          showQuickJumper: true,
          onChange: onPageChange,
          showTotal: (total, range) => (
            <text>
              Showing <b>{range[0]}-{range[1]}</b> of <b>{total}</b>
            </text>
          ),
        }}
      />
      <AdModal
        isModalVisible={isModalVisible}
        onCancel={resetModal}
        resetModal={() => {
          resetModal();
          getList();
        }}
        actionType={actionType}
        recordId={recordId}
        allRecord={allRecord}
      />
    </div>
  );
};

export default AdList;
