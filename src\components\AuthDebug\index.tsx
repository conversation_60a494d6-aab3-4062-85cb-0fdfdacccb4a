import React from 'react';
import { Card, Typography, Tag, Space } from 'antd';
import { useAuth } from '@/contexts/AuthContext';
import { accessRole, localStorageKey } from '@/constant';

const { Text, Title } = Typography;

const AuthDebug: React.FC = () => {
  const { user, isAuthenticated, hasRole, loading } = useAuth();

  return (
    <Card title="Authentication Debug" style={{ margin: '16px' }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Text strong>Loading: </Text>
          <Tag color={loading ? 'orange' : 'green'}>{loading ? 'Yes' : 'No'}</Tag>
        </div>
        
        <div>
          <Text strong>Is Authenticated: </Text>
          <Tag color={isAuthenticated ? 'green' : 'red'}>{isAuthenticated ? 'Yes' : 'No'}</Tag>
        </div>
        
        <div>
          <Text strong>User: </Text>
          <Text code>{user ? JSON.stringify(user, null, 2) : 'null'}</Text>
        </div>
        
        <div>
          <Text strong>Has ROLE_MASTER: </Text>
          <Tag color={hasRole(accessRole.MASTER) ? 'green' : 'red'}>
            {hasRole(accessRole.MASTER) ? 'Yes' : 'No'}
          </Tag>
        </div>
        
        <div>
          <Text strong>Has ROLE_ADMIN: </Text>
          <Tag color={hasRole(accessRole.ADMIN) ? 'green' : 'red'}>
            {hasRole(accessRole.ADMIN) ? 'Yes' : 'No'}
          </Tag>
        </div>
        
        <div>
          <Text strong>Has ROLE_USER: </Text>
          <Tag color={hasRole(accessRole.USER) ? 'green' : 'red'}>
            {hasRole(accessRole.USER) ? 'Yes' : 'No'}
          </Tag>
        </div>
        
        <div>
          <Text strong>LocalStorage auth_session: </Text>
          <Text code>{localStorage.getItem(localStorageKey.AUTH_SESSION) ? 'Present' : 'Not found'}</Text>
        </div>
        
        <div>
          <Text strong>LocalStorage role_hash: </Text>
          <Text code>{localStorage.getItem(localStorageKey.ROLE_HASH) ? 'Present' : 'Not found'}</Text>
        </div>
        
        <div>
          <Text strong>LocalStorage username: </Text>
          <Text code>{localStorage.getItem(localStorageKey.USERNAME) || 'Not found'}</Text>
        </div>
      </Space>
    </Card>
  );
};

export default AuthDebug;
