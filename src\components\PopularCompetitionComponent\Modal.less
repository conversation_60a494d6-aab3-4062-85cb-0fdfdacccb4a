// override ant form.item wrapper class
.ant-form-item {
  margin-bottom: 35px;
}

.custom-disabled-select.ant-select-disabled .ant-select-selector {
  background-color: #fff !important; /* Keep the background white */
  color: #000 !important; /* Set text color to black */
  // opacity: 1 !important; /* Remove the opacity that greys out the text */
  // border-color: #d9d9d9 !important; /* Optional: Keep the default border color */
  // cursor: not-allowed; /* Optional: Show a disabled cursor */
}

.custom-disabled-input.ant-input-disabled {
  background-color: #fff !important; /* Keep the background white */
  color: #000 !important; /* Set text color to black */
}

.custom-disabled-select.ant-select-disabled .ant-select-arrow {
  display: none; /* Hide the dropdown arrow if needed */
}

.site-input {
  .ant-input-clear-icon {
    font-size: 20px;
  }
}

.ant-input-number {
  width: 30% !important;
}

.form-styling {
  margin-bottom: 10px;
}