// @ts-nocheck
import React, { useEffect, useState } from 'react';
import { Modal, Input, Divider, Steps, Button, InputProps, Typography, message } from 'antd';
import { BlockCountriesContent } from './CountryComponent/BlockCountries';
import { getCompIdFromUrl, getGameType, urlStringCheck, debounce } from '@/utils/help';
import { banCompetitionVideo } from '@/api';
import { LinkOutlined } from '@ant-design/icons';
import { VideoCompetitionBlacklistStep, websiteUrlRegex } from '@/constant/config';

const { Text } = Typography;
const { confirm } = Modal;

/**
 * Competition URL Example
 * https://www.igscore.net/football-competition/gpxwrxlhgpryk0j/Overview/
 * */
const BlockCompetitionSteps: React.FC = (props) => {
  const { onCancel, onSuccess = null, isBlockCountriesStepVisible } = props;
  const [messageApi, contextHolder] = message.useMessage();
  const [compUrlStatus, setCompUrlStatus] = useState<string>('');
  const [compLink, setCompLink] = useState('');
  const [current, setCurrent] = useState(0);
  const [countries, setCountries] = useState([]);

  //reset state
  useEffect(() => {
    if (!isBlockCountriesStepVisible) {
      setCountries([]);
      setCompLink('');
      setCurrent(0);
    }
  }, [isBlockCountriesStepVisible]);

  const onSelectChange = (list) => {
    setCountries(list);
  };

  const compLinkOnChange: InputProps['onChange'] = debounce((e) => {
    const compLink = e.target.value;

    // competition link checking
    if (urlStringCheck(compLink, websiteUrlRegex) && compLink.indexOf('competition') > -1) {
      setCompUrlStatus('');
      setCompLink(e.target.value);
    } else {
      setCompUrlStatus('error');
    }
  }, 300);

  const navigate = (e: string) => {
    return () => {
      if (e === 'next') {
        setCurrent(current + 1);
      } else {
        setCurrent(current - 1);
      }
    };
  };

  const addBlocks = () => {
    confirm({
      title: `Confirmation`,
      content: 'Confirm Create New Video Live Stream Blacklist?',
      onOk() {
        const videoCompetitionBanList = [
          {
            competitionId: getCompIdFromUrl(compLink),
            countryCodes: countries,
            sportType: getGameType(compLink),
          },
        ];
        banCompetitionVideo(videoCompetitionBanList, true)
          .then((d) => {
            if (d.code === 'A00000') {
              messageApi.open({
                type: 'success',
                content: 'Create new live stream blacklist for competition/league success!',
              });
              if (onSuccess) onSuccess();
              onCancel();
            } else {
              messageApi.open({
                type: 'error',
                content: d.message,
              });
            }
          })
          .catch(() => {
            messageApi.open({
              type: 'error',
              content: 'Failed to create new live stream blacklist for competition/league!',
            });
          });
      },
      onCancel() {},
      maskClosable: true,
    });
  };

  return (
    <Modal
      title="Block Competition"
      footer={[]}
      open={isBlockCountriesStepVisible}
      onCancel={onCancel}
      width={current === 0 ? 600 : 1000}
      style={{ top: '50px' }}
    >
      {contextHolder}
      <Steps current={current} items={VideoCompetitionBlacklistStep} />
      <Divider style={{ margin: '12px 0' }} />
      <div style={{ height: '506px' }}>
        {current === 0 && (
          <div>
            <Input
              onChange={compLinkOnChange}
              style={{ marginBottom: '12px' }}
              size="large"
              placeholder="Competition Link"
              status={compUrlStatus}
              prefix={<LinkOutlined />}
              defaultValue={compLink}
              allowClear
            />
            {compUrlStatus === 'error' && <Text type="danger">Invalid Competition Link</Text>}
          </div>
        )}
        {current === 1 && <BlockCountriesContent onSelectChange={onSelectChange} />}
      </div>
      <div style={{ marginTop: 24 }}>
        <Button style={{ marginRight: '8px' }} onClick={onCancel}>
          Cancel
        </Button>
        {current < VideoCompetitionBlacklistStep.length - 1 && (
          <Button type="primary" disabled={!compLink || compUrlStatus === 'error'} onClick={navigate('next')}>
            Next
          </Button>
        )}
        {current === VideoCompetitionBlacklistStep.length - 1 && (
          <Button type="primary" onClick={addBlocks}>
            Done
          </Button>
        )}
        {current > 0 && (
          <Button style={{ margin: '0 8px' }} onClick={navigate('')}>
            Previous
          </Button>
        )}
      </div>
    </Modal>
  );
};

export default BlockCompetitionSteps;
