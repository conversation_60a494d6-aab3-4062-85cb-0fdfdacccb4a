import { useAuth } from '@/contexts/AuthContext';
import { MenuRoute } from '@/model';
import { accessRole } from '@/constant/config';

const baseMenu: MenuRoute[] = [
  {
    path: 'adminLog/',
    name: 'Admin Audit Log',
    requiredRole: accessRole.MASTER, // Only ROLE_MASTER can see this
    routes: [
      {
        path: 'list',
        name: 'Admin Audit Log List',
        requiredRole: accessRole.MASTER,
      },
    ],
  },
  {
    path: 'ad/',
    name: 'AD Manage',
    routes: [
      {
        path: 'list',
        name: 'AD List',
      },
    ],
  },
  {
    path: 'video/',
    name: 'Video Manage',
    routes: [
      {
        path: 'list',
        name: 'Video List',
      },
      {
        path: 'blacklist',
        name: 'Competition Blacklist',
      },
      {
        path: 'countrysetting',
        name: 'Country Setting',
      },
    ],
  },
  {
    path: 'matching/',
    name: 'Crawler Manager',
    routes: [
      {
        path: 'rules',
        name: 'Matching Rules',
      },
    ],
  },
  {
    path: 'setting/',
    name: 'Site Settings',
    routes: [
      {
        path: 'featured',
        name: 'Featured Section Settings',
      },
      {
        path: 'popularcompetition',
        name: 'Popular Competition Settings',
      },
      {
        path: 'displayconfig',
        name: 'Display Config',
      },
      {
        path: 'mobileappdetail',
        name: 'Mobile App Detail',
      },
    ],
  },
];

const filterMenuByRole = (menu: MenuRoute[], hasRole: (role: string) => boolean): MenuRoute[] => {
  return menu
    .filter((item) => {
      // If item has a required role, check if user has it
      if (item.requiredRole) {
        return hasRole(item.requiredRole);
      }
      return true;
    })
    .map((item) => ({
      ...item,
      routes: item.routes ? filterMenuByRole(item.routes, hasRole) : undefined,
    }))
    .filter((item) => {
      // Remove parent items that have no accessible child routes
      if (item.routes) {
        return item.routes.length > 0;
      }
      return true;
    });
};

export const useDynamicMenu = () => {
  const { hasRole, isAuthenticated, user } = useAuth();

  
  

  if (!isAuthenticated) {
    
    return { routes: [] };
  }

  const filteredMenu = filterMenuByRole(baseMenu, hasRole);
  

  return {
    routes: filteredMenu,
  };
};

// For backward compatibility with existing menu structure
export const getDynamicMenu = (hasRole: (role: string) => boolean) => {
  const filteredMenu = filterMenuByRole(baseMenu, hasRole);
  return {
    routes: filteredMenu,
  };
};
