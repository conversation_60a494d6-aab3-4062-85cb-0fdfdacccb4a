import { Form, Input, Modal, Select } from 'antd';
import { useState } from 'react';
import { SportTypeNameEnum } from '@/constant';

const sportsArray = Object.keys(SportTypeNameEnum).map((key) => {
  return {
    value: key,
    // @ts-ignore
    label: SportTypeNameEnum[key],
  };
});

export default function EditVideo(props: any) {
  const { item, onConfirm, onCancel } = props;

  const { igScoreName, liveStreamName, sportType, id, mappingType } = item;

  const [liveName, setLiveName] = useState(liveStreamName);
  const [igName, setIGName] = useState(igScoreName);
  const [type, setSportType] = useState(sportType.toString());

  const handleChangeSport= (v: string) => {
    setSportType(v);
  }

  const ok = () => {
    onConfirm({
      "id": id,
      "igScoreName": igName,
      "liveStreamName": liveName,
      "sportType": type,
      "mappingType": mappingType // Competition/Team
    });
  };

  return (
    <Modal
          title={`Create ${mappingType} stream`}
          style={{ top: '100px'}}
          width={700}
          open={true}
          onOk={ok}
          okButtonProps={{
            disabled: !liveName || !igName,
          }}
          onCancel={onCancel}
        >
          <Form name="basic" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ maxWidth: 600 }} autoComplete="off">
            <Form.Item
              label="Sport Type"
              name="type"
            >
              <Select
                defaultValue={type}
                style={{ width: 120 }}
                onChange={handleChangeSport}
                options={sportsArray}
              />
            </Form.Item>
            <Form.Item
              label="Live Stream Competition Name"
              name="game"
              rules={[{ required: true, message: 'Please input Live Stream Competition Name!' }]}
            >
              <Input defaultValue={liveName} placeholder="Please input Live Stream Competition Name!" onChange={(e) => setLiveName(e.target.value)} />
            </Form.Item>

            <Form.Item
              label={`IG Score ${mappingType} Name`}
              name="video"
              rules={[{ required: true, message: `Please input IG Score ${mappingType} Name!` }]}
            >
              <div className="videoDemo">
                <Input defaultValue={igName} placeholder={`Please input IG Score ${mappingType} Name!`} onChange={(e) => setIGName(e.target.value)} />
              </div>
            </Form.Item>
          </Form>
        </Modal>
  );
}
