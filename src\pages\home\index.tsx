import React, { useEffect, useMemo, useState } from 'react';
import { Typography } from 'antd';
import { confirmUser } from '@/api';

import './index.less';
import { PageContainer } from '@ant-design/pro-components';
import { history } from 'umi';

const { Title } = Typography;

const AdList: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const userVerification = () => {
    setLoading(true);
    confirmUser(localStorage.getItem('username') || '')
      .then((res: any) => {
        if (res?.code !== 'A00000') {
          history.push('/login');
        }
        setLoading(false);
      })
      .catch((e) => {
        localStorage.clear();
        history.push('/login');
        setLoading(false);
      });
  };

  useEffect(() => {
    userVerification();
  }, []);

  return (
    <PageContainer loading={loading} title={false}>
      <Title>Home Page</Title>
    </PageContainer>
  );
};

export default AdList;
