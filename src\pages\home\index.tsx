import React, { useEffect, useMemo, useState } from 'react';
import { Typography } from 'antd';
import { confirmUser } from '@/api';
import { localStorageKey } from '@/constant/config';

import './index.less';
import { PageContainer } from '@ant-design/pro-components';
import { history } from 'umi';
import AuthDebug from '@/components/AuthDebug';

const { Title } = Typography;

const AdList: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const userVerification = () => {
    setLoading(true);
    confirmUser(localStorage.getItem(localStorageKey.USERNAME) || '')
      .then((res: any) => {
        if (res?.code !== 'A00000') {
          history.push('/login');
        }
        setLoading(false);
      })
      .catch((e) => {
        localStorage.clear();
        history.push('/login');
        setLoading(false);
      });
  };

  useEffect(() => {
    userVerification();
  }, []);

  return (
    <PageContainer loading={loading} title={false}>
      <Title>Home Page</Title>
      {/* @ts-ignore */}
      {APP_ENV === 'development' && <AuthDebug />}
    </PageContainer>
  );
};

export default AdList;
