import React, { useEffect, useMemo, useState } from 'react';
import { Typography } from 'antd';
import { confirmUser } from '@/api';

import './index.less';
import { PageContainer } from '@ant-design/pro-components';

const { Title } = Typography;

const AdList: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const userVerification = () => {
    setLoading(true);
    confirmUser(localStorage.getItem('username') || '')
      .then(() => setLoading(false))
      .catch(() => setLoading(false));
  };

  useEffect(() => {
    userVerification();
  }, []);

  return (
    <PageContainer loading={loading} title={false}>
      <Title>Home Page</Title>
    </PageContainer>
  );
};

export default AdList;
