// @ts-nocheck121
import React, { useState } from 'react';
import { UploadOutlined, InfoCircleOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import xlsx from 'xlsx';
import { Button, message, Upload, Table, Modal, Popover } from 'antd';
import { getCompIdFromUrl, getGameIdFromUrl, getVideoParamsFromUrl } from '@/utils/help';
import videoDemo from '../../public/img/video.demo.png';
import compDemo from '../../public/img/compDemo.png';

function upload(file){
  return new Promise(resolve=>{
      let reader = new FileReader()
      reader.readAsBinaryString(file);
      reader.onload = ev=>{
          resolve(ev.target.result)
      }
  })
}

const getIdMap = {
  gamelink: getGameIdFromUrl,
  videolink: (str: string) => {
    return getVideoParamsFromUrl(str).ch
  },
  complink: getCompIdFromUrl
}

const UploadFile: React.FC = (props: any) => {
  const {columns, onConfirm, isVideo = false} = props
  const [list, setList] = useState([])
  const [open, setOpen] = useState(false)
  const columnsList = columns.map(item => {
    return {
      title: item,
      dataIndex: item.toLocaleLowerCase(),
      key: item.toLocaleLowerCase()
    }
  })
  const uploadProps: UploadProps = {
    async customRequest(info) {
      console.info(info)
      let file = info.file;
      if (!file) {
        message.error({
          title: 'read file error, please try again or change a file'
        })
        return
      };
      
      let reader = await upload(file);
      const worker = xlsx.read(reader, { type: 'binary' });
      let arr = [];
      worker.SheetNames.forEach((el) => {
        let temp = xlsx.utils.sheet_to_json(worker.Sheets[el]);
        temp.forEach((item) => {
          Object.keys(item).forEach((el) => {
            const key = el.toLocaleLowerCase()
            const val = String(item[el])
            item[key] = val
          });
        });
        arr = arr.concat(temp);
      });
      arr = arr.filter(item => {
        return columns.filter(c => item[c.toLocaleLowerCase()]).length === columns.length
      })
      setList(arr)
      setOpen(true)
    },
    showUploadList: false
  };

  const confirm = () => {
    setOpen(false)
    onConfirm(list)
  }
  
  return (
    <div>
      <div style={{position: 'relative'}}>
        <Upload {...uploadProps}>
          <Button icon={<UploadOutlined />}>Upload</Button>
        </Upload>
        <div style={{position: 'absolute', right: '-5px', top: '-5px'}}>
          <Popover
            content={(
              <img src={ isVideo ? videoDemo : compDemo}
              style={{width: '500px', height: 'auto'}}/>
            )} title="" arrow={false}>
            <InfoCircleOutlined style={{background: '#fff'}}/>
          </Popover>
        </div>
      </div>
      {
          open && (
            <Modal
              title="Upload Result"
              open={true}
              onOk={confirm}
              style={{width: '700px'}}
              onCancel={() => setOpen(false)}
          >
            <Table
              style={{maxHeight: '500px', width: '100%', overflow: 'hidden scroll'}}
              dataSource={list} columns={columnsList}
              pagination={false}
              sticky={true}
              ellipsis={true}
              />
          </Modal>
          )
      }
    </div>
  )
}

export default UploadFile