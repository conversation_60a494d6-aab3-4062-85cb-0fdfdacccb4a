// @ts-nocheck
// noinspection JSUnresolvedReference

import React, { useEffect, useState } from 'react';
import { Space, Table, Modal, Button, Select, Input, Form } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { EditOutlined, DeleteOutlined, PlusOutlined, RedoOutlined } from '@ant-design/icons';
import { createRule, deleteRule, getMatchingList, updateRule } from '@/api';
import { SportTypeNameEnum } from '@/constant';
import './index.less';
import EditRules from '@/components/EditRules';

const { error, confirm } = Modal;

const sportsArray = Object.keys(SportTypeNameEnum).map((key) => {
  return {
    value: key,
    // @ts-ignore
    label: SportTypeNameEnum[key],
  };
});

interface DataType {
  awayTeamId: string;
  awayTeamName: string;
  competitionId: string;
  competitionName: string;
  countryCodes: string[];
  homeTeamId: string;
  homeTeamName: string;
  id: any;
  matchId: string;
  matchTime: number;
  sportType: number;
  statusId: number;
  videoId: string;
}

interface Props {
  mappingType?: 'League' | 'Team';
}

const MatchRules = React.memo<Props>(({ mappingType = 'League' }) => {
  const [list, setList] = useState<DataType[]>([]);
  const [editItem, setEditItem] = useState<DataType>(null);
  const [loading, setLoading] = useState(true);
  const [sportType, setSportType] = useState(sportsArray[0].value);
  const [pageNum, setPageNum] = useState(0);
  const [pageSize, setPageSize] = useState(50);
  const [open, setOpen] = useState(false);
  const [leagueName, setLiveName] = useState('');
  const [igLeagueName, setIgName] = useState('');
  const [total, setTotal] = useState(0);

  const onPageChange = (nextPageNumber, nextPageSize) => {
    if (pageSize !== nextPageSize) {
      setPageSize(nextPageSize);
      setPageNum(0);
    } else {
      setPageNum(nextPageNumber - 1);
    }
  };

  const getList = () => {
    setLoading(true);
    getMatchingList({
      pageNum,
      pageSize,
      mappingType,
    })
      .then((d: any) => {
        setList(d.result.nameMappingRules);
        setTotal(d.result.total);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getList();
  }, [pageSize, pageNum]);

  const handleChangeSport = (v: string) => {
    setSportType(v);
  };

  const uploadRule = (list) => {
    createRule(list).then((d) => {
      if (d.code === 'A00000') {
        setOpen(false);
        getList();
      } else {
        error({
          title: d.message,
        });
      }
    });
  };

  const upDateRule = (list) => {
    updateRule(list).then((d) => {
      if (d.code === 'A00000') {
        setOpen(false);
        getList();
      } else {
        error({
          title: d.message,
        });
      }
    });
  };

  const addRule = () => {
    if (!leagueName || !igLeagueName) return false;
    uploadRule([
      {
        igScoreName: igLeagueName,
        liveStreamName: leagueName,
        sportType: sportType,
        mappingType: mappingType,
      },
    ]);
    setLiveName('');
    setIgName('');
  };

  const deleteItem = (item: DataType) => {
    confirm({
      title: 'Are you sure to delete this item',
      type: 'warn',
      onOk: () => {
        const { id } = item;
        deleteRule([
          {
            id,
          },
        ]).then((d) => {
          if (d.code === 'A00000') {
            getList();
          } else {
            error({
              title: d.message,
            });
          }
        });
      },
    });
  };

  const columns: ColumnsType<DataType> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: 'Sport Type',
      dataIndex: 'sportType',
      key: 'sportType',
      filters: Object.values(SportTypeNameEnum).map((v) => ({ text: v, value: v })),
      onFilter: (value: string, record) => {
        return SportTypeNameEnum[record.sportType] === value;
      },
      render: (sportType) => <span>{SportTypeNameEnum[sportType]}</span>,
    },
    {
      title: `Live Stream ${mappingType} Name`,
      dataIndex: 'liveStreamName',
      key: 'liveStreamName',
    },
    {
      title: `IG Score ${mappingType} Name`,
      dataIndex: 'igScoreName',
      key: 'igScoreName',
    },
    {
      title: 'Options',
      key: 'options',
      render: (_, record) => (
        <Space size="small">
          <Button size="small" onClick={() => setEditItem(record)}>
            <EditOutlined />
            Edit
          </Button>
          <Button size="small" danger onClick={() => deleteItem(record)}>
            <DeleteOutlined />
            Delete
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Space wrap style={{ marginBottom: 16 }}>
        <Button type="primary" onClick={() => setOpen(true)}>
          <PlusOutlined />
          Create
        </Button>
        <Button type="default" onClick={() => getList()}>
          <RedoOutlined />
          Refresh
        </Button>
      </Space>
      <Table
        rowKey="matchId"
        size="small"
        loading={loading}
        columns={columns}
        dataSource={list}
        pagination={{
          total,
          pageSize,
          defaultPageSize: 50,
          pageSizeOptions: [20, 50, 100],
          showSizeChanger: true,
          current: pageNum + 1,
          showQuickJumper: true,
          onChange: onPageChange,
          showTotal: (total, range) => (
            <text>
              Showing <b>{range[0]}-{range[1]}</b> of <b>{total}</b>
            </text>
          ),
        }}
      />
      {open && (
        <Modal
          title={`Create ${mappingType} stream`}
          style={{ top: '100px' }}
          width={700}
          open={true}
          onOk={addRule}
          okButtonProps={{
            disabled: !leagueName || !igLeagueName,
          }}
          onCancel={() => setOpen(false)}
        >
          <Form name="basic" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ maxWidth: 600 }} autoComplete="off">
            <Form.Item label="Sport Type" name="type">
              <Select defaultValue={sportType} style={{ width: 120 }} onChange={handleChangeSport} options={sportsArray} />
            </Form.Item>
            <Form.Item
              label="Live Stream Competition Name"
              name="game"
              rules={[{ required: true, message: 'Please input Live Stream Competition Name!' }]}
            >
              <Input placeholder="Please input Live Stream Competition Name!" onChange={(e) => setLiveName(e.target.value)} />
            </Form.Item>

            <Form.Item
              label={`IG Score ${mappingType} Name`}
              name="video"
              rules={[{ required: true, message: `Please input IG Score ${mappingType} Name!` }]}
            >
              <div className="videoDemo">
                <Input placeholder={`Please input IG Score ${mappingType} Name!`} onChange={(e) => setIgName(e.target.value)} />
              </div>
            </Form.Item>
          </Form>
        </Modal>
      )}
      {editItem && (
        <EditRules
          item={editItem}
          onCancel={() => setEditItem(null)}
          onConfirm={(nextItem) => {
            upDateRule([nextItem]);
            setEditItem(null);
          }}
        />
      )}
    </div>
  );
});

export default MatchRules;
