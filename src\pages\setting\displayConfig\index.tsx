import React, { useEffect, useReducer, useState } from 'react';
import { Space, Modal, message, Typography, Switch, Spin } from 'antd';

import { DisplayConfigApi } from '@/api';
import { DisplayConfigConstant } from '@/constant';
import { DisplayConfigHooks } from '@/hooks';

import './index.less';

const { confirm } = Modal;
const { Text } = Typography;
const { DisplayConfigSettingOrder, DisplayConfigPlatform, DisplayConfigLabel, DisplayConfigLoadingState } = DisplayConfigConstant;
const { getDisplayConfigDataHook } = DisplayConfigHooks;
const { updateData } = DisplayConfigApi;

// reducer function to handle loading state of config that is being updated
const loadingStateReducer = (state: any, action: { platform: string; setting: string; status: boolean }): any => {
  const updatedLoadingState = {
    [action.setting]: {
      ...state[action.setting],
      loading: {
        ...state[action.setting].loading,
        [action.platform]: action.status,
      },
    },
  };
  return { ...state, ...updatedLoadingState };
};

const DisplayConfig: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<any>({});
  const [configLoadingState, configLoadingStateDispatch] = useReducer(loadingStateReducer, DisplayConfigLoadingState);

  const getDisplayConfigData = () =>
    getDisplayConfigDataHook({
      setData,
      setLoading,
      messageApi,
    });

  useEffect(() => {
    getDisplayConfigData();
  }, []);

  const changeConfigConfirm = (params: any) => {
    configLoadingStateDispatch({ platform: params.platform, setting: params.setting, status: true });
    confirm({
      title: `Confirmation`,
      content: (
        <Text>
          Update <b>{params.label}</b> to <b>{params.checked ? 'Visible' : 'Hidden'}</b> on <b>{params.platform.toUpperCase()}</b>
          ?
        </Text>
      ),
      onOk() {
        updateDisplayConfig(params);
      },
      onCancel() {
        configLoadingStateDispatch({ platform: params.platform, setting: params.setting, status: false });
      },
      maskClosable: true,
      width: 400,
    });
  };

  const updateDisplayConfig = (params: any) => {
    const newStatus: number = params.checked === false ? 0 : 1;
    //update status field with new status after confirm update action
    updateData({ platform: params.platform, configName: params.setting, status: newStatus })
      .then(() => {
        messageApi.open({
          type: 'success',
          content: (
            <Text>
              <b>{params.label}</b> for <b>{params.platform.toUpperCase()}</b> Updated Success!
            </Text>
          ),
        });
      })
      .catch(() => {
        messageApi.open({
          type: 'error',
          content: (
            <Text>
              <b>{params.label}</b> for <b>{params.platform.toUpperCase()}</b> Update Failed!
            </Text>
          ),
        });
      })
      .finally(() => {
        configLoadingStateDispatch({ platform: params.platform, setting: params.setting, status: false });
        getDisplayConfigData();
      });
  };

  return (
    <Spin size="large" spinning={loading}>
      {contextHolder}
      {DisplayConfigSettingOrder.map((setting) => (
        <div className="container">
          <Text className="sub-title-styling">
            {DisplayConfigLabel[setting].label || ''} {DisplayConfigLabel[setting].moreLabel || ''} (Platform)
          </Text>
          <Space className="switch-container">
            {DisplayConfigPlatform.map(
              (platform) =>
                data[setting] &&
                data[setting].hasOwnProperty(platform) && (
                  <div className="switch-unit">
                    <Text strong>{platform.toUpperCase()}:</Text>
                    <Switch
                      checked={data[setting][platform] === 1 ? true : false}
                      checkedChildren="Visible"
                      unCheckedChildren="Hidden"
                      loading={configLoadingState[setting].loading[platform]}
                      onClick={(checked) =>
                        changeConfigConfirm({ label: DisplayConfigLabel[setting].label, setting, platform, checked })
                      }
                    />
                  </div>
                ),
            )}
          </Space>
        </div>
      ))}
    </Spin>
  );
};

export default DisplayConfig;
