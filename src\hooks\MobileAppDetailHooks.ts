import { MobileAppDetailApi } from '@/api';

const { getData } = MobileAppDetailApi;

export const getMobileAppDetailDataHook = (props: any) => {
  const { setData, setLoading, messageApi } = props;
  setLoading(true);
  return getData()
    .then((res: any) => {
      setData(res.result);
    })
    .catch(() => {
      messageApi.error('Failed to Retrieve Data!');
    })
    .finally(() => {
      setLoading(false);
    });
};
