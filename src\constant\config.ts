import androidSidebarBottom from '../../public/img/androidSidebarBottom.jpg';
import iosLiveBottom from '../../public/img/iosLiveBottom.jpg';
import iosLiveMatch from '../../public/img/iosLiveMatch.jpg';
import iosMatchChatTab from '../../public/img/iosMatchChatTab.jpg';
import iosMatchOddsBottom from '../../public/img/iosMatchOddsBottom.jpg';
import iosMatchOverviewBottom from '../../public/img/iosMatchOverviewBottom.jpg';
import iosMatchOverviewTab from '../../public/img/iosMatchOverviewTab.jpg';
import iosOpenScreen from '../../public/img/iosOpenScreen.jpg';
import mobileCompetitionBanner from '../../public/img/mobile-competitionbanner.png';
import mobileGameBanner from '../../public/img/mobile-gamebanner.png';
import mobileHome from '../../public/img/mobileHome.png';
import mobileMatchBanner from '../../public/img/mobile-matchbanner.png';
import mobilePageBottom from '../../public/img/mobile-pagebottom.png';
import mobilePlayerBanner from '../../public/img/mobile-playerbanner.png';
import mobileTeamBanner from '../../public/img/mobile-teambanner.png';
import pcCompetitionBanner from '../../public/img/pc-competitionbanner.png';
import pcGameBanner from '../../public/img/pc-gamebanner.png';
import pcMatchBanner from '../../public/img/pc-matchbanner.png';
import pcPlayerBanner from '../../public/img/pc-playerbanner.png';
import pcSideBanner from '../../public/img/pc-sidebanner.png';
import pcTeamBanner from '../../public/img/pc-teambanner.png';
import webHome from '../../public/img/webHome.png';

enum PlatformEnum {
  web = 'web',
  mobile = 'mobile',
  ios = 'ios',
  android = 'android',
  all = '',
}

interface AdImageInfo {
  [platform: string]: {
    [position: string]: {
      // label: string;
      demoImg: string;
      width: number;
      height: number;
    };
  };
}

const PlatformList = [
  {
    value: PlatformEnum.web,
    label: 'PC Web',
    color: 'blue',
  },
  {
    value: PlatformEnum.mobile,
    label: 'Mobile Web',
    color: 'magenta',
  },
  {
    value: PlatformEnum.ios,
    label: 'iOS App',
    color: 'grey',
  },
  {
    value: PlatformEnum.android,
    label: 'Android App',
    color: 'green',
  },
];

const AdSettingInfoMaps: AdImageInfo = {
  [PlatformEnum.web]: {
    homepage: {
      // label: 'Homepage',
      demoImg: webHome,
      width: 942,
      height: 100,
    },
    sideBanner: {
      demoImg: pcSideBanner,
      width: 250,
      height: 250,
    },
    gameBanner: {
      demoImg: pcGameBanner,
      width: 912,
      height: 100,
    },
    matchBanner: {
      demoImg: pcMatchBanner,
      width: 1200,
      height: 100,
    },
    competitionBanner: {
      demoImg: pcCompetitionBanner,
      width: 1200,
      height: 100,
    },
    teamBanner: {
      demoImg: pcTeamBanner,
      width: 1200,
      height: 100,
    },
    playerBanner: {
      demoImg: pcPlayerBanner,
      width: 1200,
      height: 100,
    },
  },
  [PlatformEnum.mobile]: {
    homepage: {
      demoImg: mobileHome,
      width: 750,
      height: 88,
    },
    pageBottom: {
      demoImg: mobilePageBottom,
      width: 750,
      height: 88,
    },
    gameBanner: {
      demoImg: mobileGameBanner,
      width: 750,
      height: 88,
    },
    matchBanner: {
      demoImg: mobileMatchBanner,
      width: 750,
      height: 88,
    },
    competitionBanner: {
      demoImg: mobileCompetitionBanner,
      width: 750,
      height: 88,
    },
    teamBanner: {
      demoImg: mobileTeamBanner,
      width: 750,
      height: 88,
    },
    playerBanner: {
      demoImg: mobilePlayerBanner,
      width: 750,
      height: 88,
    },
  },
  [PlatformEnum.ios]: {
    openScreen: {
      demoImg: iosOpenScreen,
      width: 750,
      height: 1160,
    },
    liveMatch: {
      demoImg: iosLiveMatch,
      width: 750,
      height: 100,
    },
    liveBottom: {
      demoImg: iosLiveBottom,
      width: 750,
      height: 100,
    },
    // matchOverviewTab: {
    //   demoImg: iosMatchOverviewTab,
    //   width: 750,
    //   height: 56,
    // },
    // matchOverviewBottom: {
    //   demoImg: iosMatchOverviewBottom,
    //   width: 750,
    //   height: 88,
    // },
    // matchChatTab: {
    //   demoImg: iosMatchChatTab,
    //   width: 750,
    //   height: 88,
    // },
    // matchOddsBottom: {
    //   demoImg: iosMatchOddsBottom,
    //   width: 750,
    //   height: 88,
    // },
    // sidebarBottom: {
    //   demoImg: androidSidebarBottom,
    //   width: 512,
    //   height: 56,
    // },
    matchBanner: {
      demoImg: mobileMatchBanner,
      width: 750,
      height: 88,
    },
    playerBanner: {
      demoImg: mobilePlayerBanner,
      width: 750,
      height: 88,
    },
    competitionBanner: {
      demoImg: mobileCompetitionBanner,
      width: 750,
      height: 88,
    },
    teamBanner: {
      demoImg: mobileTeamBanner,
      width: 750,
      height: 88,
    },
  },
  [PlatformEnum.android]: {
    openScreen: {
      demoImg: iosOpenScreen,
      width: 750,
      height: 1160,
    },
    liveMatch: {
      demoImg: iosLiveMatch,
      width: 750,
      height: 100,
    },
    liveBottom: {
      demoImg: iosLiveBottom,
      width: 750,
      height: 100,
    },
    matchOverviewTab: {
      demoImg: iosMatchOverviewTab,
      width: 750,
      height: 56,
    },
    matchOverviewBottom: {
      demoImg: iosMatchOverviewBottom,
      width: 750,
      height: 88,
    },
    matchChatTab: {
      demoImg: iosMatchChatTab,
      width: 750,
      height: 88,
    },
    matchOddsBottom: {
      demoImg: iosMatchOddsBottom,
      width: 750,
      height: 88,
    },
    sidebarBottom: {
      demoImg: androidSidebarBottom,
      width: 512,
      height: 56,
    },
    matchBanner: {
      demoImg: mobileMatchBanner,
      width: 750,
      height: 88,
    },
    playerBanner: {
      demoImg: mobilePlayerBanner,
      width: 750,
      height: 88,
    },
    competitionBanner: {
      demoImg: mobileCompetitionBanner,
      width: 750,
      height: 88,
    },
    teamBanner: {
      demoImg: mobileTeamBanner,
      width: 750,
      height: 88,
    },
  },
};

const CreateErrorMessage: any = {
  imageUrl: 'Please input your AD Image Url!',
  width: 'Please input your AD Width!',
  height: 'Please input your AD Height!',
  title: 'Please input your AD Title!',
};

const GlobalSportPathnameSpellingStr = {
  football: 'Football',
  basketball: 'Basketball',
  tennis: 'Tennis',
  cricket: 'Cricket',
  baseball: 'Baseball',
  amfootball: 'AM Football',
  icehockey: 'Hockey',
  volleyball: 'Volleyball',
  esports: 'Esports',
  handball: 'Handball',
  waterpolo: 'Waterpolo',
  tabletennis: 'Table Tennis',
  snooker: 'Snooker',
  badminton: 'Badminton',
};

const NotStartStatus = 1;

const MatchRuleName = {
  Team: 'Team',
};

const VideoCompetitionBlacklistStep = [
  {
    key: 'Add Competition Link',
    title: 'Add Competition Link',
  },
  {
    key: 'Select Blocked Countries',
    title: 'Select Blocked Countries',
  },
];

const statusFilterOption = [
  { label: 'Enable', value: 1 },
  { label: 'Disable', value: 0 },
];

const igscoreSiteUrlRegex = '^https:.*.igscore.net/';

const igscoreSiteCompetitionUrlRegex = '^https:.*.igscore.net/.*-competition';

const websiteUrlRegex = /(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/g;

const fuseConfig = {
  threshold: 0.3,
  ignoreLocation: true,
};

export {
  PlatformEnum,
  PlatformList,
  AdSettingInfoMaps,
  CreateErrorMessage,
  GlobalSportPathnameSpellingStr,
  NotStartStatus,
  MatchRuleName,
  VideoCompetitionBlacklistStep,
  statusFilterOption,
  igscoreSiteUrlRegex,
  igscoreSiteCompetitionUrlRegex,
  websiteUrlRegex,
  fuseConfig,
};
