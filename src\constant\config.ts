import webHome from '../../public/img/webHome.png';
import mobileHome from '../../public/img/mobileHome.png';
import iosOpenScreen from '../../public/img/iosOpenScreen.jpg';
import iosLiveMatch from '../../public/img/iosLiveMatch.jpg';
import iosLiveBottom from '../../public/img/iosLiveBottom.jpg';
import iosMatchChatTab from '../../public/img/iosMatchChatTab.jpg';
import iosMatchOverviewTab from '../../public/img/iosMatchOverviewTab.jpg';
import iosMatchOverviewBottom from '../../public/img/iosMatchOverviewBottom.jpg';
import iosMatchOddsBottom from '../../public/img/iosMatchOddsBottom.jpg';
import androidSidebarBottom from '../../public/img/androidSidebarBottom.jpg';

enum PlatformEnum {
  web = 'web',
  mobile = 'mobile',
  ios = 'ios',
  android = 'android',
  all = '',
}

interface AdImageInfo {
  [platform: string]: {
    [position: string]: {
      demoImg: string;
      width: number;
      height: number;
    };
  };
}

const PlatformList = [
  {
    value: PlatformEnum.web,
    label: 'PC Web',
    color: 'blue',
  },
  {
    value: PlatformEnum.mobile,
    label: 'Mobile Web',
    color: 'magenta',
  },
  {
    value: PlatformEnum.ios,
    label: 'iOS App',
    color: 'grey',
  },
  {
    value: PlatformEnum.android,
    label: 'Android App',
    color: 'green',
  },
];

const AdSettingInfoMaps: AdImageInfo = {
  [PlatformEnum.web]: {
    homepage: {
      demoImg: webHome,
      width: 992,
      height: 100,
    },
    topBanner: {
      demoImg: webHome,
      width: 1200,
      height: 90,
    },
    sideBanner: {
      demoImg: webHome,
      width: 200,
      height: 200,
    },
    homepage_v2: {
      demoImg: webHome,
      width: 942,
      height: 100,
    },
  },
  [PlatformEnum.mobile]: {
    liveTop: {
      demoImg: mobileHome,
      width: 750,
      height: 88,
    },

    matchPageBottom: {
      demoImg: mobileHome,
      width: 750,
      height: 88,
    },
  },
  [PlatformEnum.ios]: {
    openScreen: {
      demoImg: iosOpenScreen,
      width: 750,
      height: 1160,
    },
    liveMatch: {
      demoImg: iosLiveMatch,
      width: 750,
      height: 100,
    },
    liveBottom: {
      demoImg: iosLiveBottom,
      width: 750,
      height: 100,
    },
    matchOverviewTab: {
      demoImg: iosMatchOverviewTab,
      width: 750,
      height: 56,
    },
    matchOverviewBottom: {
      demoImg: iosMatchOverviewBottom,
      width: 750,
      height: 88,
    },
    matchChatTab: {
      demoImg: iosMatchChatTab,
      width: 750,
      height: 88,
    },
    matchOddsBottom: {
      demoImg: iosMatchOddsBottom,
      width: 750,
      height: 88,
    },
    sidebarBottom: {
      demoImg: androidSidebarBottom,
      width: 512,
      height: 56,
    },
  },
  [PlatformEnum.android]: {
    openScreen: {
      demoImg: iosOpenScreen,
      width: 750,
      height: 1160,
    },
    liveMatch: {
      demoImg: iosLiveMatch,
      width: 750,
      height: 100,
    },
    liveBottom: {
      demoImg: iosLiveBottom,
      width: 750,
      height: 100,
    },
    matchOverviewTab: {
      demoImg: iosMatchOverviewTab,
      width: 750,
      height: 56,
    },
    matchOverviewBottom: {
      demoImg: iosMatchOverviewBottom,
      width: 750,
      height: 88,
    },
    matchChatTab: {
      demoImg: iosMatchChatTab,
      width: 750,
      height: 88,
    },
    matchOddsBottom: {
      demoImg: iosMatchOddsBottom,
      width: 750,
      height: 88,
    },
    sidebarBottom: {
      demoImg: androidSidebarBottom,
      width: 512,
      height: 56,
    },
  },
};

const CreateErrorMessage: any = {
  imageUrl: 'Please input your AD Image Url!',
  width: 'Please input your AD Width!',
  height: 'Please input your AD Height!',
  title: 'Please input your AD Title!',
};

const GlobalSportPathnameSpellingStr = {
  football: 'Football',
  basketball: 'Basketball',
  tennis: 'Tennis',
  cricket: 'Cricket',
  baseball: 'Baseball',
  amfootball: 'AM Football',
  icehockey: 'Hockey',
  volleyball: 'Volleyball',
  esports: 'Esports',
  handball: 'Handball',
  waterpolo: 'Waterpolo',
  tabletennis: 'Table Tennis',
  snooker: 'Snooker',
  badminton: 'Badminton',
};

const NotStartStatus = 1;

const MatchRuleName = {
  Team: 'Team',
};

const VideoCompetitionBlacklistStep = [
  {
    key: 'Add Competition Link',
    title: 'Add Competition Link',
  },
  {
    key: 'Select Blocked Countries',
    title: 'Select Blocked Countries',
  },
];

const statusFilterOption = [
  { label: 'Enable', value: 1 },
  { label: 'Disable', value: 0 },
];

const igscoreSiteUrlRegex = '^https:.*.igscore.net/';

const igscoreSiteCompetitionUrlRegex = '^https:.*.igscore.net/.*-competition';

const websiteUrlRegex = /(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/g;

const fuseConfig = {
  threshold: 0.3,
  ignoreLocation: true,
};

export {
  PlatformEnum,
  PlatformList,
  AdSettingInfoMaps,
  CreateErrorMessage,
  GlobalSportPathnameSpellingStr,
  NotStartStatus,
  MatchRuleName,
  VideoCompetitionBlacklistStep,
  statusFilterOption,
  igscoreSiteUrlRegex,
  igscoreSiteCompetitionUrlRegex,
  websiteUrlRegex,
  fuseConfig,
};
