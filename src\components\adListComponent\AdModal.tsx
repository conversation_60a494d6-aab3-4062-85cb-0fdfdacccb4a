import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Typography,
  Select,
  notification,
  Image,
  Skeleton,
  Upload,
  message,
  DatePicker,
  Button,
  Switch,
} from 'antd';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { AdListDataType } from '@/model';
import { PlatformList, websiteUrlRegex, CountryNameMap } from '@/constant';
import { AdResourceApi, uploadUrl } from '@/api';
import { debounce, generateUuid, urlStringCheck } from '@/utils/help';

import './AdModal.less';
import { AdResourceHooks } from '@/hooks';
import BlockCountries from '../CountryComponent/BlockCountries';
import dayjs from 'dayjs';

const { Text } = Typography;
const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { confirm } = Modal;
const { handleTimeChangeHook, modalResetHook, positionListHook, adSettingHook, handleFileUploadHook } = AdResourceHooks;
const { createData, updateData } = AdResourceApi;
const pageDisplayLabel = 'Ad';

interface UpdateAdModalProps {
  isModalVisible: boolean;
  onCancel: () => void;
  resetModal: () => void;
  actionType: string;
  recordId: number | null;
  allRecord: any;
}

const AdModal: React.FC<UpdateAdModalProps> = (props) => {
  const { isModalVisible, onCancel, resetModal, actionType, recordId, allRecord } = props;
  const [messageApi, contextHolder] = message.useMessage();
  const [form] = Form.useForm();

  const [countryModalVisible, setCountryModalVisible] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [recordData, setRecordData] = useState<AdListDataType | null>(null);
  const [newCountryList, setNewCountryList] = useState<string[]>([]);
  const [isCommonAd, setIsCommonAd] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>('');
  const [country, setCountry] = useState<string[]>([]);
  const [platform, setPlatform] = useState<string>('');
  const [position, setPosition] = useState<string>('');

  const delayInMS = 500;

  const adSetting: any = adSettingHook({ platform, position });
  const positionArray = positionListHook({ platform });

  useEffect(() => {
    if (recordData) {
      setImageUrl(recordData?.imageUrl || '');
      setCountry(recordData?.country || []);
      setNewCountryList(recordData?.country || []);
      setIsCommonAd(recordData?.commonAd || false);
      setPlatform(recordData?.platform || '');
      setPosition(recordData?.position || '');

      // configuring form data (if action is 'update')
      form.setFieldsValue(recordData);
      form.setFieldValue(
        'displayPeriod',
        recordData?.commonAd ? null : [dayjs.unix(recordData?.startTime), dayjs.unix(recordData?.endTime)],
      );
    }
  }, [recordData]);

  // reset state of modal
  modalResetHook({
    isModalVisible,
    form,
    recordId,
    setRecordData,
    setImageUrl,
    setCountry,
    setNewCountryList,
    setIsCommonAd,
    setPlatform,
    setPosition,
    messageApi,
  });

  const actionConfirm = (data: AdListDataType) => {
    confirm({
      title: `Confirmation`,
      content:
        actionType === 'create' ? (
          <Text>Confirm Create New {pageDisplayLabel}?</Text>
        ) : (
          <Text>Confirm Update Selected {pageDisplayLabel}?</Text>
        ),
      onOk() {
        actionResult(data);
      },
      onCancel() {},
      maskClosable: true,
    });
  };

  const actionResult = (data: AdListDataType) => {
    try {
      let result;
      if (actionType === 'create') {
        data.length = adSetting.height;
        data.width = adSetting.width;
        result = createData(data);
      } else {
        data.length = adSetting.height;
        data.width = adSetting.width;
        result = updateData(data);
      }
      notification.success({
        key: 'success',
        message: 'Success!',
        description:
          actionType === 'create' ? (
            <Text>New {pageDisplayLabel} Create Success!</Text>
          ) : (
            <Text>
              Update Success! {pageDisplayLabel} ID: <b> {recordData?.id || ''}</b>
            </Text>
          ),
      });
    } catch (error) {
      notification.error({
        key: 'error',
        message: 'Error!',
        description:
          actionType === 'create' ? (
            <Text>New {pageDisplayLabel} Create Failed!</Text>
          ) : (
            <Text>
              Update Failed! {pageDisplayLabel} ID: <b> {recordData?.id || ''}</b>
            </Text>
          ),
      });
    } finally {
      resetModal();
    }
  };

  const beforeFileUpload = useCallback((file: File) => {
    return Promise.resolve(
      new File([file], `${generateUuid('igad', 10)}_${file.name}`, {
        type: file.type,
      }),
    );
  }, []);

  const uploadButton = () =>
    imageUrl ? (
      // @ts-ignore
      <img src={imageUrl} referrer="no-referrer|origin|unsafe-url" alt="avatar" style={{ width: '100%' }} />
    ) : (
      <div>
        {loading ? <LoadingOutlined /> : <PlusOutlined />}
        <div style={{ marginTop: 8 }}>Upload</div>
      </div>
    );

  const duplicateValidation = debounce((rule: any, value: string, callback: any): string => {
    // `item.id !== recordId` will only be false if it is an "update" action
    const isNotUnique = allRecord.some(
      (item: any) =>
        item.id !== recordId &&
        item.name === value &&
        item.position === position &&
        item.platform === platform &&
        item.country === country,
    );
    if (isNotUnique) {
      return callback('Duplicate Name Found!');
    }
    return callback();
  }, delayInMS);

  const checkUrlString = debounce((rule: any, value: string, callback: any): string => {
    if (!urlStringCheck(value, new RegExp(websiteUrlRegex))) {
      return callback('Not a valid URL!');
    }
    return callback();
  }, delayInMS);

  return (
    <Modal
      title={
        actionType === 'create' ? `Create New ${pageDisplayLabel}` : `Update ${pageDisplayLabel} (ID: ${recordData?.id || ''})`
      }
      open={isModalVisible}
      onCancel={onCancel}
      onOk={() => form.submit()}
      width={700}
    >
      {contextHolder}
      <Form
        form={form}
        layout="horizontal"
        onFinish={actionConfirm}
        onFinishFailed={() =>
          messageApi.open({
            type: 'error',
            content: `Submission Error!`,
          })
        }
        scrollToFirstError
        initialValues={{ remember: true }}
        autoComplete="off"
        labelCol={{ span: 6 }}
        labelWrap={true}
        labelAlign="left"
        style={{ marginBottom: 10 }}
      >
        <Form.Item name="id" hidden />
        <Form.Item
          name="name"
          label="Name"
          hasFeedback
          rules={[{ required: true, message: 'Missing Name!' }, { validator: duplicateValidation }]}
        >
          <Input placeholder={`Please input the Name of Ad`} value={recordData?.name} />
        </Form.Item>
        <Form.Item name="country" label="Country" rules={[{ required: true, message: 'Missing Country!' }]}>
          <Input
            value={country
              .slice(0, 3)
              .map((item) => CountryNameMap[item])
              .join(', ')
              .concat(country.length > 3 ? '...' : '')}
            placeholder="Country Selection..."
            disabled
            className="custom-disabled-input"
          />
          <Button type="primary" onClick={() => setCountryModalVisible(true)} className="country-button-style">
            Configure Country
          </Button>
        </Form.Item>
        <Form.Item name="platform" label="Platform" rules={[{ required: true, message: 'Missing Platform!' }]}>
          <Select
            options={PlatformList}
            placeholder="Please Select Delivery Platform"
            filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
            onChange={(e) => {
              form.validateFields(['name']);
              form.setFieldsValue({ position: null }); // reset selected 'position' value
              setPosition('');
              setPlatform(e);
            }}
          />
        </Form.Item>
        <Form.Item name="position" label="Position" rules={[{ required: true, message: 'Missing Position!' }]}>
          <Select
            options={positionArray}
            value={position}
            placeholder="Please Select a Position"
            filterOption={(input, option: any) => (option?.text ?? '').toLowerCase().includes(input.toLowerCase())}
            onChange={(e) => {
              form.validateFields(['name']);
              setPosition(e);
            }}
          />
        </Form.Item>
        <Form.Item label="Display Period" required={!isCommonAd} className="display-period-section-style">
          <Input.Group compact>
            <Form.Item name="displayPeriod" rules={[{ required: !isCommonAd, message: 'Please select a time range!' }]}>
              <RangePicker
                format="YYYY-MM-DD"
                onChange={(dateTime) => handleTimeChangeHook({ form, dateTime })}
                placeholder={['Start Time', 'End Time']}
                allowClear
                disabled={isCommonAd}
                className="range-picker-additional-style"
              />
            </Form.Item>
            <Form.Item name="commonAd" valuePropName="commonAdChecked">
              <Switch
                unCheckedChildren="Not Common Ad"
                checkedChildren="Common Ad"
                className="is-common-ad-switch"
                checked={isCommonAd}
                onChange={(checked) => {
                  form.setFieldsValue({
                    startTime: null,
                    endTime: null,
                    displayPeriod: null,
                    commonAd: checked,
                  });
                  setIsCommonAd(checked);
                }}
              />
            </Form.Item>
          </Input.Group>
        </Form.Item>

        <Form.Item label="Preview">
          <Image style={{ maxWidth: 230, maxHeight: 120 }} src={adSetting?.demoImg || ''} />
          {(!adSetting || !adSetting.demoImg) && <Skeleton.Image style={{ maxWidth: 230, maxHeight: 120 }} />}
        </Form.Item>
        <Form.Item name="length" hidden />
        <Form.Item name="width" hidden />
        <Form.Item label="Ad Image Size">
          <Text>
            <b>{adSetting?.width || ''}px</b> (Width) x <b>{adSetting?.height || ''}px</b> (Height)
          </Text>
        </Form.Item>
        <Form.Item name="imageUrl" label="Ad Image URL" rules={[{ required: true, message: 'Missing Ad Image!' }]}>
          <Input placeholder="Please Upload Image of the Ad" value={imageUrl} style={{ marginBottom: 6 }} />
          <Upload
            accept="image/*"
            name="file"
            listType="picture-card"
            className="avatar-uploader"
            showUploadList={false}
            action={uploadUrl}
            onChange={handleFileUploadHook({ setImageUrl, form, setLoading })}
            beforeUpload={beforeFileUpload}
            withCredentials={true}
          >
            {uploadButton()}
          </Upload>
        </Form.Item>
        <Form.Item
          name="jumpUrl"
          label="Redirect URL / Contact Email:"
          hasFeedback
          rules={[{ required: true, message: 'Missing Redirect/Access URL!' }, { validator: checkUrlString }]}
        >
          <Input value={recordData?.jumpUrl} placeholder="Please input the url" />
        </Form.Item>
        <Form.Item name="description" label="Ad Description:">
          <TextArea value={recordData?.description} placeholder="Please input the url" rows={3} />
        </Form.Item>
        <Form.Item name="startTime" hidden />
        <Form.Item name="endTime" hidden />
        <Form.Item name="status" hidden />
        <Form.Item name="commonAd" hidden />
        <Form.Item name="id" hidden />
      </Form>
      <BlockCountries
        countryToGet="unblocked"
        defaultCheckedList={newCountryList}
        onSelectChange={(list: string[]) => {
          setNewCountryList(list);
        }}
        onClose={() => {
          setCountryModalVisible(false);
          setNewCountryList(recordData?.country || []);
        }}
        onBlockCoutriesConfirm={() => {
          setCountryModalVisible(false);
          setCountry(newCountryList);
          form.setFieldsValue({ country: newCountryList });
        }}
        isBlockCountriesVisible={countryModalVisible}
        title={'Country Setting'}
      />
    </Modal>
  );
};

export default AdModal;
