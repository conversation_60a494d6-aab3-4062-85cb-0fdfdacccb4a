import { LockOutlined, UserOutlined } from '@ant-design/icons';
import React, { useCallback, useState, useEffect } from 'react';
import { notification, Button, Form, Input } from 'antd';
import { history } from 'umi';
import { inject, observer } from 'mobx-react';
import { useAuth } from '@/contexts/AuthContext';

import logo from '../../../public/img/logo.png';
import styles from './index.less';

const Login: React.FC = inject('store')(
  observer((props: any) => {
    const {
      store: { UserStore },
    } = props;
    const { login, isAuthenticated } = useAuth();
    const [username, setName] = useState('');
    const [password, setPassword] = useState('');
    const [loading, setLoading] = useState(false);

    // Redirect if already authenticated
    useEffect(() => {
      if (isAuthenticated) {
        history.replace('/');
      }
    }, [isAuthenticated]);

    const onFinish = useCallback(
      async (values: { username: string; password: string }) => {
        const { username: formUsername, password: formPassword } = values;

        if (!formUsername || !formPassword) {
          notification.error({
            key: 'login',
            message: 'Error',
            description: 'Please enter both username and password.'
          });
          return;
        }

        setLoading(true);
        try {
          const success = await login(formUsername, formPassword);
          if (success) {
            // Keep compatibility with existing UserStore
            UserStore.setUsername(formUsername);
            setName(formUsername);
            history.replace('/');
          }
        } catch (error) {
          console.error('Login error:', error);
        } finally {
          setLoading(false);
        }
      },
      [login, UserStore],
    );

    return (
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.header}>
            <img className={styles.logo} src={logo} alt="logo" />
          </div>
          <Form name="basic" initialValues={{ remember: true }} onFinish={onFinish} autoComplete="off">
            <Form.Item name="username" rules={[{ required: true, message: 'Please input your username!' }]}>
              <Input
                prefix={<UserOutlined />}
                size="large"
                defaultValue={username}
                disabled={loading}
                onChange={(e) => setName(e.target.value)}
              />
            </Form.Item>

            <Form.Item name="password" rules={[{ required: true, message: 'Please input your password!' }]}>
              <Input.Password
                prefix={<LockOutlined />}
                size="large"
                defaultValue={password}
                disabled={loading}
                onChange={(e) => setPassword(e.target.value)}
              />
            </Form.Item>

            <Form.Item>
              <Button loading={loading} type="primary" size="large" block htmlType="submit">
                Log in
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    );
  }),
);

export default Login;
