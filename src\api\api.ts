import { SportTypeCodeEnum } from '@/constant/sportConfig';
import igsRequest, { getBaseURL } from './instance';
import * as featuredSectionApiImport from './featuredSectionApi';
import { ResponseData, UserData } from '@/model';
import { localStorageKey } from '@/constant/config';

export const confirmUser = async (username: string) => {
  return igsRequest.get(`admin/user/${username}`);
};

export const goLogin = async ({
  username,
  password,
}: {
  username: string;
  password: string;
}): Promise<ResponseData<UserData>> => {
  return igsRequest.post(`admin/login?username=${username}&password=${password}`);
};

export const getAdList = async () => {
  return igsRequest.get('user/ad/list');
};

export const createAd = async (params: any) => {
  return igsRequest.post(
    'user/ad/create',
    Object.assign(params, {
      client: '123',
      createdBy: localStorage.getItem(localStorageKey.USERNAME),
    }),
  );
};

export const updateAd = async (params: any) => {
  return igsRequest.post(
    'user/ad/update',
    Object.assign(params, {
      client: '123',
      modifiedBy: localStorage.getItem(localStorageKey.USERNAME),
    }),
  );
};

export const getAdDetail = async (id: number) => {
  return igsRequest.get(`user/ad/${id}`);
};

export const batchHandleAdStatus = async (list: { ids: number[]; status: number }) => {
  return igsRequest.post('user/ad/updateStatus', list);
};

export const deleteAds = async (ids: number[]) => {
  return igsRequest.post('user/ad/delete', ids);
};

export interface BatchHandleAllStatusParams {
  sign: 'admin';
  videoDisplays?: { display: boolean; platform: 'ios' | 'android' }[];
  oddsDisplays?: { display: boolean; platform: 'ios' | 'android' | 'web' }[];
  imageDisplays?: { display: boolean; platform: 'ios' | 'android' }[];
}

export const batchHandleAllStatus = async (data: BatchHandleAllStatusParams) => {
  return igsRequest.post('user/config/displayChange', data);
};

export const uploadUrl = `${getBaseURL()}admin/user/image/upload`;

export const goLogout = async () => {
  return igsRequest.post('admin/logout');
};

export const getVideoList = async ({ sportType = null, startTime = '', endTime = '', pageNum = 0, pageSize = 20 }) => {
  return igsRequest.post('user/video/search', {
    sportType: sportType === null ? null : SportTypeCodeEnum[sportType],
    startTime,
    endTime,
    pageNum,
    pageSize,
  });
};

// user/video/createAndUpdate
// "mappingList": [
//   {
//       "matchId": "m123",
//       "videoId": "801",
//       "sportType": 0
//   },
//   {
//       "matchId": "m123",
//       "videoId": "803",
//       "sportType": 1
//   }
// ]
interface MappingList {
  matchId: string;
  videoId: string;
  sportType: number;
}

export const createVideo = async (mappingList: MappingList[] = []) => {
  return igsRequest.post('user/video/createAndUpdate', {
    mappingList,
  });
};

// user/video/banMatchVideo/createAndUpdate
// {
//   "videoMatchBanList": [
//       {
//           "matchId": "m123",
//           "countryCodes": [
//               "CN",
//               "US",
//               "KR"
//           ],
//           "sportType": 0
//       },
//       {
//           "matchId": "m123",
//           "countryCodes": null,
//           "sportType": 1
//       }
//   ]
// }
export const banMatchVideo = async (videoMatchBanList: any[] = []) => {
  return igsRequest.post('user/video/banMatchVideo/createAndUpdate', {
    videoMatchBanList,
  });
};

// user/video/banCompetitionVideo/createAndUpdate
// {
//   "videoCompetitionBanList": [
//       {
//           "competitionId": "123",
//           "countryCodes": [
//               "CN",
//               "US",
//               "KR"
//           ],
//           "sportType": 0
//       },
//       {
//           "competitionId": "123",
//           "countryCodes":null, //取消禁用，此处为空
//           "sportType": 1
//       }
//   ]
// }
export const banCompetitionVideo = async (videoCompetitionBanList: any[] = [], isCreate: Boolean = false) => {
  const payload: any = { videoCompetitionBanList, modifiedBy: localStorage.getItem(localStorageKey.USERNAME) };
  if (isCreate) {
    payload['createdBy'] = localStorage.getItem(localStorageKey.USERNAME);
  }
  return igsRequest.post('user/video/banCompetitionVideo/createAndUpdate', payload);
};

// user/video/banCompetitionVideo/search
export const searchBanCompetitions = async ({ sportType = null, competitionId = null, pageNum = 0, pageSize = 20 }) => {
  return igsRequest.post('user/video/banCompetitionVideo/search', {
    sportType: sportType === null ? null : SportTypeCodeEnum[sportType],
    competitionId: sportType === null ? null : competitionId,
    pageNum,
    pageSize,
  });
};

export const getMatchingList = async ({ pageNum = 0, pageSize = 20, mappingType = '' }) => {
  return igsRequest.post('user/name_mapping/search', {
    mappingType,
    pageNum,
    pageSize,
  });
};

interface NameMappingRules {
  id?: number;
  igScoreName: string;
  liveStreamName: string;
  sportType: number;
  mappingType: 'Team' | 'Competition';
}

export const createRule = async (nameMappingRules: NameMappingRules[] = []) => {
  return igsRequest.post('user/name_mapping/create', {
    nameMappingRules,
  });
};

export const updateRule = async (nameMappingRules: NameMappingRules[] = []) => {
  return igsRequest.post('user/name_mapping/update', {
    nameMappingRules,
  });
};

export const deleteRule = async (nameMappingRules: { id: number }[] = []) => {
  return igsRequest.post('user/name_mapping/delete', {
    nameMappingRules,
  });
};

// the API is an override operation, whitelist and blacklist must be passed in full each time, not providing will clear
interface WhiteBlackListParams {
  // when whitelist is empty or not provided, the whitelist will be cleared
  whitelist?: string[];
  // when blacklist is empty or not provided, the blacklist will be cleared
  blacklist?: string[];
}

export const updateWhiteBlackList = async (data: WhiteBlackListParams = {}): Promise<any> => {
  return igsRequest.post('user/config/video/white-black-list', data);
};

export interface WhiteBlackListData {
  whitelist: string[];
  blacklist: string[];
}

export const getWhiteBlackList = async (): Promise<ResponseData<WhiteBlackListData>> => {
  return igsRequest.get('user/config/video/white-black-list/get');
};

export const featuredSectionApi = featuredSectionApiImport;
