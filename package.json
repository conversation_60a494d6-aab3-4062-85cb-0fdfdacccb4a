{"private": true, "scripts": {"start": "APP_ENV=production umi dev", "start:local": "cross-env APP_ENV=production umi dev", "start:staging": "APP_ENV=staging umi dev", "build": "APP_ENV=production umi build", "build:local": "cross-env APP_ENV=development umi build", "build:staging": "APP_ENV=staging umi build", "postinstall": "umi generate tmp", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage"}, "dependencies": {"@ant-design/icons": "^5.3.7", "@ant-design/pro-components": "2.3.50", "antd": "^5.1.2", "axios": "^1.6.0", "dayjs": "^1.11.9", "fuse.js": "^7.0.0", "i18n-iso-countries": "^7.14.0", "mobx": "^6.6.0", "mobx-react": "^7.5.0", "moment": "^2.29.4", "qs": "^6.11.2", "react": "17.x", "react-dom": "17.x", "umi": "^3.5.36", "xlsx": "0.18.0"}, "devDependencies": {"@types/qs": "^6.9.7", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@umijs/preset-react": "1.x", "@umijs/test": "^3.5.36", "cross-env": "^7.0.3", "lint-staged": "^10.0.7", "prettier": "^2.2.0", "typescript": "^4.1.2", "yorkie": "^2.0.0"}}