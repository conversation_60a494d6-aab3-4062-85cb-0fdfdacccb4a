import React, { ChangeEvent, memo, useCallback, useEffect, useMemo, useState } from 'react';
import { CountryList } from '@/constant/country';
import { Checkbox, Divider, Modal, Input, Typography, Row, Col, Tag } from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import type { CheckboxValueType } from 'antd/es/checkbox/Group';
import { SearchOutlined } from '@ant-design/icons';
import { debounce } from '@/utils/help';
import './BlockCountries.less';

const { Text } = Typography;

interface BlockCountriesContentProps {
  defaultCheckedList?: string[] | [];
  onSelectChange?: (list: string[]) => void;
  countryToGet?: 'blocked' | 'unblocked';
  isClose?: boolean;
}

interface BlockCountriesProps {
  countryToGet?: 'blocked' | 'unblocked';
  defaultCheckedList?: string[] | [];
  onBlockCoutriesConfirm?: (list: string[]) => void;
  onClose?: () => void;
  onSelectChange?: (list: string[]) => void;
  isBlockCountriesVisible?: boolean;
  title?: string;
}

/**
 * @param countryToGet {blocked | unblocked}:  if countryToGet === 'blocked', `checkedList` array return is country to blocked, vice versa
 *
 * NOTE: checkedList contains country code that are `blocked`, uncheckedList contains country code that are `not blocked`
 */
export const BlockCountriesContent: React.FC<BlockCountriesContentProps> = memo((props: any) => {
  const { defaultCheckedList = [], onSelectChange = null, countryToGet = 'blocked' } = props;
  const [checkedList, setCheckedList] = useState<CheckboxValueType[]>(defaultCheckedList);
  // const [checkedCountryNameList, setCheckedCountryNameList] = useState<string[]>([]);
  // const [unCheckedCountryNameList, setUnCheckedCountryNameList] = useState<string[]>([]);
  const [checkAll, setCheckAll] = useState(false);
  const [options, setOptions] = useState(CountryList);
  const [allOptionsValue, setAllOptionsValue] = useState<string[]>([]);
  const [searchWords, setSearchWords] = useState('');

  const indeterminate = checkedList.length > 0 && checkedList.length < CountryList.length;

  const [unCheckedCountryNameList, checkedCountryNameList] = useMemo(() => {
    const notblocked: string[] = [];
    const blocked: string[] = [];
    CountryList.forEach((item) => {
      (checkedList.includes(item.value) ? blocked : notblocked).push(item.label);
    });
    return [notblocked, blocked];
  }, [checkedList]);

  // useEffect to re-render country list option when searching for country
  useEffect(() => {
    const searchedCountry = CountryList.filter(
      (item) => item.label.toLocaleLowerCase().indexOf(searchWords.toLocaleLowerCase()) > -1,
    );
    setOptions(searchedCountry);
    setAllOptionsValue(searchedCountry.map((item) => item.value));
  }, [searchWords]);

  useEffect(() => {
    if (onSelectChange) {
      onSelectChange(checkedList);
    }
  }, [checkedList, onSelectChange]);

  const onChange = async (e: CheckboxValueType[]) => {
    // Get values that were previously checked but are not in current filtered options
    const checkedButNotVisible = checkedList.filter((item) => !options.map((opt) => opt.value).includes(item as string));

    // Combine with newly checked items from visible options
    const newList = [...new Set([...checkedButNotVisible, ...e])];

    setCheckedList(newList);
    setCheckAll(e.length === options.length);
  };

  const countrySearchOnChange = debounce((e: ChangeEvent<HTMLInputElement>) => {
    setSearchWords(e.target.value);
  }, 300);

  const onCheckAllChange = useCallback(
    (e: CheckboxChangeEvent) => {
      setCheckedList(e.target.checked ? allOptionsValue : []);
      setCheckAll(e.target.checked);
    },
    [allOptionsValue],
  );

  // handle processing of check list to gather
  // useEffect(() => {
  //   const notblockedCountryList: string[] = [];
  //   const blockedCountryList: string[] = [];
  //   CountryList.forEach((item) => {
  //     if (!checkedList.includes(item.value)) {
  //       notblockedCountryList.push(item.label);
  //     } else {
  //       blockedCountryList.push(item.label);
  //     }
  //   });
  //   setUnCheckedCountryNameList(notblockedCountryList);
  //   setCheckedCountryNameList(blockedCountryList);
  // }, [checkedList]);

  return (
    <Row justify={'space-between'}>
      <Col className="group-box">
        <div className="country-list-top-section">
          <Checkbox onChange={onCheckAllChange} checked={checkAll} indeterminate={indeterminate}>
            <Text strong>Select All ({checkedList.length} Selected)</Text>
          </Checkbox>
          <Divider />
        </div>
        <Input
          onChange={countrySearchOnChange}
          size="large"
          placeholder="Search Country..."
          prefix={<SearchOutlined />}
          allowClear
          className="search-country-input"
        />
        <div className="country-list">
          <Checkbox.Group
            options={options}
            onChange={onChange}
            value={checkedList}
            className="country-list-checkbox"
            key={checkedList.length}
          />
        </div>
      </Col>
      <Col className="group-box">
        <div className="section-header">
          <Text strong>Not Blocked in Following Country</Text>
          <Divider />
        </div>
        <div className="unblocked-country-list">
          {countryToGet === 'unblocked'
            ? checkedCountryNameList.map((item: string) => item && <Tag>{item}</Tag>)
            : unCheckedCountryNameList.map((item: string) => item && <Tag>{item}</Tag>)}
        </div>
      </Col>
      <Col className="group-box">
        <div className="section-header">
          <Text strong>Blocked in Following Country</Text>
          <Divider />
        </div>
        <div className="unblocked-country-list">
          {countryToGet === 'unblocked'
            ? unCheckedCountryNameList.map((item: string) => item && <Tag>{item}</Tag>)
            : checkedCountryNameList.map((item: string) => item && <Tag>{item}</Tag>)}
        </div>
      </Col>
    </Row>
  );
});

const BlockCountries: React.FC<BlockCountriesProps> = (props: any) => {
  const {
    defaultCheckedList = [],
    onBlockCoutriesConfirm,
    onClose,
    onSelectChange = null,
    isBlockCountriesVisible,
    title,
    ...otherProps
  } = props;
  const [checkedList, setCheckedList] = useState<CheckboxValueType[]>(defaultCheckedList);

  const onChange = useCallback(
    (list: CheckboxValueType[]) => {
      setCheckedList(list);
      if (onSelectChange) {
        onSelectChange(list);
      }
    },
    [onSelectChange],
  );

  const confirm = useCallback(() => {
    onBlockCoutriesConfirm(checkedList);
  }, [checkedList]);

  return (
    <Modal
      className="pop-up-style"
      title={title}
      open={isBlockCountriesVisible}
      onOk={confirm}
      onCancel={onClose}
      destroyOnClose={true}
      {...otherProps}
      width={1000}
    >
      <BlockCountriesContent {...props} onSelectChange={onChange} />
    </Modal>
  );
};

export default memo(BlockCountries);
