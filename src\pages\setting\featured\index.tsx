import React, { useEffect, useMemo, useState } from 'react';
import {
  Space,
  Table,
  Modal,
  Button,
  notification,
  Tag,
  message,
  Typography,
  Switch,
  Tooltip,
  Input,
  Col,
  Select,
  DatePicker,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { RedoOutlined, PlusOutlined, DeleteOutlined, FormOutlined } from '@ant-design/icons';
import { FeaturedSectionApi } from '@/api';
import { SportTypeList } from '@/constant/sportConfig';
import { statusFilterOption } from '@/constant/config';
import { debounce } from '@/utils/help';

import './index.less';
import { dateTimeTagComponent } from '@/components/Common/CommonComponent';
import FeaturedSectionModal from '@/components/featuredComponent/FeaturedSectionModal';
import { FeaturedSectionDataType } from '@/model';
import { FeaturedSectionHooks } from '@/hooks';

const { confirm } = Modal;
const { Text } = Typography;
const { categoryListHook, fuzzySearchHook, getListHook, handleStartTimeChangeHook, handleDeleteHook } = FeaturedSectionHooks;
const { updateData } = FeaturedSectionApi;
const pageDisplayLabel = 'Featured';

const FeaturedSectionList: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const [allRecord, setAllRecord] = useState<FeaturedSectionDataType[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [statusUpdateLoad, setStatusUpdateLoad] = useState<{ [key: number]: boolean }>({});
  const [isModalVisible, setModalVisible] = useState(false);
  const [recordId, setRecordId] = useState<number | null>(null);
  const [actionType, setActionType] = useState('');
  const [sportType, setSportType] = useState(-1);
  const [category, setCategory] = useState<string | null>(null);
  const [startTime, setStartTime] = useState<number | null>(null);
  const [total, setTotal] = useState(0);
  const [pageNum, setPageNum] = useState(0);
  const [pageSize, setPageSize] = useState(15);
  const [descriptionFilter, setDescriptionFilter] = useState('');

  const sportsArray = SportTypeList.map((item) => ({ label: item.label, value: item.value }));
  sportsArray.unshift({
    value: -1,
    label: 'All Sports',
  });

  const hasSelected = useMemo(() => selectedRowKeys.length > 0, [selectedRowKeys]);
  const getList = getListHook({
    sportType,
    category,
    startTime,
    pageNum,
    pageSize,
    setAllRecord,
    setTotal,
    setLoading,
    messageApi,
  });

  //initial load
  useEffect(() => {
    setLoading(true);
    getList();
  }, [sportType, category, startTime, pageNum, pageSize]);

  const resetModal = () => {
    setModalVisible(false);
    setRecordId(null);
  };

  const onPageChange = (nextPageNumber: number, nextPageSize: number) => {
    if (pageSize !== nextPageSize) {
      setPageSize(nextPageSize);
      setPageNum(0);
    } else {
      setPageNum(nextPageNumber - 1);
    }
  };

  const filteredData = fuzzySearchHook<FeaturedSectionDataType>(allRecord, descriptionFilter, 0.5, ['description'], setLoading);

  const handleDelete = (id: number) =>
    handleDeleteHook({
      confirm,
      id,
      selectedRowKeys,
      pageDisplayLabel,
      getList,
      messageApi,
    });

  const updateStatus = (data: FeaturedSectionDataType) => {
    const newStatus: number = data.status === 1 ? 0 : 1;
    //update status field with new status after confirm update action
    updateData(Object.assign(data, { status: newStatus }))
      .then(() => {
        notification.success({
          key: 'success',
          message: 'Success!',
          description: (
            <Text>
              Status Update Success! {pageDisplayLabel} ID: <b> {data.id || ''}</b>
            </Text>
          ),
        });
        getList();
      })
      .catch(() => {
        notification.error({
          key: 'error',
          message: 'Error!',
          description: (
            <Text>
              Status Update Failed! {pageDisplayLabel} ID: <b> {data.id || ''}</b>
            </Text>
          ),
        });
      })
      .finally(() => {
        setStatusUpdateLoad((prev) => ({ ...prev, [data.id]: false }));
      });
  };

  const updateStatusConfirm = (data: FeaturedSectionDataType) => {
    setStatusUpdateLoad((prev) => ({ ...prev, [data.id]: true }));
    confirm({
      title: `Confirmation`,
      content: (
        <Text>
          {data.status === 1 ? 'Disable' : 'Enable'} {pageDisplayLabel} <b>(ID: {data.id || ''})</b>?
        </Text>
      ),
      onOk() {
        updateStatus(data);
      },
      onCancel() {
        setStatusUpdateLoad((prev) => ({ ...prev, [data.id]: false }));
      },
      maskClosable: true,
    });
  };

  const columns: ColumnsType<FeaturedSectionDataType> = useMemo(
    () => [
      {
        title: 'Id',
        dataIndex: 'id',
        key: 'id',
      },
      {
        title: 'Sports',
        dataIndex: 'sportType',
        key: 'sportType',
        render: (value) => {
          const tagColor = SportTypeList.filter((item) => item.value === value)[0].color;
          return (
            <Tag color={tagColor} className="category-tag">
              {SportTypeList.filter((item) => item.value === value)[0].label}
            </Tag>
          );
        },
      },
      {
        title: 'Category',
        dataIndex: 'category',
        key: 'category',
        render: (value) => <Text strong>{value && value.toUpperCase()}</Text>,
      },
      {
        title: 'Start Date (UTC)',
        dataIndex: 'startTime',
        key: 'startTime',
        align: 'center',
        sorter: (a: any, b: any) => {
          return a.startTime - b.startTime;
        },
        render: (value) => dateTimeTagComponent(value, 'green'),
        width: '10%',
      },
      {
        title: 'End Date (UTC)',
        dataIndex: 'endTime',
        key: 'endTime',
        align: 'center',
        sorter: (a: any, b: any) => {
          return a.endTime - b.endTime;
        },
        render: (value) => dateTimeTagComponent(value, 'red'),
        width: '10%',
      },
      {
        title: (
          <Col>
            Description
            <Input
              placeholder="Search..."
              onChange={debounce((e: any) => {
                setLoading(true);
                setDescriptionFilter(e.target.value);
              }, 300)}
              className="search-input"
            />
          </Col>
        ),
        dataIndex: 'description',
        key: 'description',
        width: '15%',
      },
      {
        title: 'Extra Description',
        dataIndex: 'extraDescription',
        key: 'extraDescription',
        width: '10%',
      },
      {
        title: 'Modified At',
        dataIndex: 'modifiedAt',
        key: 'modifiedAt',
        align: 'center',
        sorter: (a: any, b: any) => {
          return a.modifiedAt - b.modifiedAt;
        },
        render: (value) => dateTimeTagComponent(value, ''),
      },
      {
        title: 'Modified By',
        dataIndex: 'modifiedBy',
        key: 'modifiedBy',
      },
      {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        align: 'center',
        filters: statusFilterOption,
        onFilter: (value, record) => value === record?.status,
        render: (value, record) => {
          const checkStatus: boolean = value === 1 ? true : false;
          return (
            <Switch
              checkedChildren="Enable"
              unCheckedChildren="Disable"
              loading={statusUpdateLoad[record.id]}
              checked={checkStatus}
              onClick={() => {
                updateStatusConfirm(record);
              }}
            />
          );
        },
      },
      {
        title: 'Options',
        key: 'action',
        align: 'center',
        render: (_, record) => (
          <Space size="middle">
            <Tooltip title="EDIT">
              <FormOutlined
                className="form-outline"
                onClick={() => {
                  setActionType('update');
                  setRecordId(record.id);
                  setModalVisible(true);
                }}
              />
            </Tooltip>
            <Tooltip title="DELETE">
              <DeleteOutlined
                className="form-outline"
                onClick={() => {
                  handleDelete(record.id);
                }}
              />
            </Tooltip>
          </Space>
        ),
      },
    ],
    [allRecord, statusUpdateLoad],
  );

  return (
    <div>
      {contextHolder}
      <Space wrap style={{ marginBottom: 16 }}>
        <Select
          value={sportType}
          style={{ width: 150 }}
          onChange={(value) => {
            setSportType(value);
            setCategory(null);
          }}
          options={sportsArray}
        />
        {/* Select choice will be disable if `ALL` sport is returned */}
        <Select
          disabled={sportType < 0}
          allowClear
          value={category}
          placeholder="Choose Category..."
          style={{ width: 200 }}
          onChange={(value) => setCategory(value)}
          options={categoryListHook({ sportType })}
        />
        <DatePicker
          onChange={(date, dateString) => handleStartTimeChangeHook({ date: date, setStartTime })}
          format="YYYY-MM-DD"
          placeholder="Select Start Time..."
          style={{ width: 200 }}
        />
        <Button
          type="primary"
          onClick={() => {
            setActionType('create');
            setModalVisible(true);
          }}
        >
          <PlusOutlined />
          Create
        </Button>
        <Button type="primary" onClick={getList}>
          <RedoOutlined />
          Refresh
        </Button>
        <Button type="primary" disabled={!hasSelected} danger onClick={() => handleDelete(-1)}>
          <DeleteOutlined />
          Delete
        </Button>
      </Space>
      <Table
        rowKey="id"
        loading={loading}
        columns={columns}
        dataSource={filteredData}
        rowSelection={{
          selectedRowKeys,
          onChange: (newSelectedRowKeys: any) => setSelectedRowKeys(newSelectedRowKeys),
        }}
        pagination={{
          total,
          pageSize,
          defaultPageSize: 50,
          pageSizeOptions: [20, 50, 100],
          showSizeChanger: true,
          current: pageNum + 1,
          showQuickJumper: true,
          onChange: onPageChange,
          showTotal: (total, range) => (
            <Text>
              Showing <b>{range[0]}-{range[1]}</b> of <b>{total}</b>
            </Text>
          ),
        }}
      />
      <FeaturedSectionModal
        isModalVisible={isModalVisible}
        onCancel={resetModal}
        resetModal={() => {
          resetModal();
          getList();
        }}
        actionType={actionType}
        recordId={recordId}
      />
    </div>
  );
};

export default FeaturedSectionList;
