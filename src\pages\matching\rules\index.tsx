// @ts-nocheck
// noinspection JSUnresolvedReference

import React from 'react';
import { Collapse, Tabs, Typography } from 'antd';
import type { TabsProps } from 'antd';
import MatchRules from './matchRules';

const { Title, Text, Paragraph } = Typography;

const items: TabsProps['items'] = [
  {
    key: '1',
    label: 'Competition Name Rules',
    children: <MatchRules mappingType="League" />,
  },
  {
    key: '2',
    label: 'Team Name Rules',
    children: <MatchRules mappingType="Team" />,
  },
];

const MatchIndex = React.memo(() => {
  return (
    <div>
      <Collapse ghost>
        <Collapse.Panel
          header={
            <Text type="danger" strong>
              Introduction To Use
            </Text>
          }
          key="1"
        >
          <Title level={5} style={{ marginTop: 0 }}>
            The purpose of this page is to enable the configuration of matching rules for the auto-create-livestream program.
          </Title>
          <Paragraph>
            In both sportsmng data and igscore data, certain leagues and teams are not automatically recognized as having the same
            name. For instance:
          </Paragraph>
          <Paragraph>
            <ul>
              <li>
                For leagues, "THAILAND THAI LEAGUE 2" (sportsmng) and "Thai League 2" (igscore) cannot be regarded as the same
                team due to various ways of naming countries (such as "thailand thai" vs "thai"), which cannot all be listed for
                automatic identification;
              </li>
              <li>
                For teams, "Western Sydney Wanderers" (sportsmng) and "WS Wanderers" (igscore) cannot be regarded as the same team
                because their abbreviations ("Western Sydney" vs "WS") do not match, with countless variations of abbreviations
                making it impossible to list them all for automatic identification.
              </li>
            </ul>
          </Paragraph>
          <Paragraph>Consequently, special league and team names require manual matching on this page. For instance:</Paragraph>
          <Paragraph>
            <ul>
              <li>
                For leagues, after creating the matching rule for "THAILAND Thai League 2" and "Thai League 2", any leagues
                resembling "thailand thai league x" or "thai league x" will be considered equivalent.
                <ul>
                  <li>
                    There is no need to worry about identifying "League 1" and "League 2" as the same league since prior to
                    implementing this rule, match start times are first assessed where different times would not be deemed
                    indicative of the same league.
                  </li>
                  <li>
                    So for names with the same structure, such as "THAILAND THAI LEAGUE 2", there is no need to create more rules
                    (e.g. THAILAND THAI LEAGUE 1, THAILAND THAI LEAGUE 3...). , you can create only one.
                  </li>
                </ul>
              </li>
              <li>Team names are handled in the same way as league names.</li>
            </ul>
          </Paragraph>
        </Collapse.Panel>
      </Collapse>
      <Tabs defaultActiveKey="1" items={items} />
    </div>
  );
});

export default MatchIndex;
