import igsRequest from './instance';

const urlPath = 'user/ad';

export const getData = async (request: any) => {
  return igsRequest.post(`${urlPath}/search`, request);
};

export const createData = async (params: any) => {
  return igsRequest.post(
    `${urlPath}/create`,
    Object.assign(params, {
      client: '123',
      createdBy: localStorage.getItem('username'),
    }),
  );
};

export const updateData = async (params: any) => {
  return igsRequest.post(
    `${urlPath}/update`,
    Object.assign(params, {
      client: '123',
      modifiedBy: localStorage.getItem('username'),
    }),
  );
};

export const updateStatus = async (params: any) => {
  return igsRequest.post(
    `${urlPath}/updateStatus`,
    Object.assign(params, {
      client: '123',
      modifiedBy: localStorage.getItem('username'),
    }),
  );
};

export const getSingleData = async (id: number) => {
  return igsRequest.get(`${urlPath}/${id}`);
};

export const deleteData = async (ids: number[]) => {
  return igsRequest.post(`${urlPath}/delete`, ids);
};
