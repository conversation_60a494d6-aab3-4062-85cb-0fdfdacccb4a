import igsRequest from './instance';
import { localStorageKey } from '@/constant/config';

const urlPath = 'user/ad';

export const getData = async (request: any) => {
  return igsRequest.post(`${urlPath}/search`, request);
};

export const createData = async (params: any) => {
  return igsRequest.post(
    `${urlPath}/create`,
    Object.assign(params, {
      client: '123',
      createdBy: localStorage.getItem(localStorageKey.USERNAME),
    }),
  );
};

export const updateData = async (params: any) => {
  return igsRequest.post(
    `${urlPath}/update`,
    Object.assign(params, {
      client: '123',
      modifiedBy: localStorage.getItem(localStorageKey.USERNAME),
    }),
  );
};

export const updateStatus = async (params: any) => {
  return igsRequest.post(
    `${urlPath}/updateStatus`,
    Object.assign(params, {
      client: '123',
      modifiedBy: localStorage.getItem(localStorageKey.USERNAME),
    }),
  );
};

export const getSingleData = async (id: number) => {
  return igsRequest.get(`${urlPath}/${id}`);
};

export const deleteData = async (ids: number[]) => {
  return igsRequest.post(`${urlPath}/delete`, ids);
};
