import { confirmUser } from '@/api/api';

/**
 * Server-side role verification utility
 * This function calls the server to verify the user's actual roles
 * to prevent client-side tampering
 */
export const verifyUserRoleOnServer = async (username: string, requiredRole: string): Promise<boolean> => {
  try {
    const response: any = await confirmUser(username);

    if (response.code === 'A00000' && response.result) {
      const userData = response.result;

      if (userData.authorities && Array.isArray(userData.authorities)) {
        return userData.authorities.some((auth: any) => auth.authority === requiredRole);
      }
    }

    return false;
  } catch (error) {
    console.error('Server role verification failed:', error);
    return false;
  }
};

/**
 * Enhanced security check that combines client and server verification
 */
export const performSecurityCheck = async (clientHasRole: boolean, username: string, requiredRole: string): Promise<boolean> => {
  // If client says user doesn't have role, no need to check server
  if (!clientHasRole) {
    return false;
  }

  // Verify with server to prevent client-side tampering
  const serverVerification = await verifyUserRoleOnServer(username, requiredRole);

  // Both client and server must agree
  return clientHasRole && serverVerification;
};

/**
 * Secure session validation
 * Checks if the current session is valid and hasn't been tampered with
 */
export const validateSession = (): boolean => {
  const token = localStorage.getItem('auth_session');
  const roleHash = localStorage.getItem('role_hash');
  const username = localStorage.getItem('username');

  // All required items must be present
  if (!token || !roleHash || !username) {
    return false;
  }

  try {
    // Basic validation - in production, add more sophisticated checks
    const decodedToken = atob(token);
    return decodedToken.includes(username);
  } catch {
    return false;
  }
};

/**
 * Clear all authentication data
 */
export const clearAuthData = (): void => {
  localStorage.removeItem('auth_session');
  localStorage.removeItem('role_hash');
  localStorage.removeItem('username');
};

/**
 * Role constants for consistency
 */
export const AUTH_ROLES = {
  MASTER: 'ROLE_MASTER',
  ADMIN: 'ROLE_ADMIN',
  USER: 'ROLE_USER',
} as const;

/**
 * Check if user has any of the specified roles
 */
export const hasAnyRole = (userRoles: string[], requiredRoles: string[]): boolean => {
  return requiredRoles.some((role) => userRoles.includes(role));
};

/**
 * Check if user has all of the specified roles
 */
export const hasAllRoles = (userRoles: string[], requiredRoles: string[]): boolean => {
  return requiredRoles.every((role) => userRoles.includes(role));
};
