// @ts-nocheck
import axios from 'axios';

const isStaging = location.origin.includes('staging');
const prodHost = 'https://api.igscore.net:8080/';
const stagingHost = 'https://stagingapi.igscore.net:8080/';
const localHost = 'http://localhost:8080/';

export const getBaseURL = () => {
  if (APP_ENV === 'production') {
    return prodHost;
  } else if (APP_ENV === 'staging') {
    return stagingHost;
  } else {
    return localHost;
  }
};
export const BaseImageURL = (APP_ENV === 'staging' ? 'https://staging.igscore.net/' : 'https://www.igscore.net/') + 'static-images/ads/';

const igsRequest = axios.create({
  baseURL: getBaseURL(),
  timeout: 100000,
  withCredentials: true,
});

// 请求拦截,所有的网络请求都会先走这个方法
// igsRequest.interceptors.request.use(
//   function (config) {
//     const nextConfig = Object.assign(config, {
//       // withCredentials: true,
//       // data: Object.assign(config.data),
//     });
//     return nextConfig;
//   },
//   function (err) {
//     return Promise.reject(err);
//   },
// );

// 请求拦截,所有的网络请求都会先走这个方法
igsRequest.interceptors.response.use(
  function (response) {
    // add config
    try {
      return response.data;
    } catch (e) {
      console.error(e);
      return {};
    }
  },
  function (err) {
    try {
      if (err.response.data.code == '403') {
        location.replace('/login');
      }
    } catch (e) {
      return Promise.reject(err);
    }
    return Promise.reject(err);
  },
);

export default igsRequest;
