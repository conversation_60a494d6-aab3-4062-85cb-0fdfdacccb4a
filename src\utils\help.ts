import qs from 'qs';
import Fuse from 'fuse.js'; // Import Fuse.js for fuzzy search
import { SportTypeCodeEnum, igscoreSiteUrlRegex } from '@/constant';

export const getGameIdFromUrl = (url: string): string => {
  return url.match(/match\/(\w+)\//)![1];
};

export const getGameType = (url: string): number => {
  const game: string = url.match(/net\/(\w+)-/)![1];
  // @ts-ignore
  return SportTypeCodeEnum[game];
};

export const getCompIdFromUrl = (url: string): string => {
  return url.match(/competition\/(\w+)\//)![1];
};

export const getDataFromSiteUrl = (url: string): string[] => {
  return url.match(new RegExp(igscoreSiteUrlRegex + '([^/]+)/([^/]+)')) ?? [];
};

// ch=602&i=64bf6efea61a23ee9b6a85d7&s=&flv=undefined
interface Video {
  ch: string;
  i: string;
  s: string;
  flv: string;
}

export const getVideoParamsFromUrl = (url: string): Video => {
  // return url.match(/match\/(\w+)\//)![1]\
  // @ts-ignore
  return qs.parse(url.split('?')[1]);
};

export const debounce = function (fn: Function, wait: number) {
  let time: any = null;
  return function (...args: any) {
    if (time) {
      clearTimeout(time);
    }
    // @ts-ignore
    const self = this;
    time = setTimeout(() => {
      fn.apply(self, args);
    }, wait);
  };
};

export const generateUuid = (prefix: string, length: number) => {
  const uuidStr = 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx';
  const len = !length ? uuidStr.length : length;
  let date = Date.now();
  const uuid = uuidStr.replace(/[xy]/g, (c) => {
    const r = (date + Math.random() * 16) % 16 | 0;
    date = Math.floor(date / 16);
    return (c === 'x' ? r : (r & 0x7) | 0x8).toString(16);
  });
  return `${!prefix ? '' : prefix}${uuid.slice(0, len)}`;
};

// returns null if no match
export const urlStringCheck = (url: string, regex: RegExp) => {
  if (!url) {
    return false;
  }
  return url.match(regex);
};

/**
 * Fuzzy search utils
 * @param data: data to be searched
 * @param threshold: threshold/fuzziness for fuzzy search ranging 0 to 1. (0 = exact match, 1 = very fuzzy)
 * @param keys: keys to be searched
 */
export const fuzzySearch = (data: any, threshold: number, keys: string[]) => {
  const fuseInstance = new Fuse(data, {
    keys: keys, // Define which fields to search
    threshold: threshold,
  });
  return fuseInstance;
};

export const saveStore = (key = '', data = '') => {
  localStorage.setItem(key, data);
};

export const getStore = (key = '', defaultValue = null) => {
  try {
    // @ts-ignore
    const res = JSON.parse(window.localStorage.getItem(key));
    return res || defaultValue;
  } catch (e) {
    return defaultValue;
  }
};