# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ahooksjs/use-request@^2.0.0":
  "integrity" "sha512-xhVaM4fyIiAMdVFuuU5i3CFUdFa/IblF+fvITVMFaUEO3w/V5tVCAF6WIA3T03n1/RPuzRkA7Ao1PFtSGtGelw=="
  "resolved" "https://registry.npmjs.org/@ahooksjs/use-request/-/use-request-2.8.15.tgz"
  "version" "2.8.15"
  dependencies:
    "lodash.debounce" "^4.0.8"
    "lodash.throttle" "^4.1.1"

"@ampproject/remapping@^2.1.0":
  "integrity" "sha512-lFMjJTrFL3j7L9yBxwYfCq2k6qqwHyzuUl/XBnif78PWTJYyL/dfowQHWE3sp6U6ZzqWiiIZnpTMO96zhkjwtg=="
  "resolved" "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@ant-design/colors@^6.0.0":
  "integrity" "sha512-qAZRvPzfdWHtfameEGP2Qvuf838NhergR35o+EuVyB5XvSA98xod5r4utvi4TJ3ywmevm290g9nsCG5MryrdWQ=="
  "resolved" "https://registry.npmjs.org/@ant-design/colors/-/colors-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@ctrl/tinycolor" "^3.4.0"

"@ant-design/colors@^7.0.0":
  "integrity" "sha512-MMoDGWn1y9LdQJQSHiCC20x3uZ3CwQnv9QMz6pCmJOrqdgM9YxsoVVY0wtrdXbmfSgnV0KNk6zi09NAhMR2jvg=="
  "resolved" "https://registry.npmjs.org/@ant-design/colors/-/colors-7.1.0.tgz"
  "version" "7.1.0"
  dependencies:
    "@ctrl/tinycolor" "^3.6.1"

"@ant-design/cssinjs@^1.0.0", "@ant-design/cssinjs@^1.5.6":
  "integrity" "sha512-nMw2v+JcHhzi5QD62CpNzzXDHSlOhyx1KkFpCCWfxWI59Osz5o1AkExnJ1jTA1Vo1rt4J7WJX9SxLGN6OLI1hA=="
  "resolved" "https://registry.npmjs.org/@ant-design/cssinjs/-/cssinjs-1.15.0.tgz"
  "version" "1.15.0"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "@emotion/hash" "^0.8.0"
    "@emotion/unitless" "^0.7.5"
    "classnames" "^2.3.1"
    "csstype" "^3.0.10"
    "rc-util" "^5.34.1"
    "stylis" "^4.0.13"

"@ant-design/icons-svg@^4.3.0", "@ant-design/icons-svg@^4.4.0":
  "integrity" "sha512-vHbT+zJEVzllwP+CM+ul7reTEfBR0vgxFe7+lREAsAA7YGsYpboiq2sQNeQeRvh09GfQgs/GyFEvZpJ9cLXpXA=="
  "resolved" "https://registry.npmmirror.com/@ant-design/icons-svg/-/icons-svg-4.4.2.tgz"
  "version" "4.4.2"

"@ant-design/icons@^4.0.0", "@ant-design/icons@^4.3.0", "@ant-design/icons@^4.8.2":
  "integrity" "sha512-HGlIQZzrEbAhpJR6+IGdzfbPym94Owr6JZkJ2QCCnOkPVIWMO2xgIVcOKnl8YcpijIo39V7l2qQL5fmtw56cMw=="
  "resolved" "https://registry.npmjs.org/@ant-design/icons/-/icons-4.8.3.tgz"
  "version" "4.8.3"
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/icons-svg" "^4.3.0"
    "@babel/runtime" "^7.11.2"
    "classnames" "^2.2.6"
    "lodash" "^4.17.15"
    "rc-util" "^5.9.4"

"@ant-design/icons@^4.1.0":
  "integrity" "sha512-HGlIQZzrEbAhpJR6+IGdzfbPym94Owr6JZkJ2QCCnOkPVIWMO2xgIVcOKnl8YcpijIo39V7l2qQL5fmtw56cMw=="
  "resolved" "https://registry.npmjs.org/@ant-design/icons/-/icons-4.8.3.tgz"
  "version" "4.8.3"
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/icons-svg" "^4.3.0"
    "@babel/runtime" "^7.11.2"
    "classnames" "^2.2.6"
    "lodash" "^4.17.15"
    "rc-util" "^5.9.4"

"@ant-design/icons@^4.2.1":
  "integrity" "sha512-HGlIQZzrEbAhpJR6+IGdzfbPym94Owr6JZkJ2QCCnOkPVIWMO2xgIVcOKnl8YcpijIo39V7l2qQL5fmtw56cMw=="
  "resolved" "https://registry.npmjs.org/@ant-design/icons/-/icons-4.8.3.tgz"
  "version" "4.8.3"
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/icons-svg" "^4.3.0"
    "@babel/runtime" "^7.11.2"
    "classnames" "^2.2.6"
    "lodash" "^4.17.15"
    "rc-util" "^5.9.4"

"@ant-design/icons@^4.7.0":
  "integrity" "sha512-HGlIQZzrEbAhpJR6+IGdzfbPym94Owr6JZkJ2QCCnOkPVIWMO2xgIVcOKnl8YcpijIo39V7l2qQL5fmtw56cMw=="
  "resolved" "https://registry.npmjs.org/@ant-design/icons/-/icons-4.8.3.tgz"
  "version" "4.8.3"
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/icons-svg" "^4.3.0"
    "@babel/runtime" "^7.11.2"
    "classnames" "^2.2.6"
    "lodash" "^4.17.15"
    "rc-util" "^5.9.4"

"@ant-design/icons@^5.0.0", "@ant-design/icons@^5.3.7":
  "integrity" "sha512-0UrM02MA2iDIgvLatWrj6YTCYe0F/cwXvVE0E2SqGrL7PZireQwgEKTKBisWpZyal5eXZLvuM98kju6YtYne8w=="
  "resolved" "https://registry.npmjs.org/@ant-design/icons/-/icons-5.5.1.tgz"
  "version" "5.5.1"
  dependencies:
    "@ant-design/colors" "^7.0.0"
    "@ant-design/icons-svg" "^4.4.0"
    "@babel/runtime" "^7.24.8"
    "classnames" "^2.2.6"
    "rc-util" "^5.31.1"

"@ant-design/pro-card@2.1.8":
  "integrity" "sha512-PHgfVZi2IdYCGnQ5Pa4BFX7e+GpcholErmrHeWO8pECrnOaKc1q27Sq15UTFmnj5Vd6hAnZpvgluFP05aaaupQ=="
  "resolved" "https://registry.npmjs.org/@ant-design/pro-card/-/pro-card-2.1.8.tgz"
  "version" "2.1.8"
  dependencies:
    "@ant-design/icons" "^4.2.1"
    "@ant-design/pro-provider" "2.3.1"
    "@ant-design/pro-utils" "2.5.2"
    "@babel/runtime" "^7.18.0"
    "classnames" "^2.2.6"
    "omit.js" "^2.0.2"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^5.4.0"

"@ant-design/pro-components@2.3.50":
  "integrity" "sha512-5lzA4deiG95DmD1fraOKIvQ98zN7aPzfR6tPLLU7ke/BsFAnUca73JliJGoKyWKwkNkGaginXnqn2oZr8LU3lA=="
  "resolved" "https://registry.npmjs.org/@ant-design/pro-components/-/pro-components-2.3.50.tgz"
  "version" "2.3.50"
  dependencies:
    "@ant-design/pro-card" "2.1.8"
    "@ant-design/pro-descriptions" "2.0.42"
    "@ant-design/pro-field" "2.2.3"
    "@ant-design/pro-form" "2.5.2"
    "@ant-design/pro-layout" "7.5.2"
    "@ant-design/pro-list" "2.0.43"
    "@ant-design/pro-provider" "2.3.1"
    "@ant-design/pro-skeleton" "2.0.7"
    "@ant-design/pro-table" "3.2.10"
    "@ant-design/pro-utils" "2.5.2"
    "@babel/runtime" "^7.16.3"

"@ant-design/pro-descriptions@2.0.42":
  "integrity" "sha512-+1THR+J/hI6hVaJbWBAWxi60Sny7KwA/n5YlBf0d8U+wtpNShlYmvlUNtpGQ6B/Stvv7vRctXSnLZ59/aTVsoQ=="
  "resolved" "https://registry.npmjs.org/@ant-design/pro-descriptions/-/pro-descriptions-2.0.42.tgz"
  "version" "2.0.42"
  dependencies:
    "@ant-design/pro-field" "2.2.3"
    "@ant-design/pro-form" "2.5.2"
    "@ant-design/pro-skeleton" "2.0.7"
    "@ant-design/pro-utils" "2.5.2"
    "@babel/runtime" "^7.18.0"
    "rc-util" "^5.0.6"
    "use-json-comparison" "^1.0.5"

"@ant-design/pro-field@2.2.3":
  "integrity" "sha512-pXx3J7O0uoOHgzf4XVUlMAMRISpW7HFxi7U1FCwJcUNY2SRJW9H+9SY3soK5ct98AXOyOkaXq9xzai0EwPU9Kw=="
  "resolved" "https://registry.npmjs.org/@ant-design/pro-field/-/pro-field-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "@ant-design/icons" "^4.2.1"
    "@ant-design/pro-provider" "2.3.1"
    "@ant-design/pro-utils" "2.5.2"
    "@babel/runtime" "^7.18.0"
    "@chenshuai2144/sketch-color" "^1.0.8"
    "classnames" "^2.2.6"
    "dayjs" "^1.11.4"
    "lodash.tonumber" "^4.0.3"
    "omit.js" "^2.0.2"
    "rc-util" "^5.4.0"
    "swr" "^2.0.0"

"@ant-design/pro-form@2.5.2":
  "integrity" "sha512-gSa7fD/R3H9uZb6nXxrfxcxxOlhrPtpLt7Y5DO37MDILsVXOMsA3/EACSenlLal7xdOUCTQXQcnVlHsoZ4+87w=="
  "resolved" "https://registry.npmjs.org/@ant-design/pro-form/-/pro-form-2.5.2.tgz"
  "version" "2.5.2"
  dependencies:
    "@ant-design/icons" "^4.2.1"
    "@ant-design/pro-field" "2.2.3"
    "@ant-design/pro-provider" "2.3.1"
    "@ant-design/pro-utils" "2.5.2"
    "@babel/runtime" "^7.18.0"
    "@umijs/use-params" "^1.0.9"
    "classnames" "^2.2.6"
    "lodash.merge" "^4.6.2"
    "omit.js" "^2.0.2"
    "rc-resize-observer" "^1.1.0"
    "rc-util" "^5.0.6"
    "use-json-comparison" "^1.0.5"
    "use-media-antd-query" "^1.1.0"

"@ant-design/pro-layout@^6.0.0":
  "integrity" "sha512-rsxY2E0BTyRE7IO67VACWVPSfdtDnTaWtECuMnc4ctL+xmnzbnnr0zEHVQ+S58BxLnCqO3IyOsSc0nFl2hHsnQ=="
  "resolved" "https://registry.npmjs.org/@ant-design/pro-layout/-/pro-layout-6.38.22.tgz"
  "version" "6.38.22"
  dependencies:
    "@ant-design/icons" "^4.0.0"
    "@ant-design/pro-provider" "1.10.0"
    "@ant-design/pro-utils" "1.45.3"
    "@babel/runtime" "^7.18.0"
    "@umijs/route-utils" "^2.1.0"
    "@umijs/ssr-darkreader" "^4.9.44"
    "@umijs/use-params" "^1.0.9"
    "antd" "^4.20.0 "
    "classnames" "^2.2.6"
    "lodash.merge" "^4.6.2"
    "omit.js" "^2.0.2"
    "path-to-regexp" "2.4.0"
    "rc-resize-observer" "^1.1.0"
    "rc-util" "^5.0.6"
    "swr" "^1.2.0"
    "unstated-next" "^1.1.0"
    "use-json-comparison" "^1.0.3"
    "use-media-antd-query" "^1.1.0"
    "warning" "^4.0.3"

"@ant-design/pro-layout@7.5.2":
  "integrity" "sha512-3FbAe73JmleF/6l89TrRT74/Q/JUtig/5qaqkZSqIgIHVzjTUN0kWMERrQzvfeQ7qoodSFlTmkUVhrRTsWIN7A=="
  "resolved" "https://registry.npmjs.org/@ant-design/pro-layout/-/pro-layout-7.5.2.tgz"
  "version" "7.5.2"
  dependencies:
    "@ant-design/icons" "^4.0.0"
    "@ant-design/pro-provider" "2.3.1"
    "@ant-design/pro-utils" "2.5.2"
    "@babel/runtime" "^7.18.0"
    "@umijs/route-utils" "^2.1.0"
    "@umijs/use-params" "^1.0.9"
    "classnames" "^2.2.6"
    "lodash.merge" "^4.6.2"
    "omit.js" "^2.0.2"
    "path-to-regexp" "2.4.0"
    "rc-resize-observer" "^1.1.0"
    "rc-util" "^5.0.6"
    "swr" "^2.0.0"
    "unstated-next" "^1.1.0"
    "use-json-comparison" "^1.0.3"
    "use-media-antd-query" "^1.1.0"
    "warning" "^4.0.3"

"@ant-design/pro-list@2.0.43":
  "integrity" "sha512-WeCxX9M8nNb2im91Rkatb3zONMsCuOCOA80HmU9e5D/8KVCIyexSfx5ysIXhDk649tPUrAOtIdmTBsv1y27n9Q=="
  "resolved" "https://registry.npmjs.org/@ant-design/pro-list/-/pro-list-2.0.43.tgz"
  "version" "2.0.43"
  dependencies:
    "@ant-design/icons" "^4.0.0"
    "@ant-design/pro-card" "2.1.8"
    "@ant-design/pro-field" "2.2.3"
    "@ant-design/pro-table" "3.2.10"
    "@babel/runtime" "^7.18.0"
    "classnames" "^2.2.6"
    "dayjs" "^1.11.4"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^4.19.0"
    "unstated-next" "^1.1.0"
    "use-media-antd-query" "^1.1.0"

"@ant-design/pro-provider@1.10.0":
  "integrity" "sha512-gbQtq+Qlnob6aEghiWX/kXEzNhe4eOFlv5Ue2/xsal+22qvot/kFoOB652TD76fMI7vlkoema1pTKsvD5Uji6A=="
  "resolved" "https://registry.npmjs.org/@ant-design/pro-provider/-/pro-provider-1.10.0.tgz"
  "version" "1.10.0"
  dependencies:
    "@babel/runtime" "^7.18.0"
    "antd" "^4.20.0 "
    "rc-util" "^5.0.1"
    "swr" "^1.2.0"

"@ant-design/pro-provider@2.3.1":
  "integrity" "sha512-fznRzpg+7s76FcPAoL25+5xXq2iU1G5Ub3YFmHP1wXcugazHVKCPOGIwGFFTe5zmu1omOCh5eDDNvtBbWp1IBA=="
  "resolved" "https://registry.npmjs.org/@ant-design/pro-provider/-/pro-provider-2.3.1.tgz"
  "version" "2.3.1"
  dependencies:
    "@ant-design/cssinjs" "^1.0.0"
    "@babel/runtime" "^7.18.0"
    "@ctrl/tinycolor" "^3.4.0"
    "rc-util" "^5.0.1"
    "swr" "^2.0.0"

"@ant-design/pro-skeleton@2.0.7":
  "integrity" "sha512-7RoCUVgWDTGrWeNuFlscsxrrXc5jshYXk6gjkQKospR0hQ1AK/aWlkttAAaqDFZlvPxSyLu8w11TmquRuYcNcg=="
  "resolved" "https://registry.npmjs.org/@ant-design/pro-skeleton/-/pro-skeleton-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "@babel/runtime" "^7.18.0"
    "use-media-antd-query" "^1.1.0"

"@ant-design/pro-table@3.2.10":
  "integrity" "sha512-faXvcyJEX2171g4WnhBOSJJVlI9rT3vMwhBE2TH+tmo7jN4V2UDkYnzaexaCX9DvLqzv0K/H32C4szCRWG7YfQ=="
  "resolved" "https://registry.npmjs.org/@ant-design/pro-table/-/pro-table-3.2.10.tgz"
  "version" "3.2.10"
  dependencies:
    "@ant-design/icons" "^4.1.0"
    "@ant-design/pro-card" "2.1.8"
    "@ant-design/pro-field" "2.2.3"
    "@ant-design/pro-form" "2.5.2"
    "@ant-design/pro-provider" "2.3.1"
    "@ant-design/pro-utils" "2.5.2"
    "@babel/runtime" "^7.18.0"
    "classnames" "^2.2.6"
    "dayjs" "^1.11.4"
    "omit.js" "^2.0.2"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^5.0.1"
    "react-sortable-hoc" "^2.0.0"
    "unstated-next" "^1.1.0"
    "use-json-comparison" "^1.0.5"

"@ant-design/pro-utils@1.45.3":
  "integrity" "sha512-W2qtyckdkHTWGVXfFfqb9s/ahq3o4GlIZC3+WDiGUgqJTsUkwY69yV67E2ZfAro1XRXblUzbvxIv96M7bEAfFg=="
  "resolved" "https://registry.npmjs.org/@ant-design/pro-utils/-/pro-utils-1.45.3.tgz"
  "version" "1.45.3"
  dependencies:
    "@ant-design/icons" "^4.3.0"
    "@ant-design/pro-provider" "1.10.0"
    "@babel/runtime" "^7.18.0"
    "antd" "^4.20.0 "
    "classnames" "^2.2.6"
    "moment" "^2.27.0"
    "rc-util" "^5.0.6"
    "react-sortable-hoc" "^2.0.0"
    "swr" "^1.2.0"

"@ant-design/pro-utils@2.5.2":
  "integrity" "sha512-V7Lhg23/N1cEB9SIdkOgTJTWV64OeomReLOyjU9dnrv6fW+uNH6dTAgN+D1y5QrGAH/Nzn/ohReHSnPD1eC4SA=="
  "resolved" "https://registry.npmjs.org/@ant-design/pro-utils/-/pro-utils-2.5.2.tgz"
  "version" "2.5.2"
  dependencies:
    "@ant-design/icons" "^4.3.0"
    "@ant-design/pro-provider" "2.3.1"
    "@babel/runtime" "^7.18.0"
    "classnames" "^2.2.6"
    "dayjs" "^1.11.4"
    "rc-util" "^5.0.6"
    "react-sortable-hoc" "^2.0.0"
    "swr" "^2.0.0"

"@ant-design/react-slick@~0.29.1":
  "integrity" "sha512-kgjtKmkGHa19FW21lHnAfyyH9AAoh35pBdcJ53rHmQ3O+cfFHGHnUbj/HFrRNJ5vIts09FKJVAD8RpaC+RaWfA=="
  "resolved" "https://registry.npmjs.org/@ant-design/react-slick/-/react-slick-0.29.2.tgz"
  "version" "0.29.2"
  dependencies:
    "@babel/runtime" "^7.10.4"
    "classnames" "^2.2.5"
    "json2mq" "^0.2.0"
    "lodash" "^4.17.21"
    "resize-observer-polyfill" "^1.5.1"

"@ant-design/react-slick@~1.0.0", "@ant-design/react-slick@~1.0.2":
  "integrity" "sha512-Wj8onxL/T8KQLFFiCA4t8eIRGpRR+UPgOdac2sYzonv+i0n3kXHmvHLLiOYL655DQx2Umii9Y9nNgL7ssu5haQ=="
  "resolved" "https://registry.npmjs.org/@ant-design/react-slick/-/react-slick-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "@babel/runtime" "^7.10.4"
    "classnames" "^2.2.5"
    "json2mq" "^0.2.0"
    "resize-observer-polyfill" "^1.5.1"
    "throttle-debounce" "^5.0.0"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.18.6", "@babel/code-frame@^7.22.5":
  "integrity" "sha512-Xmwn266vad+6DAqEB2A6V/CcZVp62BbwVmcOJc2RPuwih1kw02TjQvWVWlcKGbBPd+8/0V5DEkOcizRGYsspYQ=="
  "resolved" "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.22.5.tgz"
  "version" "7.22.5"
  dependencies:
    "@babel/highlight" "^7.22.5"

"@babel/compat-data@^7.22.9":
  "integrity" "sha512-5UamI7xkUcJ3i9qVDS+KFDEK8/7oJ55/sJMB1Ge7IEapr7KfdfV/HErR+koZwOfd+SgtFKOKRhRakdg++DcJpQ=="
  "resolved" "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.22.9.tgz"
  "version" "7.22.9"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.1.0", "@babel/core@^7.12.3", "@babel/core@^7.7.5", "@babel/core@7.18.6":
  "integrity" "sha512-cQbWBpxcbbs/IUredIPkHiAGULLV8iwgNRMFzvbhEXISp4f3rUUXE5+TIw6KwUWUR3DwyI6gmBRnmAtYaWehwQ=="
  "resolved" "https://registry.npmjs.org/@babel/core/-/core-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@ampproject/remapping" "^2.1.0"
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.18.6"
    "@babel/helper-compilation-targets" "^7.18.6"
    "@babel/helper-module-transforms" "^7.18.6"
    "@babel/helpers" "^7.18.6"
    "@babel/parser" "^7.18.6"
    "@babel/template" "^7.18.6"
    "@babel/traverse" "^7.18.6"
    "@babel/types" "^7.18.6"
    "convert-source-map" "^1.7.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.2"
    "json5" "^2.2.1"
    "semver" "^6.3.0"

"@babel/generator@^7.18.6", "@babel/generator@^7.22.7":
  "integrity" "sha512-KtLMbmicyuK2Ak/FTCJVbDnkN1SlT8/kceFTiuDiiRUUSMnHMidxSCdG4ndkTOHHpoomWe/4xkvHkEOncwjYIw=="
  "resolved" "https://registry.npmjs.org/@babel/generator/-/generator-7.22.9.tgz"
  "version" "7.22.9"
  dependencies:
    "@babel/types" "^7.22.5"
    "@jridgewell/gen-mapping" "^0.3.2"
    "@jridgewell/trace-mapping" "^0.3.17"
    "jsesc" "^2.5.1"

"@babel/helper-compilation-targets@^7.18.6":
  "integrity" "sha512-7qYrNM6HjpnPHJbopxmb8hSPoZ0gsX8IvUS32JGVoy+pU9e5N0nLr1VjJoR6kA4d9dmGLxNYOjeB8sUDal2WMw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.22.9.tgz"
  "version" "7.22.9"
  dependencies:
    "@babel/compat-data" "^7.22.9"
    "@babel/helper-validator-option" "^7.22.5"
    "browserslist" "^4.21.9"
    "lru-cache" "^5.1.1"
    "semver" "^6.3.1"

"@babel/helper-environment-visitor@^7.22.5":
  "integrity" "sha512-XGmhECfVA/5sAt+H+xpSg0mfrHq6FzNr9Oxh7PSEBBRUb/mL7Kz3NICXb194rCqAEdxkhPT1a88teizAFyvk8Q=="
  "resolved" "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.22.5.tgz"
  "version" "7.22.5"

"@babel/helper-function-name@^7.22.5":
  "integrity" "sha512-wtHSq6jMRE3uF2otvfuD3DIvVhOsSNshQl0Qrd7qC9oQJzHvOL4qQXlQn2916+CXGywIjpGuIkoyZRRxHPiNQQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.22.5.tgz"
  "version" "7.22.5"
  dependencies:
    "@babel/template" "^7.22.5"
    "@babel/types" "^7.22.5"

"@babel/helper-hoist-variables@^7.22.5":
  "integrity" "sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.22.5.tgz"
  "version" "7.22.5"
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-module-imports@^7.22.5":
  "integrity" "sha512-8Dl6+HD/cKifutF5qGd/8ZJi84QeAKh+CEe1sBzz8UayBBGg1dAIJrdHOcOM5b2MpzWL2yuotJTtGjETq0qjXg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.22.5.tgz"
  "version" "7.22.5"
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-module-transforms@^7.18.6":
  "integrity" "sha512-t+WA2Xn5K+rTeGtC8jCsdAH52bjggG5TKRuRrAGNM/mjIbO4GxvlLMFOEz9wXY5I2XQ60PMFsAG2WIcG82dQMQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.22.9.tgz"
  "version" "7.22.9"
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.5"
    "@babel/helper-module-imports" "^7.22.5"
    "@babel/helper-simple-access" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.22.6"
    "@babel/helper-validator-identifier" "^7.22.5"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.8.0":
  "integrity" "sha512-uLls06UVKgFG9QD4OeFYLEGteMIAa5kpTPcFL28yuCIIzsf6ZyKZMllKVOCZFhiZ5ptnwX4mtKdWCBE/uT4amg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.22.5.tgz"
  "version" "7.22.5"

"@babel/helper-simple-access@^7.22.5":
  "integrity" "sha512-n0H99E/K+Bika3++WNL17POvo4rKWZ7lZEp1Q+fStVbUi8nxPQEBOlTmCOxW/0JsS56SKKQ+ojAe2pHKJHN35w=="
  "resolved" "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.22.5.tgz"
  "version" "7.22.5"
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-split-export-declaration@^7.22.6":
  "integrity" "sha512-AsUnxuLhRYsisFiaJwvp1QF+I3KjD5FOxut14q/GzovUe6orHLesW2C7d754kRm53h5gqrz6sFl6sxc4BVtE/g=="
  "resolved" "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.22.6.tgz"
  "version" "7.22.6"
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-string-parser@^7.22.5":
  "integrity" "sha512-mM4COjgZox8U+JcXQwPijIZLElkgEpO5rsERVDJTc2qfCDfERyob6k5WegS14SX18IIjv+XD+GrqNumY5JRCDw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.22.5.tgz"
  "version" "7.22.5"

"@babel/helper-validator-identifier@^7.22.5":
  "integrity" "sha512-aJXu+6lErq8ltp+JhkJUfk1MTGyuA4v7f3pA+BJ5HLfNC6nAQ0Cpi9uOquUj8Hehg0aUiHzWQbOVJGao6ztBAQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.5.tgz"
  "version" "7.22.5"

"@babel/helper-validator-option@^7.22.5":
  "integrity" "sha512-R3oB6xlIVKUnxNUxbmgq7pKjxpru24zlimpE8WK47fACIlM0II/Hm1RS8IaOI7NgCr6LNS+jl5l75m20npAziw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.22.5.tgz"
  "version" "7.22.5"

"@babel/helpers@^7.18.6":
  "integrity" "sha512-YjDs6y/fVOYFV8hAf1rxd1QvR9wJe1pDBZ2AREKq/SDayfPzgk0PBnVuTCE5X1acEpMMNOVUqoe+OwiZGJ+OaA=="
  "resolved" "https://registry.npmjs.org/@babel/helpers/-/helpers-7.22.6.tgz"
  "version" "7.22.6"
  dependencies:
    "@babel/template" "^7.22.5"
    "@babel/traverse" "^7.22.6"
    "@babel/types" "^7.22.5"

"@babel/highlight@^7.22.5":
  "integrity" "sha512-BSKlD1hgnedS5XRnGOljZawtag7H1yPfQp0tdNJCHoH6AZ+Pcm9VvkrK59/Yy593Ypg0zMxH2BxD1VPYUQ7UIw=="
  "resolved" "https://registry.npmjs.org/@babel/highlight/-/highlight-7.22.5.tgz"
  "version" "7.22.5"
  dependencies:
    "@babel/helper-validator-identifier" "^7.22.5"
    "chalk" "^2.0.0"
    "js-tokens" "^4.0.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.14.7", "@babel/parser@^7.18.6", "@babel/parser@^7.20.7", "@babel/parser@^7.22.5", "@babel/parser@^7.22.7":
  "integrity" "sha512-7NF8pOkHP5o2vpmGgNGcfAeCvOYhGLyA3Z4eBQkT1RJlWu47n63bCs93QfJ2hIAFCil7L5P2IWhs1oToVgrL0Q=="
  "resolved" "https://registry.npmjs.org/@babel/parser/-/parser-7.22.7.tgz"
  "version" "7.22.7"

"@babel/plugin-syntax-async-generators@^7.8.4":
  "integrity" "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  "integrity" "sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.8.3":
  "integrity" "sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-import-meta@^7.8.3":
  "integrity" "sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  "integrity" "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-logical-assignment-operators@^7.8.3":
  "integrity" "sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  "integrity" "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.8.3":
  "integrity" "sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  "integrity" "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  "integrity" "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  "integrity" "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-top-level-await@^7.8.3":
  "integrity" "sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.1.2", "@babel/runtime@^7.10.1", "@babel/runtime@^7.10.4", "@babel/runtime@^7.11.1", "@babel/runtime@^7.11.2", "@babel/runtime@^7.12.1", "@babel/runtime@^7.12.5", "@babel/runtime@^7.15.4", "@babel/runtime@^7.16.3", "@babel/runtime@^7.16.7", "@babel/runtime@^7.18.0", "@babel/runtime@^7.18.3", "@babel/runtime@^7.2.0", "@babel/runtime@^7.20.0", "@babel/runtime@^7.20.7", "@babel/runtime@^7.21.0", "@babel/runtime@^7.24.8", "@babel/runtime@^7.9.2":
  "integrity" "sha512-FDSOghenHTiToteC/QRlv2q3DhPZ/oOXTBoirfWNx1Cx3TMVcGWQtMMmQcSvb/JjpNeGzx8Pq/b4fKEJuWm1sw=="
  "resolved" "https://registry.npmjs.org/@babel/runtime/-/runtime-7.26.0.tgz"
  "version" "7.26.0"
  dependencies:
    "regenerator-runtime" "^0.14.0"

"@babel/runtime@7.18.6":
  "integrity" "sha512-t9wi7/AW6XtKahAe20Yw0/mMljKq0B1r2fPdvaAdV/KPDZewFXdaaa6K7lxmZBZ8FBNpCiAT6iHPmd6QO9bKfQ=="
  "resolved" "https://registry.npmjs.org/@babel/runtime/-/runtime-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "regenerator-runtime" "^0.13.4"

"@babel/template@^7.18.6", "@babel/template@^7.22.5", "@babel/template@^7.3.3":
  "integrity" "sha512-X7yV7eiwAxdj9k94NEylvbVHLiVG1nvzCV2EAowhxLTwODV1jl9UzZ48leOC0sH7OnuHrIkllaBgneUykIcZaw=="
  "resolved" "https://registry.npmjs.org/@babel/template/-/template-7.22.5.tgz"
  "version" "7.22.5"
  dependencies:
    "@babel/code-frame" "^7.22.5"
    "@babel/parser" "^7.22.5"
    "@babel/types" "^7.22.5"

"@babel/traverse@^7.1.0", "@babel/traverse@^7.18.6", "@babel/traverse@^7.22.6":
  "integrity" "sha512-y6LPR+wpM2I3qJrsheCTwhIinzkETbplIgPBbwvqPKc+uljeA5gP+3nP8irdYt1mjQaDnlIcG+dw8OjAco4GXw=="
  "resolved" "https://registry.npmjs.org/@babel/traverse/-/traverse-7.22.8.tgz"
  "version" "7.22.8"
  dependencies:
    "@babel/code-frame" "^7.22.5"
    "@babel/generator" "^7.22.7"
    "@babel/helper-environment-visitor" "^7.22.5"
    "@babel/helper-function-name" "^7.22.5"
    "@babel/helper-hoist-variables" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.22.6"
    "@babel/parser" "^7.22.7"
    "@babel/types" "^7.22.5"
    "debug" "^4.1.0"
    "globals" "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.18.6", "@babel/types@^7.20.7", "@babel/types@^7.22.5", "@babel/types@^7.3.3":
  "integrity" "sha512-zo3MIHGOkPOfoRXitsgHLjEXmlDaD/5KU1Uzuc9GNiZPhSqVxVRtxuPaSBZDsYZ9qV88AjtMtWW7ww98loJ9KA=="
  "resolved" "https://registry.npmjs.org/@babel/types/-/types-7.22.5.tgz"
  "version" "7.22.5"
  dependencies:
    "@babel/helper-string-parser" "^7.22.5"
    "@babel/helper-validator-identifier" "^7.22.5"
    "to-fast-properties" "^2.0.0"

"@bcoe/v8-coverage@^0.2.3":
  "integrity" "sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw=="
  "resolved" "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz"
  "version" "0.2.3"

"@bloomberg/record-tuple-polyfill@0.0.3":
  "integrity" "sha512-sBnCqW0nqofE47mxFnw+lvx6kzsQstwaQMVkh66qm/A6IlsnH7WsyGuVXTou8RF2wL4W7ybOoHPvP2WgIo6rhQ=="
  "resolved" "https://registry.npmjs.org/@bloomberg/record-tuple-polyfill/-/record-tuple-polyfill-0.0.3.tgz"
  "version" "0.0.3"

"@chenshuai2144/sketch-color@^1.0.8":
  "integrity" "sha512-obzSy26cb7Pm7OprWyVpgMpIlrZpZ0B7vbrU0RMbvRg0YAI890S5Xy02Aj1Nhl4+KTbi1lVYHt6HQP8Hm9s+1w=="
  "resolved" "https://registry.npmjs.org/@chenshuai2144/sketch-color/-/sketch-color-1.0.9.tgz"
  "version" "1.0.9"
  dependencies:
    "reactcss" "^1.2.3"
    "tinycolor2" "^1.4.2"

"@cnakazawa/watch@^1.0.3":
  "integrity" "sha512-v9kIhKwjeZThiWrLmj0y17CWoyddASLj9O2yvbZkbvw/N3rWOYy9zkV66ursAoVr0mV15bL8g0c4QZUE6cdDoQ=="
  "resolved" "https://registry.npmjs.org/@cnakazawa/watch/-/watch-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "exec-sh" "^0.3.2"
    "minimist" "^1.2.0"

"@csstools/convert-colors@^1.4.0":
  "integrity" "sha512-5a6wqoJV/xEdbRNKVo6I4hO3VjyDq//8q2f9I6PBAvMesJHFauXDorcNCsr9RzvsZnaWi5NYCcfyqP1QeFHFbw=="
  "resolved" "https://registry.npmjs.org/@csstools/convert-colors/-/convert-colors-1.4.0.tgz"
  "version" "1.4.0"

"@ctrl/tinycolor@^3.4.0", "@ctrl/tinycolor@^3.6.1":
  "integrity" "sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA=="
  "resolved" "https://registry.npmjs.org/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz"
  "version" "3.6.1"

"@emotion/hash@^0.8.0":
  "integrity" "sha512-kBJtf7PH6aWwZ6fka3zQ0p6SBYzx4fl1LoZXE2RrnYST9Xljm7WfKJrU4g/Xr3Beg72MLrp1AWNUmuYJTL7Cow=="
  "resolved" "https://registry.npmjs.org/@emotion/hash/-/hash-0.8.0.tgz"
  "version" "0.8.0"

"@emotion/unitless@^0.7.5":
  "integrity" "sha512-OWORNpfjMsSSUBVrRBVGECkhWcULOAJz9ZW8uK9qgxD+87M7jHRcvh/A96XXNhXTLmKcoYSQtBEX7lHMO7YRwg=="
  "resolved" "https://registry.npmjs.org/@emotion/unitless/-/unitless-0.7.5.tgz"
  "version" "0.7.5"

"@formatjs/intl-displaynames@^1.2.0":
  "integrity" "sha512-GROA2RP6+7Ouu0WnHFF78O5XIU7pBfI19WM1qm93l6MFWibUk67nCfVCK3VAYJkLy8L8ZxjkYT11VIAfvSz8wg=="
  "resolved" "https://registry.npmjs.org/@formatjs/intl-displaynames/-/intl-displaynames-1.2.10.tgz"
  "version" "1.2.10"
  dependencies:
    "@formatjs/intl-utils" "^2.3.0"

"@formatjs/intl-listformat@^1.4.1":
  "integrity" "sha512-WNMQlEg0e50VZrGIkgD5n7+DAMGt3boKi1GJALfhFMymslJb5i+5WzWxyj/3a929Z6MAFsmzRIJjKuv+BxKAOQ=="
  "resolved" "https://registry.npmjs.org/@formatjs/intl-listformat/-/intl-listformat-1.4.8.tgz"
  "version" "1.4.8"
  dependencies:
    "@formatjs/intl-utils" "^2.3.0"

"@formatjs/intl-pluralrules@^1.5.0":
  "integrity" "sha512-37E1ZG+Oqo3qrpUfumzNcFTV+V+NCExmTkkQ9Zw4FSlvJ4WhbbeYdieVapUVz9M0cLy8XrhCkfuM/Kn03iKReg=="
  "resolved" "https://registry.npmjs.org/@formatjs/intl-pluralrules/-/intl-pluralrules-1.5.9.tgz"
  "version" "1.5.9"
  dependencies:
    "@formatjs/intl-utils" "^2.3.0"

"@formatjs/intl-relativetimeformat@^4.5.7", "@formatjs/intl-relativetimeformat@^4.5.9":
  "integrity" "sha512-IQ0haY97oHAH5OYUdykNiepdyEWj3SAT+Fp9ZpR85ov2JNiFx+12WWlxlVS8ehdyncC2ZMt/SwFIy2huK2+6/A=="
  "resolved" "https://registry.npmjs.org/@formatjs/intl-relativetimeformat/-/intl-relativetimeformat-4.5.16.tgz"
  "version" "4.5.16"
  dependencies:
    "@formatjs/intl-utils" "^2.3.0"

"@formatjs/intl-unified-numberformat@^3.2.0":
  "integrity" "sha512-KnWgLRHzCAgT9eyt3OS34RHoyD7dPDYhRcuKn+/6Kv2knDF8Im43J6vlSW6Hm1w63fNq3ZIT1cFk7RuVO3Psag=="
  "resolved" "https://registry.npmjs.org/@formatjs/intl-unified-numberformat/-/intl-unified-numberformat-3.3.7.tgz"
  "version" "3.3.7"
  dependencies:
    "@formatjs/intl-utils" "^2.3.0"

"@formatjs/intl-utils@^2.2.0", "@formatjs/intl-utils@^2.3.0":
  "integrity" "sha512-KWk80UPIzPmUg+P0rKh6TqspRw0G6eux1PuJr+zz47ftMaZ9QDwbGzHZbtzWkl5hgayM/qrKRutllRC7D/vVXQ=="
  "resolved" "https://registry.npmjs.org/@formatjs/intl-utils/-/intl-utils-2.3.0.tgz"
  "version" "2.3.0"

"@istanbuljs/load-nyc-config@^1.0.0":
  "integrity" "sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ=="
  "resolved" "https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "camelcase" "^5.3.1"
    "find-up" "^4.1.0"
    "get-package-type" "^0.1.0"
    "js-yaml" "^3.13.1"
    "resolve-from" "^5.0.0"

"@istanbuljs/schema@^0.1.2":
  "integrity" "sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA=="
  "resolved" "https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz"
  "version" "0.1.3"

"@jest/console@^26.6.2":
  "integrity" "sha512-IY1R2i2aLsLr7Id3S6p2BA82GNWryt4oSvEXLAKc+L2zdi89dSkE8xC1C+0kpATG4JhBJREnQOH7/zmccM2B0g=="
  "resolved" "https://registry.npmjs.org/@jest/console/-/console-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "jest-message-util" "^26.6.2"
    "jest-util" "^26.6.2"
    "slash" "^3.0.0"

"@jest/core@^26.6.3":
  "integrity" "sha512-xvV1kKbhfUqFVuZ8Cyo+JPpipAHHAV3kcDBftiduK8EICXmTFddryy3P7NfZt8Pv37rA9nEJBKCCkglCPt/Xjw=="
  "resolved" "https://registry.npmjs.org/@jest/core/-/core-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/reporters" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "ansi-escapes" "^4.2.1"
    "chalk" "^4.0.0"
    "exit" "^0.1.2"
    "graceful-fs" "^4.2.4"
    "jest-changed-files" "^26.6.2"
    "jest-config" "^26.6.3"
    "jest-haste-map" "^26.6.2"
    "jest-message-util" "^26.6.2"
    "jest-regex-util" "^26.0.0"
    "jest-resolve" "^26.6.2"
    "jest-resolve-dependencies" "^26.6.3"
    "jest-runner" "^26.6.3"
    "jest-runtime" "^26.6.3"
    "jest-snapshot" "^26.6.2"
    "jest-util" "^26.6.2"
    "jest-validate" "^26.6.2"
    "jest-watcher" "^26.6.2"
    "micromatch" "^4.0.2"
    "p-each-series" "^2.1.0"
    "rimraf" "^3.0.0"
    "slash" "^3.0.0"
    "strip-ansi" "^6.0.0"

"@jest/environment@^26.6.2":
  "integrity" "sha512-nFy+fHl28zUrRsCeMB61VDThV1pVTtlEokBRgqPrcT1JNq4yRNIyTHfyht6PqtUvY9IsuLGTrbG8kPXjSZIZwA=="
  "resolved" "https://registry.npmjs.org/@jest/environment/-/environment-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "jest-mock" "^26.6.2"

"@jest/fake-timers@^26.6.2":
  "integrity" "sha512-14Uleatt7jdzefLPYM3KLcnUl1ZNikaKq34enpb5XG9i81JpppDb5muZvonvKyrl7ftEHkKS5L5/eB/kxJ+bvA=="
  "resolved" "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "@sinonjs/fake-timers" "^6.0.1"
    "@types/node" "*"
    "jest-message-util" "^26.6.2"
    "jest-mock" "^26.6.2"
    "jest-util" "^26.6.2"

"@jest/globals@^26.6.2":
  "integrity" "sha512-85Ltnm7HlB/KesBUuALwQ68YTU72w9H2xW9FjZ1eL1U3lhtefjjl5c2MiUbpXt/i6LaPRvoOFJ22yCBSfQ0JIA=="
  "resolved" "https://registry.npmjs.org/@jest/globals/-/globals-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/types" "^26.6.2"
    "expect" "^26.6.2"

"@jest/reporters@^26.6.2":
  "integrity" "sha512-h2bW53APG4HvkOnVMo8q3QXa6pcaNt1HkwVsOPMBV6LD/q9oSpxNSYZQYkAnjdMjrJ86UuYeLo+aEZClV6opnw=="
  "resolved" "https://registry.npmjs.org/@jest/reporters/-/reporters-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "chalk" "^4.0.0"
    "collect-v8-coverage" "^1.0.0"
    "exit" "^0.1.2"
    "glob" "^7.1.2"
    "graceful-fs" "^4.2.4"
    "istanbul-lib-coverage" "^3.0.0"
    "istanbul-lib-instrument" "^4.0.3"
    "istanbul-lib-report" "^3.0.0"
    "istanbul-lib-source-maps" "^4.0.0"
    "istanbul-reports" "^3.0.2"
    "jest-haste-map" "^26.6.2"
    "jest-resolve" "^26.6.2"
    "jest-util" "^26.6.2"
    "jest-worker" "^26.6.2"
    "slash" "^3.0.0"
    "source-map" "^0.6.0"
    "string-length" "^4.0.1"
    "terminal-link" "^2.0.0"
    "v8-to-istanbul" "^7.0.0"
  optionalDependencies:
    "node-notifier" "^8.0.0"

"@jest/source-map@^26.6.2":
  "integrity" "sha512-YwYcCwAnNmOVsZ8mr3GfnzdXDAl4LaenZP5z+G0c8bzC9/dugL8zRmxZzdoTl4IaS3CryS1uWnROLPFmb6lVvA=="
  "resolved" "https://registry.npmjs.org/@jest/source-map/-/source-map-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "callsites" "^3.0.0"
    "graceful-fs" "^4.2.4"
    "source-map" "^0.6.0"

"@jest/test-result@^26.6.2":
  "integrity" "sha512-5O7H5c/7YlojphYNrK02LlDIV2GNPYisKwHm2QTKjNZeEzezCbwYs9swJySv2UfPMyZ0VdsmMv7jIlD/IKYQpQ=="
  "resolved" "https://registry.npmjs.org/@jest/test-result/-/test-result-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "collect-v8-coverage" "^1.0.0"

"@jest/test-sequencer@^26.6.3":
  "integrity" "sha512-YHlVIjP5nfEyjlrSr8t/YdNfU/1XEt7c5b4OxcXCjyRhjzLYu/rO69/WHPuYcbCWkz8kAeZVZp2N2+IOLLEPGw=="
  "resolved" "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@jest/test-result" "^26.6.2"
    "graceful-fs" "^4.2.4"
    "jest-haste-map" "^26.6.2"
    "jest-runner" "^26.6.3"
    "jest-runtime" "^26.6.3"

"@jest/transform@^26.6.2":
  "integrity" "sha512-E9JjhUgNzvuQ+vVAL21vlyfy12gP0GhazGgJC4h6qUt1jSdUXGWJ1wfu/X7Sd8etSgxV4ovT1pb9v5D6QW4XgA=="
  "resolved" "https://registry.npmjs.org/@jest/transform/-/transform-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/types" "^26.6.2"
    "babel-plugin-istanbul" "^6.0.0"
    "chalk" "^4.0.0"
    "convert-source-map" "^1.4.0"
    "fast-json-stable-stringify" "^2.0.0"
    "graceful-fs" "^4.2.4"
    "jest-haste-map" "^26.6.2"
    "jest-regex-util" "^26.0.0"
    "jest-util" "^26.6.2"
    "micromatch" "^4.0.2"
    "pirates" "^4.0.1"
    "slash" "^3.0.0"
    "source-map" "^0.6.1"
    "write-file-atomic" "^3.0.0"

"@jest/types@^26.6.2":
  "integrity" "sha512-fC6QCp7Sc5sX6g8Tvbmj4XUTbyrik0akgRy03yjXbQaBWWNWGE7SGtJk98m0N8nzegD/7SggrUlivxo5ax4KWQ=="
  "resolved" "https://registry.npmjs.org/@jest/types/-/types-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^15.0.0"
    "chalk" "^4.0.0"

"@jridgewell/gen-mapping@^0.3.0", "@jridgewell/gen-mapping@^0.3.2":
  "integrity" "sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ=="
  "resolved" "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@3.1.0":
  "integrity" "sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w=="
  "resolved" "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz"
  "version" "3.1.0"

"@jridgewell/set-array@^1.0.1":
  "integrity" "sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw=="
  "resolved" "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz"
  "version" "1.1.2"

"@jridgewell/source-map@^0.3.2":
  "integrity" "sha512-UTYAUj/wviwdsMfzoSJspJxbkH5o1snzwX0//0ENX1u/55kkZZkcTZP6u9bwKGkv+dkk9at4m1Cpt0uY80kcpQ=="
  "resolved" "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.5.tgz"
  "version" "0.3.5"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/sourcemap-codec@^1.4.10":
  "integrity" "sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg=="
  "resolved" "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz"
  "version" "1.4.15"

"@jridgewell/sourcemap-codec@1.4.14":
  "integrity" "sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw=="
  "resolved" "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz"
  "version" "1.4.14"

"@jridgewell/trace-mapping@^0.3.17", "@jridgewell/trace-mapping@^0.3.9":
  "integrity" "sha512-w+niJYzMHdd7USdiH2U6869nqhD2nbfZXND5Yp93qIbEmnDNk7PD48o+YchRVpzMU7M6jVCbenTR7PA1FLQ9pA=="
  "resolved" "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.18.tgz"
  "version" "0.3.18"
  dependencies:
    "@jridgewell/resolve-uri" "3.1.0"
    "@jridgewell/sourcemap-codec" "1.4.14"

"@qixian.cs/path-to-regexp@^6.1.0":
  "integrity" "sha512-2jIiLiVZB1jnY7IIRQKtoV8Gnr7XIhk4mC88ONGunZE3hYt5IHUG4BE/6+JiTBjjEWQLBeWnZB8hGpppkufiVw=="
  "resolved" "https://registry.npmjs.org/@qixian.cs/path-to-regexp/-/path-to-regexp-6.1.0.tgz"
  "version" "6.1.0"

"@rc-component/context@^1.3.0":
  "integrity" "sha512-6QdaCJ7Wn5UZLJs15IEfqy4Ru3OaL5ctqpQYWd5rlfV9wwzrzdt6+kgAQZV/qdB0MUPN4nhyBfRembQCIvBf+w=="
  "resolved" "https://registry.npmjs.org/@rc-component/context/-/context-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "rc-util" "^5.27.0"

"@rc-component/mini-decimal@^1.0.1":
  "integrity" "sha512-jS4E7T9Li2GuYwI6PyiVXmxTiM6b07rlD9Ge8uGZSCz3WlzcG5ZK7g5bbuKNeZ9pgUuPK/5guV781ujdVpm4HQ=="
  "resolved" "https://registry.npmjs.org/@rc-component/mini-decimal/-/mini-decimal-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "@babel/runtime" "^7.18.0"

"@rc-component/mutate-observer@^1.0.0":
  "integrity" "sha512-okqRJSfNisXdI6CUeOLZC5ukBW/8kir2Ii4PJiKpUt+3+uS7dxwJUMxsUZquxA1rQuL8YcEmKVp/TCnR+yUdZA=="
  "resolved" "https://registry.npmjs.org/@rc-component/mutate-observer/-/mutate-observer-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "@babel/runtime" "^7.18.0"
    "classnames" "^2.3.2"
    "rc-util" "^5.24.4"

"@rc-component/portal@^1.0.0-6", "@rc-component/portal@^1.0.0-8", "@rc-component/portal@^1.0.0-9", "@rc-component/portal@^1.0.2":
  "integrity" "sha512-tbXM9SB1r5FOuZjRCljERFByFiEUcMmCWMXLog/NmgCzlAzreXyf23Vei3ZpSMxSMavzPnhCovfZjZdmxS3d1w=="
  "resolved" "https://registry.npmjs.org/@rc-component/portal/-/portal-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "@babel/runtime" "^7.18.0"
    "classnames" "^2.3.2"
    "rc-util" "^5.24.4"

"@rc-component/portal@^1.1.1":
  "integrity" "sha512-6f813C0IsasTZms08kfA8kPAGxbbkYToa8ALaiDIGGECU4i9hj8Plgbx0sNJDrey3EtHO30hmdaxtT0138xZcg=="
  "resolved" "https://registry.npmjs.org/@rc-component/portal/-/portal-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@babel/runtime" "^7.18.0"
    "classnames" "^2.3.2"
    "rc-util" "^5.24.4"

"@rc-component/tour@~1.6.0":
  "integrity" "sha512-b/s7LCb7bW4wxpWfZyNpl7khHUzSyObSlsLaIScRGd+W/v1wFVk8F7gRytl/z8ik9ZSXbLWx9EvexIuHoO/RcQ=="
  "resolved" "https://registry.npmjs.org/@rc-component/tour/-/tour-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "@babel/runtime" "^7.18.0"
    "@rc-component/portal" "^1.0.0-9"
    "classnames" "^2.3.2"
    "rc-trigger" "^5.3.4"
    "rc-util" "^5.24.4"

"@sinonjs/commons@^1.7.0":
  "integrity" "sha512-Ky+XkAkqPZSm3NLBeUng77EBQl3cmeJhITaGHdYH8kjVB+aun3S4XBRti2zt17mtt0mIUDiNxYeoJm6drVvBJQ=="
  "resolved" "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.8.6.tgz"
  "version" "1.8.6"
  dependencies:
    "type-detect" "4.0.8"

"@sinonjs/fake-timers@^6.0.1":
  "integrity" "sha512-MZPUxrmFubI36XS1DI3qmI0YdN1gks62JtFZvxR67ljjSNCeK6U08Zx4msEWOXuofgqUt6zPHSi1H9fbjR/NRA=="
  "resolved" "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "@sinonjs/commons" "^1.7.0"

"@tootallnate/once@1":
  "integrity" "sha512-RbzJvlNzmRq5c3O09UipeuXno4tA1FE6ikOjxZK0tuxVv3412l64l5t1W5pj4+rJq9vpkm/kwiR07aZXnsKPxw=="
  "resolved" "https://registry.npmjs.org/@tootallnate/once/-/once-1.1.2.tgz"
  "version" "1.1.2"

"@types/babel__core@^7.0.0", "@types/babel__core@^7.1.7":
  "integrity" "sha512-aACu/U/omhdk15O4Nfb+fHgH/z3QsfQzpnvRZhYhThms83ZnAOZz7zZAWO7mn2yyNQaA4xTO8GLK3uqFU4bYYw=="
  "resolved" "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.1.tgz"
  "version" "7.20.1"
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  "integrity" "sha512-tFkciB9j2K755yrTALxD44McOrk+gfpIpvC3sxHjRawj6PfnQxrse4Clq5y/Rq+G3mrBurMax/lG8Qn2t9mSsg=="
  "resolved" "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.6.4.tgz"
  "version" "7.6.4"
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  "integrity" "sha512-azBFKemX6kMg5Io+/rdGT0dkGreboUVR0Cdm3fz9QJWpaQGJRQXl7C+6hOTCZcMll7KFyEQpgbYI2lHdsS4U7g=="
  "resolved" "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.1.tgz"
  "version" "7.4.1"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.4", "@types/babel__traverse@^7.0.6":
  "integrity" "sha512-MitHFXnhtgwsGZWtT68URpOvLN4EREih1u3QtQiN4VdAxWKRVvGCSvw/Qth0M0Qq3pJpnGOu5JaM/ydK7OGbqg=="
  "resolved" "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.1.tgz"
  "version" "7.20.1"
  dependencies:
    "@babel/types" "^7.20.7"

"@types/graceful-fs@^4.1.2":
  "integrity" "sha512-Sig0SNORX9fdW+bQuTEovKj3uHcUL6LQKbCrrqb1X7J6/ReAbhCXRAhc+SMejhLELFj2QcyuxmUooZ4bt5ReSw=="
  "resolved" "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.6.tgz"
  "version" "4.1.6"
  dependencies:
    "@types/node" "*"

"@types/history@*", "@types/history@^4.7.11":
  "integrity" "sha512-qjDJRrmvBMiTx+jyLxvLfJU7UznFuokDv4f3WRuriHKERccVpFU+8XMQUAbDzoiJCsmexxRExQeMwwCdamSKDA=="
  "resolved" "https://registry.npmjs.org/@types/history/-/history-4.7.11.tgz"
  "version" "4.7.11"

"@types/hoist-non-react-statics@^3.3.0", "@types/hoist-non-react-statics@^3.3.1":
  "integrity" "sha512-iMIqiko6ooLrTh1joXodJK5X9xeEALT1kM5G3ZLhD3hszxBdIEd5C75U834D9mLcINgD4OyZf5uQXjkuYydWvA=="
  "resolved" "https://registry.npmjs.org/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "@types/react" "*"
    "hoist-non-react-statics" "^3.3.0"

"@types/invariant@^2.2.31":
  "integrity" "sha512-DxX1V9P8zdJPYQat1gHyY0xj3efl8gnMVjiM9iCY6y27lj+PoQWkgjt8jDqmovPqULkKVpKRg8J36iQiA+EtEg=="
  "resolved" "https://registry.npmjs.org/@types/invariant/-/invariant-2.2.35.tgz"
  "version" "2.2.35"

"@types/isomorphic-fetch@^0.0.34":
  "integrity" "sha512-BmJKuPCZCR6pbYYgi5nKFJrPC4pLoBgsi/B1nFN64Ba+hLLGUcKPIh7eVlR2xG763Ap08hgQafq/Wx4RFb0omQ=="
  "resolved" "https://registry.npmjs.org/@types/isomorphic-fetch/-/isomorphic-fetch-0.0.34.tgz"
  "version" "0.0.34"

"@types/isomorphic-fetch@^0.0.35":
  "integrity" "sha512-DaZNUvLDCAnCTjgwxgiL1eQdxIKEpNLOlTNtAgnZc50bG2copGhRrFN9/PxPBuJe+tZVLCbQ7ls0xveXVRPkvw=="
  "resolved" "https://registry.npmjs.org/@types/isomorphic-fetch/-/isomorphic-fetch-0.0.35.tgz"
  "version" "0.0.35"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  "integrity" "sha512-z/QT1XN4K4KYuslS23k62yDIDLwLFkzxOuMplDtObz0+y7VqJCaO2o+SPwHCvLFZh7xazvvoor2tA/hPz9ee7g=="
  "resolved" "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.4.tgz"
  "version" "2.0.4"

"@types/istanbul-lib-report@*":
  "integrity" "sha512-plGgXAPfVKFoYfa9NpYDAkseG+g6Jr294RqeqcqDixSbU34MZVJRi/P+7Y8GDpzkEwLaGZZOpKIEmeVZNtKsrg=="
  "resolved" "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  "integrity" "sha512-c3mAZEuK0lvBp8tmuL74XRKn1+y2dcwOUpH7x4WrF6gk1GIgiluDRgMYQtw2OFcBvAJWlt6ASU3tSqxp0Uu0Aw=="
  "resolved" "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/json-schema@^7.0.8":
  "integrity" "sha512-Hr5Jfhc9eYOQNPYO5WLDq/n4jqijdHNlDXjuAQkkt+mWdQR+XJToOHrsD4cPaMXpn6KO7y2+wM8AZEs8VpBLVA=="
  "resolved" "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.12.tgz"
  "version" "7.0.12"

"@types/node@*":
  "integrity" "sha512-rt40Nk13II9JwQBdeYqmbn2Q6IVTA5uPhvSO+JVqdXw/6/4glI6oR9ezty/A9Hg5u7JH4OmYmuQ+XvjKm0Datg=="
  "resolved" "https://registry.npmjs.org/@types/node/-/node-20.4.5.tgz"
  "version" "20.4.5"

"@types/normalize-package-data@^2.4.0":
  "integrity" "sha512-Gj7cI7z+98M282Tqmp2K5EIsoouUEzbBJhQQzDE3jSIRk6r9gsz0oUokqIUR4u1R3dMHo0pDHM7sNOHyhulypw=="
  "resolved" "https://registry.npmjs.org/@types/normalize-package-data/-/normalize-package-data-2.4.1.tgz"
  "version" "2.4.1"

"@types/parse-json@^4.0.0":
  "integrity" "sha512-//oorEZjL6sbPcKUaCdIGlIUeH26mgzimjBB77G6XRgnDl/L5wOnpyBGRe/Mmf5CVW3PwEBE1NjiMZ/ssFh4wA=="
  "resolved" "https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.0.tgz"
  "version" "4.0.0"

"@types/prettier@^2.0.0":
  "integrity" "sha512-+68kP9yzs4LMp7VNh8gdzMSPZFL44MLGqiHWvttYJe+6qnuVr4Ek9wSBQoveqY/r+LwjCcU29kNVkidwim+kYA=="
  "resolved" "https://registry.npmjs.org/@types/prettier/-/prettier-2.7.3.tgz"
  "version" "2.7.3"

"@types/prop-types@*":
  "integrity" "sha512-JCB8C6SnDoQf0cNycqd/35A7MjcnK+ZTqE7judS6o7utxUCg6imJg3QK2qzHKszlTjcj2cn+NwMB2i96ubpj7w=="
  "resolved" "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.5.tgz"
  "version" "15.7.5"

"@types/qs@^6.9.7":
  "integrity" "sha512-FGa1F62FT09qcrueBA6qYTrJPVDzah9a+493+o2PCXsesWHIn27G98TsSMs3WPNbZIEj4+VJf6saSFpvD+3Zsw=="
  "resolved" "https://registry.npmjs.org/@types/qs/-/qs-6.9.7.tgz"
  "version" "6.9.7"

"@types/react-dom@^16.9.8":
  "integrity" "sha512-xC8D280Bf6p0zguJ8g62jcEOKZiUbx9sIe6O3tT/lKfR87A7A6g65q13z6D5QUMIa/6yFPkNhqjF5z/VVZEYqQ=="
  "resolved" "https://registry.npmjs.org/@types/react-dom/-/react-dom-16.9.19.tgz"
  "version" "16.9.19"
  dependencies:
    "@types/react" "^16"

"@types/react-dom@^17.0.0":
  "integrity" "sha512-rLVtIfbwyur2iFKykP2w0pl/1unw26b5td16d5xMgp7/yjTHomkyxPYChFoCr/FtEX1lN9wY6lFj1qvKdS5kDw=="
  "resolved" "https://registry.npmjs.org/@types/react-dom/-/react-dom-17.0.18.tgz"
  "version" "17.0.18"
  dependencies:
    "@types/react" "^17"

"@types/react-helmet@^6.1.0":
  "integrity" "sha512-ZKcoOdW/Tg+kiUbkFCBtvDw0k3nD4HJ/h/B9yWxN4uDO8OkRksWTO+EL+z/Qu3aHTeTll3Ro0Cc/8UhwBCMG5A=="
  "resolved" "https://registry.npmjs.org/@types/react-helmet/-/react-helmet-6.1.6.tgz"
  "version" "6.1.6"
  dependencies:
    "@types/react" "*"

"@types/react-redux@^7.1.0", "@types/react-redux@^7.1.20":
  "integrity" "sha512-NF8m5AjWCkert+fosDsN3hAlHzpjSiXlVy9EgQEmLoBhaNXbmyeGs/aj5dQzKuF+/q+S7JQagorGDW8pJ28Hmg=="
  "resolved" "https://registry.npmjs.org/@types/react-redux/-/react-redux-7.1.33.tgz"
  "version" "7.1.33"
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.0"
    "@types/react" "*"
    "hoist-non-react-statics" "^3.3.0"
    "redux" "^4.0.0"

"@types/react-router-config@^5.0.2":
  "integrity" "sha512-pFFVXUIydHlcJP6wJm7sDii5mD/bCmmAY0wQzq+M+uX7bqS95AQqHZWP1iNMKrWVQSuHIzj5qi9BvrtLX2/T4w=="
  "resolved" "https://registry.npmjs.org/@types/react-router-config/-/react-router-config-5.0.7.tgz"
  "version" "5.0.7"
  dependencies:
    "@types/history" "^4.7.11"
    "@types/react" "*"
    "@types/react-router" "^5.1.0"

"@types/react-router-config@5.0.2":
  "integrity" "sha512-WOSetDV3YPxbkVJAdv/bqExJjmcdCi/vpCJh3NfQOy1X15vHMSiMioXIcGekXDJJYhqGUMDo9e337mh508foAA=="
  "resolved" "https://registry.npmjs.org/@types/react-router-config/-/react-router-config-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "@types/history" "*"
    "@types/react" "*"
    "@types/react-router" "*"

"@types/react-router-dom@^4.2.7":
  "integrity" "sha512-eFajSUASYbPHg2BDM1G8Btx+YqGgvROPIg6sBhl3O4kbDdYXdFdfrgQFf/pcBuQVObjfT9AL/dd15jilR5DIEA=="
  "resolved" "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.3.5.tgz"
  "version" "4.3.5"
  dependencies:
    "@types/history" "*"
    "@types/react" "*"
    "@types/react-router" "*"

"@types/react-router-dom@^5.1.2", "@types/react-router-dom@5.1.7":
  "integrity" "sha512-D5mHD6TbdV/DNHYsnwBTv+y73ei+mMjrkGrla86HthE4/PVvL1J94Bu3qABU+COXzpL23T1EZapVVpwHuBXiUg=="
  "resolved" "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-5.1.7.tgz"
  "version" "5.1.7"
  dependencies:
    "@types/history" "*"
    "@types/react" "*"
    "@types/react-router" "*"

"@types/react-router-redux@^5.0.13":
  "integrity" "sha512-qC5lbuP2K/kMR/HE3e5ltCJptyiQhmfV0wbklqcqWDbNdpJBDwUsBGP4f/0RDYJf09+OTbz43u6iG+8E0Zcwqw=="
  "resolved" "https://registry.npmjs.org/@types/react-router-redux/-/react-router-redux-5.0.27.tgz"
  "version" "5.0.27"
  dependencies:
    "@types/history" "^4.7.11"
    "@types/react" "*"
    "@types/react-router" "^5.1.0"
    "redux" ">= 3.7.2"

"@types/react-router@*", "@types/react-router@^5.1.0":
  "integrity" "sha512-jGjmu/ZqS7FjSH6owMcD5qpq19+1RS9DeVRqfl1FeBMxTDQAGwlMWOcs52NDoXaNKyG3d1cYQFMs9rCrb88o9Q=="
  "resolved" "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.20.tgz"
  "version" "5.1.20"
  dependencies:
    "@types/history" "^4.7.11"
    "@types/react" "*"

"@types/react-router@5.1.12":
  "integrity" "sha512-0bhXQwHYfMeJlCh7mGhc0VJTRm0Gk+Z8T00aiP4702mDUuLs9SMhnd2DitpjWFjdOecx2UXtICK14H9iMnziGA=="
  "resolved" "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.12.tgz"
  "version" "5.1.12"
  dependencies:
    "@types/history" "*"
    "@types/react" "*"

"@types/react@*", "@types/react@^17", "@types/react@^17.0.0":
  "integrity" "sha512-1yIpQR2zdYu1Z/dc1OxC+MA6GR240u3gcnP4l6mvj/PJiVaqHsQPmWttsvHsfnhfPbU2FuGmo0wSITPygjBmsw=="
  "resolved" "https://registry.npmjs.org/@types/react/-/react-17.0.53.tgz"
  "version" "17.0.53"
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "*"
    "csstype" "^3.0.2"

"@types/react@^16", "@types/react@^16.9.43":
  "integrity" "sha512-7zdjv7jvoLLQg1tTvpQsm+hyNUMT2mPlNV1+d0I8fbGhkJl82spopMyBlu4wb1dviZAxpGdk5eHu/muacknnfw=="
  "resolved" "https://registry.npmjs.org/@types/react/-/react-16.14.43.tgz"
  "version" "16.14.43"
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "*"
    "csstype" "^3.0.2"

"@types/scheduler@*":
  "integrity" "sha512-5cJ8CB4yAx7BH1oMvdU0Jh9lrEXyPkar6F9G/ERswkCuvP4KQZfZkSjcMbAICCpQTN4OuZn8tz0HiKv9TGZgrQ=="
  "resolved" "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.3.tgz"
  "version" "0.16.3"

"@types/stack-utils@^2.0.0":
  "integrity" "sha512-Hl219/BT5fLAaz6NDkSuhzasy49dwQS/DSdu4MdggFB8zcXv7vflBI3xp7FEmkmdDkBUI2bPUNeMttp2knYdxw=="
  "resolved" "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.1.tgz"
  "version" "2.0.1"

"@types/yargs-parser@*":
  "integrity" "sha512-iO9ZQHkZxHn4mSakYV0vFHAVDyEOIJQrV2uZ06HxEPcx+mt8swXoZHIbaaJ2crJYFfErySgktuTZ3BeLz+XmFA=="
  "resolved" "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.0.tgz"
  "version" "21.0.0"

"@types/yargs@^15.0.0":
  "integrity" "sha512-IziEYMU9XoVj8hWg7k+UJrXALkGFjWJhn5QFEv9q4p+v40oZhSuC135M38st8XPjICL7Ey4TV64ferBGUoJhBg=="
  "resolved" "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.15.tgz"
  "version" "15.0.15"
  dependencies:
    "@types/yargs-parser" "*"

"@umijs/ast@3.5.36":
  "integrity" "sha512-bqxi6YGH9s7YvytdtD0jSeuJRdzf2pG9GXeY3jZdrhfUKwyzq7LCVnzDl0SbVEydjqbqn1tyWFQ3AQTE577vgg=="
  "resolved" "https://registry.npmjs.org/@umijs/ast/-/ast-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@umijs/utils" "3.5.36"

"@umijs/babel-plugin-auto-css-modules@3.5.36":
  "integrity" "sha512-GFFR3TetbrErFpjIgcrfdQch4J4/Y7EUPCj9YBvfLfhdsrVREIRo/vz8pWps9H+K0S19ZQzhH2fUcVDXM1sPfw=="
  "resolved" "https://registry.npmjs.org/@umijs/babel-plugin-auto-css-modules/-/babel-plugin-auto-css-modules-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@umijs/utils" "3.5.36"

"@umijs/babel-plugin-auto-css-modules@3.5.41":
  "integrity" "sha512-8iUcSFyLwRcRBsTW6hHuzDWX9Np+0AXqsFN08fnPI1P7zD4CSZWBLkjLz+zl4X8vakaPbqZy346KHoukbvEcew=="
  "resolved" "https://registry.npmjs.org/@umijs/babel-plugin-auto-css-modules/-/babel-plugin-auto-css-modules-3.5.41.tgz"
  "version" "3.5.41"
  dependencies:
    "@umijs/utils" "3.5.41"

"@umijs/babel-plugin-import-to-await-require@3.5.36":
  "integrity" "sha512-h3rTt9diVsziW+qVyVfcKrEjPiTjoiKnErv2Tl1mYrwAXC4spYaU6kkSvA2Nl35M0IcxRIx9nVADYJmuIXrIBA=="
  "resolved" "https://registry.npmjs.org/@umijs/babel-plugin-import-to-await-require/-/babel-plugin-import-to-await-require-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@umijs/utils" "3.5.36"

"@umijs/babel-plugin-import-to-await-require@3.5.41":
  "integrity" "sha512-xn9JhFc/vz/itTaLKC4NX4qcGfsBQQN03Kp8Bt5irXtp3CeBfeYFHjwcZ4TE4SJDTjka2wOR4RhmV3CJ2NZrQQ=="
  "resolved" "https://registry.npmjs.org/@umijs/babel-plugin-import-to-await-require/-/babel-plugin-import-to-await-require-3.5.41.tgz"
  "version" "3.5.41"
  dependencies:
    "@umijs/utils" "3.5.41"

"@umijs/babel-plugin-lock-core-js-3@3.5.36":
  "integrity" "sha512-lPWt5pCBITCnRhKxtLli5YYcE8INTDNEqk81UVS1Y4QhHTc4smmyvmEhr8rQ5gfa9alGL+/rnXAyxfEUhlGidQ=="
  "resolved" "https://registry.npmjs.org/@umijs/babel-plugin-lock-core-js-3/-/babel-plugin-lock-core-js-3-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@umijs/utils" "3.5.36"
    "core-js" "3.6.5"

"@umijs/babel-plugin-lock-core-js-3@3.5.41":
  "integrity" "sha512-+h47NI5u4YLA46iDv5TMPXtZWactlPgFYeMotrsxyW3R6QiUyX8oMF2UroT4lhbhcrf7GHe4n7jQH3TllgFL6Q=="
  "resolved" "https://registry.npmjs.org/@umijs/babel-plugin-lock-core-js-3/-/babel-plugin-lock-core-js-3-3.5.41.tgz"
  "version" "3.5.41"
  dependencies:
    "@umijs/utils" "3.5.41"
    "core-js" "3.6.5"

"@umijs/babel-plugin-no-anonymous-default-export@3.5.36":
  "integrity" "sha512-iLxN3LxFXSIFVedTjdoyl7VoEdfio7xgHsw5RHtclmDJ/ELHxYgCcMevDk/kpT8rr32ZA4eCDyTRGntodrqGUg=="
  "resolved" "https://registry.npmjs.org/@umijs/babel-plugin-no-anonymous-default-export/-/babel-plugin-no-anonymous-default-export-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@umijs/utils" "3.5.36"

"@umijs/babel-plugin-no-anonymous-default-export@3.5.41":
  "integrity" "sha512-uUoLNUsRcITEYW+ap8+OAjUYQS606Ob8YxqYCq0ZL4SktxjP5CDMabqrJ6CatWKjjpNvq2A6KPLJKA7/9/fwFA=="
  "resolved" "https://registry.npmjs.org/@umijs/babel-plugin-no-anonymous-default-export/-/babel-plugin-no-anonymous-default-export-3.5.41.tgz"
  "version" "3.5.41"
  dependencies:
    "@umijs/utils" "3.5.41"

"@umijs/babel-preset-umi@3.5.36":
  "integrity" "sha512-7Yhh6ZzdaY02SgtUvHgA9KBVdyIARDRlkGv1yNzfgSXAcHkjbs4Rp7mnsNoE1IYmm4/HzcNzJyN2N2N80U2HKw=="
  "resolved" "https://registry.npmjs.org/@umijs/babel-preset-umi/-/babel-preset-umi-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@babel/runtime" "7.18.6"
    "@umijs/babel-plugin-auto-css-modules" "3.5.36"
    "@umijs/babel-plugin-import-to-await-require" "3.5.36"
    "@umijs/babel-plugin-lock-core-js-3" "3.5.36"
    "@umijs/babel-plugin-no-anonymous-default-export" "3.5.36"
    "@umijs/deps" "3.5.36"
    "@umijs/utils" "3.5.36"

"@umijs/babel-preset-umi@3.5.41":
  "integrity" "sha512-roKx1FWjqGtuqQazfceBVJrJNdiGRRiBgnthZtGujLWIec4iB5uliwvzmCunuw18l3MvLwkDEBhaRdMM0Ij3Dg=="
  "resolved" "https://registry.npmjs.org/@umijs/babel-preset-umi/-/babel-preset-umi-3.5.41.tgz"
  "version" "3.5.41"
  dependencies:
    "@babel/runtime" "7.18.6"
    "@umijs/babel-plugin-auto-css-modules" "3.5.41"
    "@umijs/babel-plugin-import-to-await-require" "3.5.41"
    "@umijs/babel-plugin-lock-core-js-3" "3.5.41"
    "@umijs/babel-plugin-no-anonymous-default-export" "3.5.41"
    "@umijs/deps" "3.5.41"
    "@umijs/utils" "3.5.41"

"@umijs/bundler-utils@3.5.36":
  "integrity" "sha512-1TNEORqrsp2udJhyhctmxqOevbHeTAZM1jT6WWAgZH+1LPYsM/kevWi18R2z2Ql/tdaa3vl9Tb8H17pMY0g0vg=="
  "resolved" "https://registry.npmjs.org/@umijs/bundler-utils/-/bundler-utils-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@umijs/babel-preset-umi" "3.5.36"
    "@umijs/types" "3.5.36"
    "@umijs/utils" "3.5.36"

"@umijs/bundler-webpack@3.5.36":
  "integrity" "sha512-XOhSaJ12Yw8ChPe08RqiLmXc+CeRWjDASlWWtDWlaEWzsbn95sBOoihgli9RKt/aHH4fdJpwY2gdHLg7Ke3stw=="
  "resolved" "https://registry.npmjs.org/@umijs/bundler-webpack/-/bundler-webpack-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@umijs/bundler-utils" "3.5.36"
    "@umijs/case-sensitive-paths-webpack-plugin" "^1.0.1"
    "@umijs/deps" "3.5.36"
    "@umijs/types" "3.5.36"
    "@umijs/utils" "3.5.36"
    "jest-worker" "26.6.2"
    "node-libs-browser" "2.2.1"
    "normalize-url" "1.9.1"
    "postcss" "7.0.32"
    "postcss-flexbugs-fixes" "4.2.1"
    "postcss-loader" "3.0.0"
    "postcss-preset-env" "6.7.0"
    "postcss-safe-parser" "4.0.2"
    "terser" "5.14.2"
    "webpack-chain" "6.5.1"

"@umijs/case-sensitive-paths-webpack-plugin@^1.0.1":
  "integrity" "sha512-kDKJ8yTarxwxGJDInG33hOpaQRZ//XpNuuznQ/1Mscypw6kappzFmrBr2dOYave++K7JHouoANF354UpbEQw0Q=="
  "resolved" "https://registry.npmjs.org/@umijs/case-sensitive-paths-webpack-plugin/-/case-sensitive-paths-webpack-plugin-1.0.1.tgz"
  "version" "1.0.1"

"@umijs/core@3.5.36":
  "integrity" "sha512-e+ypIlYqGrj0uNKJ7TShqUMbDSmYjFPV7LPSdmIXc07Il6QYsZMtcGKmAS3qoxCHI3R8UdPUmbISd3D/Eq0J0Q=="
  "resolved" "https://registry.npmjs.org/@umijs/core/-/core-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@umijs/ast" "3.5.36"
    "@umijs/babel-preset-umi" "3.5.36"
    "@umijs/deps" "3.5.36"
    "@umijs/utils" "3.5.36"

"@umijs/deps@3.5.36":
  "integrity" "sha512-uuwjgQRvJaN72G844uiEIN3Xc4n6WJwY+TeWRijYUlzdrmjh9KwOj64iJS12ff4b+mGogA9AX9ord6DOVEtw3g=="
  "resolved" "https://registry.npmjs.org/@umijs/deps/-/deps-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@bloomberg/record-tuple-polyfill" "0.0.3"
    "chokidar" "3.5.1"
    "clipboardy" "2.3.0"
    "esbuild" "0.12.15"
    "jest-worker" "24.9.0"
    "prettier" "2.2.1"
    "regenerate-unicode-properties" "10.0.1"

"@umijs/deps@3.5.41":
  "integrity" "sha512-bMozsu97L0HJMoRtCevi81XvZ+nkJw2WMAlQuHnl5DsFYon9M+WljG88T/FX/x+lYfgdGY+fOuvcKWEazfA3SA=="
  "resolved" "https://registry.npmjs.org/@umijs/deps/-/deps-3.5.41.tgz"
  "version" "3.5.41"
  dependencies:
    "@bloomberg/record-tuple-polyfill" "0.0.3"
    "chokidar" "3.5.1"
    "clipboardy" "2.3.0"
    "esbuild" "0.12.15"
    "jest-worker" "24.9.0"
    "prettier" "2.2.1"
    "regenerate" "1.4.2"
    "regenerate-unicode-properties" "10.0.1"

"@umijs/plugin-access@2.4.3":
  "integrity" "sha512-6FON4aHzVFs/vtFK5Sdnw+FBolfoYi8wV8V9Q8x2T19NwjZtjXSPjt0gtrG4jdx4LRsSV2DKLaMGoJYbNr94aA=="
  "resolved" "https://registry.npmjs.org/@umijs/plugin-access/-/plugin-access-2.4.3.tgz"
  "version" "2.4.3"

"@umijs/plugin-analytics@0.2.2":
  "integrity" "sha512-dVDzUfgIdEwdCC6a5IsMYpIPI+bEZjBEqIhAvw9dic6Vk77w9RxQxyRfW11dDmdXLAwWphp22NntQNt1ejZPtg=="
  "resolved" "https://registry.npmjs.org/@umijs/plugin-analytics/-/plugin-analytics-0.2.2.tgz"
  "version" "0.2.2"

"@umijs/plugin-antd@0.13.0":
  "integrity" "sha512-7tooYtOylVatrzMWCJtk8JFQL90i94OD0FgZYpKBbM7keThH8prYkSkDJFIDkuGfZ6pl6BJT8ESnYLxf2OiQUw=="
  "resolved" "https://registry.npmjs.org/@umijs/plugin-antd/-/plugin-antd-0.13.0.tgz"
  "version" "0.13.0"
  dependencies:
    "antd" "^4.1.3"
    "antd-mobile" "^2.3.1"
    "semver" "^7.3.5"

"@umijs/plugin-crossorigin@1.2.1":
  "integrity" "sha512-4oXNFBLOCH1FHTgFi7EDmlcjjYMthOQlrTT+mSbNeMzvl0P/WKldSJsYAUXUTg+I7vlbb5bNby4dpHKK37JcbQ=="
  "resolved" "https://registry.npmjs.org/@umijs/plugin-crossorigin/-/plugin-crossorigin-1.2.1.tgz"
  "version" "1.2.1"

"@umijs/plugin-dva@0.13.0":
  "integrity" "sha512-cpGq8vzN8AmyWxexWLQ+WGqfT0WMCiryvU5EcNGbx1/Ub9aICUfPBpB46cxJrxjDCjUzuVslRNb2pdgI0xXUXg=="
  "resolved" "https://registry.npmjs.org/@umijs/plugin-dva/-/plugin-dva-0.13.0.tgz"
  "version" "0.13.0"
  dependencies:
    "babel-plugin-dva-hmr" "^0.4.2"
    "dva" "^2.6.0-beta.20"
    "dva-immer" "^0.5.2"
    "dva-loading" "^3.0.20"

"@umijs/plugin-helmet@1.1.3":
  "integrity" "sha512-X4GRFlvOb8DxMDm1vExfpWOvxl6bp0oZz114DyhwnMSriJ8RcHAigTjtVJHYxJpKv7P6KayN3n13J4w/XZGWMg=="
  "resolved" "https://registry.npmjs.org/@umijs/plugin-helmet/-/plugin-helmet-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "@types/react-helmet" "^6.1.0"
    "react-helmet" "^6.1.0"

"@umijs/plugin-initial-state@2.4.0", "@umijs/plugin-initial-state@2.x":
  "integrity" "sha512-UHH5fTL9skm4771R3xvWCrjd5xiQ7OdJ83G0P1YZLhuNbh0cAFVT8SPTU8dA9o8TTAZ66IEq7axhWl1cGxVsXw=="
  "resolved" "https://registry.npmjs.org/@umijs/plugin-initial-state/-/plugin-initial-state-2.4.0.tgz"
  "version" "2.4.0"

"@umijs/plugin-layout@0.18.1":
  "integrity" "sha512-15YTcFpic0v15nFzlg6G8nvMCveBtcBL77Wr9wXYJdgR0iU7dnO7T4OqbLwgEc1uHxtGhTVibi6nSGelX56zCg=="
  "resolved" "https://registry.npmjs.org/@umijs/plugin-layout/-/plugin-layout-0.18.1.tgz"
  "version" "0.18.1"
  dependencies:
    "@umijs/route-utils" "^2.0.0"
    "antd" "^4.1.2"
    "lodash" "^4.17.15"
    "path-to-regexp" "1.x"

"@umijs/plugin-locale@0.15.1", "@umijs/plugin-locale@0.x":
  "integrity" "sha512-5j2yg6KaaLVfNdWRdQ4IRsKUJAGcNDWKcdU+APXIlB8QZI9I5W+ctVC2cswftKt6sMiw1mQREyCqzjcwT0kvMg=="
  "resolved" "https://registry.npmjs.org/@umijs/plugin-locale/-/plugin-locale-0.15.1.tgz"
  "version" "0.15.1"
  dependencies:
    "@ant-design/icons" "^4.1.0"
    "@formatjs/intl-pluralrules" "^1.5.0"
    "@formatjs/intl-relativetimeformat" "^4.5.7"
    "intl" "1.2.5"
    "moment" "^2.29.1"
    "react-intl" "3.12.1"
    "warning" "^4.0.3"

"@umijs/plugin-model@2.6.2", "@umijs/plugin-model@2.x":
  "integrity" "sha512-MKLAgYIoBkciavxTqJY6VOhvnNQv7jp+olNmXqqZ3I3WsLg7jo33NCOAA3ej103kmo9ZvD9R1aakl7e9WKsdAw=="
  "resolved" "https://registry.npmjs.org/@umijs/plugin-model/-/plugin-model-2.6.2.tgz"
  "version" "2.6.2"
  dependencies:
    "fast-deep-equal" "3.1.1"

"@umijs/plugin-request@2.8.0":
  "integrity" "sha512-bwJg0rFbMZcizO+fHbSNz6X/U6suR84ciLIo9zLtRMuradx/yanMnz+ckh2dlZvTlGYrckkJwWgoSFxWlU8yyA=="
  "resolved" "https://registry.npmjs.org/@umijs/plugin-request/-/plugin-request-2.8.0.tgz"
  "version" "2.8.0"
  dependencies:
    "@ahooksjs/use-request" "^2.0.0"
    "umi-request" "^1.2.14"

"@umijs/plugin-test@1.0.2":
  "integrity" "sha512-EIV86Xmw/Cn8vRLXha+ct0PfY6gRRKov5lanvt1B2L5t6xAqnTd03AxmB7i1Go9vDcFxDBheuDf/nyGiT/xTFw=="
  "resolved" "https://registry.npmjs.org/@umijs/plugin-test/-/plugin-test-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "@umijs/test" "~3.5.14"

"@umijs/preset-built-in@3.5.36":
  "integrity" "sha512-Q9xIXb2J59jAurZxoPiWKAAbYrtssbBmDwQz4zOfCT0QsJldlHF1sJ7AU6sswJKJiQ/usxKpfu6u91W7QF0T2Q=="
  "resolved" "https://registry.npmjs.org/@umijs/preset-built-in/-/preset-built-in-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@types/react-router-config" "5.0.2"
    "@umijs/babel-preset-umi" "3.5.36"
    "@umijs/bundler-webpack" "3.5.36"
    "@umijs/core" "3.5.36"
    "@umijs/deps" "3.5.36"
    "@umijs/renderer-mpa" "3.5.36"
    "@umijs/renderer-react" "3.5.36"
    "@umijs/runtime" "3.5.36"
    "@umijs/server" "3.5.36"
    "@umijs/types" "3.5.36"
    "@umijs/utils" "3.5.36"
    "ansi-html" "^0.0.9"
    "core-js" "3.6.5"
    "core-js-pure" "^3.8.1"
    "error-stack-parser" "^2.0.6"
    "es-module-lexer" "0.7.1"
    "es5-imcompatible-versions" "^0.1.62"
    "history-with-query" "4.10.4"
    "html-entities" "^2.1.0"
    "mime" "1.4.1"
    "react-refresh" "0.10.0"
    "react-router" "5.2.0"
    "react-router-config" "5.1.1"
    "react-router-dom" "5.2.0"
    "regenerator-runtime" "0.13.5"
    "schema-utils" "^3.0.0"

"@umijs/preset-react@1.x":
  "integrity" "sha512-zQRYJw68mgG6pPcy6GwAXFRC/XxATIGmiaFAZHt7aYDy3VCccmjNKjs1pXqrcHYmGhoa7TyjCHUYEuJSv0n8uw=="
  "resolved" "https://registry.npmjs.org/@umijs/preset-react/-/preset-react-1.8.31.tgz"
  "version" "1.8.31"
  dependencies:
    "@umijs/plugin-access" "2.4.3"
    "@umijs/plugin-analytics" "0.2.2"
    "@umijs/plugin-antd" "0.13.0"
    "@umijs/plugin-crossorigin" "1.2.1"
    "@umijs/plugin-dva" "0.13.0"
    "@umijs/plugin-helmet" "1.1.3"
    "@umijs/plugin-initial-state" "2.4.0"
    "@umijs/plugin-layout" "0.18.1"
    "@umijs/plugin-locale" "0.15.1"
    "@umijs/plugin-model" "2.6.2"
    "@umijs/plugin-request" "2.8.0"
    "@umijs/plugin-test" "1.0.2"

"@umijs/renderer-mpa@3.5.36":
  "integrity" "sha512-AaNOW/67pe2fqL3p9xW5+3mKRgahFoT1NgdbYA3Ob8eVdxmdMUULkNzwzaJJjJUsJlwjWB5h51xXuMjvyC9E9A=="
  "resolved" "https://registry.npmjs.org/@umijs/renderer-mpa/-/renderer-mpa-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@types/react" "^16.9.43"
    "@types/react-dom" "^16.9.8"
    "@umijs/runtime" "3.5.36"

"@umijs/renderer-react@3.5.36":
  "integrity" "sha512-x72gyNkR3KyNpZYcMQE6m1ECRtQjlVsAVfItNjQIGJXj0fTmGEKl3oBXRP8t+mbnphgXWdQAb1IX7g4Zht4ulQ=="
  "resolved" "https://registry.npmjs.org/@umijs/renderer-react/-/renderer-react-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@types/react" "^16.9.43"
    "@types/react-dom" "^16.9.8"
    "@types/react-router-config" "^5.0.2"
    "@umijs/runtime" "3.5.36"
    "react-router-config" "5.1.1"

"@umijs/route-utils@^2.0.0", "@umijs/route-utils@^2.1.0":
  "integrity" "sha512-cMk6qizy0pfpiwpVCvNQB0BKBUJEH33pDv5q5k2tSleSDw2abkJkTu2Kd5hKzoESLuFK43oGeOfcplZqm2bRxw=="
  "resolved" "https://registry.npmjs.org/@umijs/route-utils/-/route-utils-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "@qixian.cs/path-to-regexp" "^6.1.0"
    "fast-deep-equal" "^3.1.3"
    "lodash.isequal" "^4.5.0"
    "memoize-one" "^5.1.1"

"@umijs/runtime@3.5.36":
  "integrity" "sha512-VRA7cT6iEvpdDkZImhU/WnbTirbDcAxedzoqVJL6SXnBofPsrynb6bDoz9H//Q7zCnd16Ll6Oxr9lnvjjf1/sg=="
  "resolved" "https://registry.npmjs.org/@umijs/runtime/-/runtime-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@types/react-router" "5.1.12"
    "@types/react-router-dom" "5.1.7"
    "history-with-query" "4.10.4"
    "react-router" "5.2.0"
    "react-router-dom" "5.2.0"
    "use-subscription" "1.5.1"

"@umijs/server@3.5.36":
  "integrity" "sha512-5l6KKJvSgqsxLF2g/i7mFNE/0onjh8QUcDAMoJt7TBDplmAzVwUqB+4XfWZyLktuxaSOW6qStyI1IJGsYfVbAg=="
  "resolved" "https://registry.npmjs.org/@umijs/server/-/server-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@umijs/core" "3.5.36"
    "@umijs/deps" "3.5.36"
    "@umijs/utils" "3.5.36"

"@umijs/ssr-darkreader@^4.9.44":
  "integrity" "sha512-XlcwzSYQ/SRZpHdwIyMDS4FOGX5kP4U/2g2mykyn/iPQTK4xTiQAyBu6UnnDnn7d5P8s7Atzh1C7H0ETNOypJg=="
  "resolved" "https://registry.npmjs.org/@umijs/ssr-darkreader/-/ssr-darkreader-4.9.45.tgz"
  "version" "4.9.45"

"@umijs/test@^3.5.36", "@umijs/test@~3.5.14":
  "integrity" "sha512-0Y2XuGwlFBd4Un3AbColgnt8GejYVN2dfYdzkdo6Tsn0BX2iFTtJiF1L1SwO4YqHob3oI/CMzpEcKCNkC041QQ=="
  "resolved" "https://registry.npmjs.org/@umijs/test/-/test-3.5.41.tgz"
  "version" "3.5.41"
  dependencies:
    "@babel/core" "7.18.6"
    "@umijs/babel-preset-umi" "3.5.41"
    "@umijs/utils" "3.5.41"
    "babel-jest" "^26.6.3"
    "core-js" "3.8.2"
    "identity-obj-proxy" "3.0.0"
    "jest" "^26.6.3"
    "jest-cli" "^26.6.3"
    "regenerator-runtime" "^0.13.7"
    "whatwg-fetch" "^3.5.0"

"@umijs/types@3.5.36":
  "integrity" "sha512-2FuJ+ZA+sXfHnOUBjDDAhYDiOO83ow68XnjYtp6ItG7QfbLAstDjijU1PCqd8mElqkUGGoAm86vJjCfSaumIjw=="
  "resolved" "https://registry.npmjs.org/@umijs/types/-/types-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@umijs/babel-preset-umi" "3.5.36"
    "@umijs/core" "3.5.36"
    "@umijs/deps" "3.5.36"
    "@umijs/renderer-react" "3.5.36"
    "@umijs/server" "3.5.36"
    "@umijs/utils" "3.5.36"
    "webpack-chain" "6.5.1"

"@umijs/use-params@^1.0.9":
  "integrity" "sha512-QlN0RJSBVQBwLRNxbxjQ5qzqYIGn+K7USppMoIOVlf7fxXHsnQZ2bEsa6Pm74bt6DVQxpUE8HqvdStn6Y9FV1w=="
  "resolved" "https://registry.npmjs.org/@umijs/use-params/-/use-params-1.0.9.tgz"
  "version" "1.0.9"

"@umijs/utils@3.5.36":
  "integrity" "sha512-z6c0UGYjxVOAkWRj1oshtjE2ZkzXkcMgNZ33TcvI4ve+HVt9/upc4hkic1OSM6z2She28d+itj3hWndkFt3X3Q=="
  "resolved" "https://registry.npmjs.org/@umijs/utils/-/utils-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@umijs/babel-preset-umi" "3.5.36"
    "@umijs/deps" "3.5.36"

"@umijs/utils@3.5.41":
  "integrity" "sha512-htBGGUfVUlEv5j9hUZ0tmVa9Jj1Z3vhFsUNKl2Luw7hWqTv86prWKPEcG2FWOjTTmE8iSK6r4dHmBSlSC5P6Tw=="
  "resolved" "https://registry.npmjs.org/@umijs/utils/-/utils-3.5.41.tgz"
  "version" "3.5.41"
  dependencies:
    "@umijs/babel-preset-umi" "3.5.41"
    "@umijs/deps" "3.5.41"

"abab@^2.0.3", "abab@^2.0.5":
  "integrity" "sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvHoRGU2zzP8cPoqys+xHTRDWW8L+/BA=="
  "resolved" "https://registry.npmjs.org/abab/-/abab-2.0.6.tgz"
  "version" "2.0.6"

"acorn-globals@^6.0.0":
  "integrity" "sha512-ZQl7LOWaF5ePqqcX4hLuv/bLXYQNfNWw2c0/yX/TsPRKamzHcTGQnlCjHT3TsmkOUVEPS3crCxiPfdzE/Trlhg=="
  "resolved" "https://registry.npmjs.org/acorn-globals/-/acorn-globals-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "acorn" "^7.1.1"
    "acorn-walk" "^7.1.1"

"acorn-walk@^7.1.1":
  "integrity" "sha512-OPdCF6GsMIP+Az+aWfAAOEt2/+iVDKE7oy6lJ098aoe59oAmK76qV6Gw60SbZ8jHuG2wH058GF4pLFbYamYrVA=="
  "resolved" "https://registry.npmjs.org/acorn-walk/-/acorn-walk-7.2.0.tgz"
  "version" "7.2.0"

"acorn@^7.1.1":
  "integrity" "sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-7.4.1.tgz"
  "version" "7.4.1"

"acorn@^8.2.4", "acorn@^8.5.0":
  "integrity" "sha512-xjIYgE8HBrkpd/sJqOGNspf8uHG+NOHGOw6a/Urj8taM2EXfdNAH2oFcPeIFfsv3+kz/mJrS5VuMqbNLjCa2vw=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-8.8.2.tgz"
  "version" "8.8.2"

"add-dom-event-listener@^1.1.0":
  "integrity" "sha512-WCxx1ixHT0GQU9hb0KI/mhgRQhnU+U3GvwY6ZvVjYq8rsihIGoaIOUbY0yMPBxLH5MDtr0kz3fisWGNcbWW7Jw=="
  "resolved" "https://registry.npmjs.org/add-dom-event-listener/-/add-dom-event-listener-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "object-assign" "4.x"

"adler-32@~1.3.0":
  "integrity" "sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A=="
  "resolved" "https://registry.npmmirror.com/adler-32/-/adler-32-1.3.1.tgz"
  "version" "1.3.1"

"agent-base@6":
  "integrity" "sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ=="
  "resolved" "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "debug" "4"

"aggregate-error@^3.0.0":
  "integrity" "sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA=="
  "resolved" "https://registry.npmjs.org/aggregate-error/-/aggregate-error-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "clean-stack" "^2.0.0"
    "indent-string" "^4.0.0"

"ajv-errors@^1.0.0":
  "integrity" "sha512-DCRfO/4nQ+89p/RK43i8Ezd41EqdGIU4ld7nGF8OQ14oc/we5rEntLCUa7+jrn3nn83BosfwZA0wb4pon2o8iQ=="
  "resolved" "https://registry.npmjs.org/ajv-errors/-/ajv-errors-1.0.1.tgz"
  "version" "1.0.1"

"ajv-keywords@^3.1.0", "ajv-keywords@^3.5.2":
  "integrity" "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ=="
  "resolved" "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  "version" "3.5.2"

"ajv@^6.1.0", "ajv@^6.12.5", "ajv@^6.9.1", "ajv@>=5.0.0":
  "integrity" "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ansi-colors@^4.1.1":
  "integrity" "sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw=="
  "resolved" "https://registry.npmjs.org/ansi-colors/-/ansi-colors-4.1.3.tgz"
  "version" "4.1.3"

"ansi-escapes@^4.2.1", "ansi-escapes@^4.3.0":
  "integrity" "sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ=="
  "resolved" "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "type-fest" "^0.21.3"

"ansi-html@^0.0.9":
  "integrity" "sha512-ozbS3LuenHVxNRh/wdnN16QapUHzauqSomAl1jwwJRRsGwFwtj644lIhxfWu0Fy0acCij2+AEgHvjscq3dlVXg=="
  "resolved" "https://registry.npmjs.org/ansi-html/-/ansi-html-0.0.9.tgz"
  "version" "0.0.9"

"ansi-regex@^5.0.0", "ansi-regex@^5.0.1":
  "integrity" "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  "version" "5.0.1"

"ansi-styles@^3.2.1":
  "integrity" "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^4.0.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ansi-styles@^4.1.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"antd-mobile@^2.3.1":
  "integrity" "sha512-Uw02Ghc+DPzaQceJQ+5p1ZnQFafvILA0chTTen7m7c89Uzbw6Ny3zsVZDE1gfteNEIsL4JpPe0I/+aI3Q/nPAA=="
  "resolved" "https://registry.npmjs.org/antd-mobile/-/antd-mobile-2.3.4.tgz"
  "version" "2.3.4"
  dependencies:
    "array-tree-filter" "~2.1.0"
    "babel-runtime" "6.x"
    "classnames" "^2.2.1"
    "normalize.css" "^7.0.0"
    "rc-checkbox" "~2.0.0"
    "rc-collapse" "~1.9.1"
    "rc-slider" "~8.2.0"
    "rc-swipeout" "~2.0.0"
    "rmc-calendar" "^1.0.0"
    "rmc-cascader" "~5.0.0"
    "rmc-date-picker" "^6.0.8"
    "rmc-dialog" "^1.0.1"
    "rmc-drawer" "^0.4.11"
    "rmc-feedback" "^2.0.0"
    "rmc-input-number" "^1.0.0"
    "rmc-list-view" "^0.11.0"
    "rmc-notification" "~1.0.0"
    "rmc-nuka-carousel" "~3.0.0"
    "rmc-picker" "~5.0.0"
    "rmc-pull-to-refresh" "~1.0.1"
    "rmc-steps" "~1.0.0"
    "rmc-tabs" "~1.2.0"
    "rmc-tooltip" "~1.0.0"

"antd@^4.1.2", "antd@^4.20.0 ":
  "integrity" "sha512-zZrK4UYxHtU6tGOOf0uG/kBRx1kTvypfuSB3GqE/SBQxFhZ/TZ+yj7Z1qwI8vGfMtUUJdLeuoCAqGDa1zPsXnQ=="
  "resolved" "https://registry.npmjs.org/antd/-/antd-4.24.16.tgz"
  "version" "4.24.16"
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/icons" "^4.8.2"
    "@ant-design/react-slick" "~1.0.2"
    "@babel/runtime" "^7.18.3"
    "@ctrl/tinycolor" "^3.6.1"
    "classnames" "^2.2.6"
    "copy-to-clipboard" "^3.2.0"
    "lodash" "^4.17.21"
    "moment" "^2.29.2"
    "rc-cascader" "~3.7.3"
    "rc-checkbox" "~3.0.1"
    "rc-collapse" "~3.4.2"
    "rc-dialog" "~9.0.2"
    "rc-drawer" "~6.3.0"
    "rc-dropdown" "~4.0.1"
    "rc-field-form" "~1.38.2"
    "rc-image" "~5.13.0"
    "rc-input" "~0.1.4"
    "rc-input-number" "~7.3.11"
    "rc-mentions" "~1.13.1"
    "rc-menu" "~9.8.4"
    "rc-motion" "^2.9.0"
    "rc-notification" "~4.6.1"
    "rc-pagination" "~3.2.0"
    "rc-picker" "~2.7.6"
    "rc-progress" "~3.4.2"
    "rc-rate" "~2.9.3"
    "rc-resize-observer" "^1.3.1"
    "rc-segmented" "~2.3.0"
    "rc-select" "~14.1.18"
    "rc-slider" "~10.0.1"
    "rc-steps" "~5.0.0"
    "rc-switch" "~3.2.2"
    "rc-table" "~7.26.0"
    "rc-tabs" "~12.5.10"
    "rc-textarea" "~0.4.7"
    "rc-tooltip" "~5.2.2"
    "rc-tree" "~5.7.12"
    "rc-tree-select" "~5.5.5"
    "rc-trigger" "^5.3.4"
    "rc-upload" "~4.3.6"
    "rc-util" "^5.37.0"
    "scroll-into-view-if-needed" "^2.2.25"

"antd@^4.1.3":
  "integrity" "sha512-VOW9+ekutTuov2NxH9ReDoUmEtfyGPoXSVplUrP7jkYIvPREQsLi+825/nwf1WRBxagWZgJxzJwtl2i9fbvh9A=="
  "resolved" "https://registry.npmjs.org/antd/-/antd-4.24.12.tgz"
  "version" "4.24.12"
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/icons" "^4.7.0"
    "@ant-design/react-slick" "~0.29.1"
    "@babel/runtime" "^7.18.3"
    "@ctrl/tinycolor" "^3.4.0"
    "classnames" "^2.2.6"
    "copy-to-clipboard" "^3.2.0"
    "lodash" "^4.17.21"
    "moment" "^2.29.2"
    "rc-cascader" "~3.7.0"
    "rc-checkbox" "~3.0.0"
    "rc-collapse" "~3.4.2"
    "rc-dialog" "~9.0.2"
    "rc-drawer" "~6.3.0"
    "rc-dropdown" "~4.0.0"
    "rc-field-form" "~1.34.0"
    "rc-image" "~5.13.0"
    "rc-input" "~0.1.4"
    "rc-input-number" "~7.3.9"
    "rc-mentions" "~1.13.1"
    "rc-menu" "~9.8.0"
    "rc-motion" "^2.6.1"
    "rc-notification" "~4.6.0"
    "rc-pagination" "~3.2.0"
    "rc-picker" "~2.7.0"
    "rc-progress" "~3.4.1"
    "rc-rate" "~2.9.0"
    "rc-resize-observer" "^1.2.0"
    "rc-segmented" "~2.1.0"
    "rc-select" "~14.1.17"
    "rc-slider" "~10.0.0"
    "rc-steps" "~5.0.0-alpha.2"
    "rc-switch" "~3.2.0"
    "rc-table" "~7.26.0"
    "rc-tabs" "~12.5.6"
    "rc-textarea" "~0.4.5"
    "rc-tooltip" "~5.2.0"
    "rc-tree" "~5.7.0"
    "rc-tree-select" "~5.5.0"
    "rc-trigger" "^5.2.10"
    "rc-upload" "~4.3.0"
    "rc-util" "^5.22.5"
    "scroll-into-view-if-needed" "^2.2.25"

"antd@^5.1.2", "antd@>=4.23.0":
  "integrity" "sha512-FZDgV60I2sZ0ZpxcP1vq+lwNM+OwMESjT1A2Zs9mgyK1P3X4JILugD+m05IZoUqkjWO80IKNUo9qkE1S1rEB6A=="
  "resolved" "https://registry.npmjs.org/antd/-/antd-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@ant-design/colors" "^7.0.0"
    "@ant-design/cssinjs" "^1.5.6"
    "@ant-design/icons" "^5.0.0"
    "@ant-design/react-slick" "~1.0.0"
    "@babel/runtime" "^7.18.3"
    "@ctrl/tinycolor" "^3.4.0"
    "@rc-component/mutate-observer" "^1.0.0"
    "@rc-component/tour" "~1.6.0"
    "classnames" "^2.2.6"
    "copy-to-clipboard" "^3.2.0"
    "dayjs" "^1.11.1"
    "qrcode.react" "^3.1.0"
    "rc-cascader" "~3.8.0"
    "rc-checkbox" "~2.3.0"
    "rc-collapse" "~3.5.2"
    "rc-dialog" "~9.0.2"
    "rc-drawer" "~6.1.1"
    "rc-dropdown" "~4.0.0"
    "rc-field-form" "~1.27.0"
    "rc-image" "~5.13.0"
    "rc-input" "~0.2.1"
    "rc-input-number" "~7.4.0"
    "rc-mentions" "~2.0.0"
    "rc-menu" "~9.8.2"
    "rc-motion" "^2.6.1"
    "rc-notification" "~5.0.0"
    "rc-pagination" "~3.2.0"
    "rc-picker" "~3.1.1"
    "rc-progress" "~3.4.1"
    "rc-rate" "~2.9.0"
    "rc-resize-observer" "^1.2.0"
    "rc-segmented" "~2.1.2"
    "rc-select" "~14.2.0"
    "rc-slider" "~10.1.0"
    "rc-steps" "~6.0.0"
    "rc-switch" "~4.0.0"
    "rc-table" "~7.30.2"
    "rc-tabs" "~12.5.1"
    "rc-textarea" "~1.0.0"
    "rc-tooltip" "~5.3.1"
    "rc-tree" "~5.7.0"
    "rc-tree-select" "~5.6.0"
    "rc-trigger" "^5.3.4"
    "rc-upload" "~4.3.0"
    "rc-util" "^5.27.0"
    "scroll-into-view-if-needed" "^3.0.3"
    "throttle-debounce" "^5.0.0"

"anymatch@^2.0.0":
  "integrity" "sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw=="
  "resolved" "https://registry.npmjs.org/anymatch/-/anymatch-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "micromatch" "^3.1.4"
    "normalize-path" "^2.1.1"

"anymatch@^3.0.3", "anymatch@~3.1.1":
  "integrity" "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw=="
  "resolved" "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"arch@^2.1.1":
  "integrity" "sha512-Of/R0wqp83cgHozfIYLbBMnej79U/SVGOOyuB3VVFv1NRM/PSFMK12x9KVtiYzJqmnU5WR2qp0Z5rHb7sWGnFQ=="
  "resolved" "https://registry.npmjs.org/arch/-/arch-2.2.0.tgz"
  "version" "2.2.0"

"argparse@^1.0.7":
  "integrity" "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg=="
  "resolved" "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"arr-diff@^4.0.0":
  "integrity" "sha512-YVIQ82gZPGBebQV/a8dar4AitzCQs0jjXwMPZllpXMaGjXPYVUawSxQrRsjhjupyVxEvbHgUmIhKVlND+j02kA=="
  "resolved" "https://registry.npmjs.org/arr-diff/-/arr-diff-4.0.0.tgz"
  "version" "4.0.0"

"arr-flatten@^1.1.0":
  "integrity" "sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg=="
  "resolved" "https://registry.npmjs.org/arr-flatten/-/arr-flatten-1.1.0.tgz"
  "version" "1.1.0"

"arr-union@^3.1.0":
  "integrity" "sha512-sKpyeERZ02v1FeCZT8lrfJq5u6goHCtpTAzPwJYe7c8SPFOboNjNg1vz2L4VTn9T4PQxEx13TbXLmYUcS6Ug7Q=="
  "resolved" "https://registry.npmjs.org/arr-union/-/arr-union-3.1.0.tgz"
  "version" "3.1.0"

"array-tree-filter@^2.1.0", "array-tree-filter@~2.1.0", "array-tree-filter@2.1.x":
  "integrity" "sha512-4ROwICNlNw/Hqa9v+rk5h22KjmzB1JGTMVKP2AKJBOCgb0yL0ASf0+YvCcLNNwquOHNX48jkeZIJ3a+oOQqKcw=="
  "resolved" "https://registry.npmjs.org/array-tree-filter/-/array-tree-filter-2.1.0.tgz"
  "version" "2.1.0"

"array-unique@^0.3.2":
  "integrity" "sha512-SleRWjh9JUud2wH1hPs9rZBZ33H6T9HOiL0uwGnGx9FpE6wKGyfWugmbkEOIs6qWrZhg0LWeLziLrEwQJhs5mQ=="
  "resolved" "https://registry.npmjs.org/array-unique/-/array-unique-0.3.2.tgz"
  "version" "0.3.2"

"asap@~2.0.3":
  "integrity" "sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA=="
  "resolved" "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz"
  "version" "2.0.6"

"asn1.js@^5.2.0":
  "integrity" "sha512-+I//4cYPccV8LdmBLiX8CYvf9Sp3vQsrqu2QNXRcrbiWvcx/UdlFiqUJJzxRQxgsZmvhXhn4cSKeSmoFjVdupA=="
  "resolved" "https://registry.npmjs.org/asn1.js/-/asn1.js-5.4.1.tgz"
  "version" "5.4.1"
  dependencies:
    "bn.js" "^4.0.0"
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"
    "safer-buffer" "^2.1.0"

"assert@^1.1.1":
  "integrity" "sha512-EDsgawzwoun2CZkCgtxJbv392v4nbk9XDD06zI+kQYoBM/3RBWLlEyJARDOmhAAosBjWACEkKL6S+lIZtcAubA=="
  "resolved" "https://registry.npmjs.org/assert/-/assert-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "object-assign" "^4.1.1"
    "util" "0.10.3"

"assign-symbols@^1.0.0":
  "integrity" "sha512-Q+JC7Whu8HhmTdBph/Tq59IoRtoy6KAm5zzPv00WdujX82lbAL8K7WVjne7vdCsAmbF4AYaDOPyO3k0kl8qIrw=="
  "resolved" "https://registry.npmjs.org/assign-symbols/-/assign-symbols-1.0.0.tgz"
  "version" "1.0.0"

"astral-regex@^2.0.0":
  "integrity" "sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ=="
  "resolved" "https://registry.npmjs.org/astral-regex/-/astral-regex-2.0.0.tgz"
  "version" "2.0.0"

"async-validator@^4.1.0":
  "integrity" "sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg=="
  "resolved" "https://registry.npmjs.org/async-validator/-/async-validator-4.2.5.tgz"
  "version" "4.2.5"

"asynckit@^0.4.0":
  "integrity" "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="
  "resolved" "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"atob@^2.1.2":
  "integrity" "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg=="
  "resolved" "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz"
  "version" "2.1.2"

"autoprefixer@^9.6.1":
  "integrity" "sha512-eM9d/swFopRt5gdJ7jrpCwgvEMIayITpojhkkSMRsFHYuH5bkSQ4p/9qTEHtmNudUZh22Tehu7I6CxAW0IXTKA=="
  "resolved" "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.8.8.tgz"
  "version" "9.8.8"
  dependencies:
    "browserslist" "^4.12.0"
    "caniuse-lite" "^1.0.30001109"
    "normalize-range" "^0.1.2"
    "num2fraction" "^1.2.2"
    "picocolors" "^0.2.1"
    "postcss" "^7.0.32"
    "postcss-value-parser" "^4.1.0"

"axios@^1.6.0":
  "integrity" "sha512-Ar7ND9pU99eJ9GpoGQKhKf58GpUOgnzuaB7ueNQ5BMi0p+LZ5oaEnfF999fAArcTIBwXTCHAmGcHOZJaWPq9Nw=="
  "resolved" "https://registry.npmmirror.com/axios/-/axios-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "follow-redirects" "^1.15.6"
    "form-data" "^4.0.0"
    "proxy-from-env" "^1.1.0"

"babel-jest@^26.6.3":
  "integrity" "sha512-pl4Q+GAVOHwvjrck6jKjvmGhnO3jHX/xuB9d27f+EJZ/6k+6nMuPjorrYp7s++bKKdANwzElBWnLWaObvTnaZA=="
  "resolved" "https://registry.npmjs.org/babel-jest/-/babel-jest-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/babel__core" "^7.1.7"
    "babel-plugin-istanbul" "^6.0.0"
    "babel-preset-jest" "^26.6.2"
    "chalk" "^4.0.0"
    "graceful-fs" "^4.2.4"
    "slash" "^3.0.0"

"babel-plugin-dva-hmr@^0.4.2":
  "integrity" "sha512-QP5W0IT9LDFgoqkXD0g7wn/FMn7zO562P8qys23twXri4i3E1SGOQ3z7jqcPrqzfTZHx+Ufr+cXy9oI6OLMYZg=="
  "resolved" "https://registry.npmjs.org/babel-plugin-dva-hmr/-/babel-plugin-dva-hmr-0.4.2.tgz"
  "version" "0.4.2"

"babel-plugin-istanbul@^6.0.0":
  "integrity" "sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA=="
  "resolved" "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz"
  "version" "6.1.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    "istanbul-lib-instrument" "^5.0.4"
    "test-exclude" "^6.0.0"

"babel-plugin-jest-hoist@^26.6.2":
  "integrity" "sha512-PO9t0697lNTmcEHH69mdtYiOIkkOlj9fySqfO3K1eCcdISevLAE0xY59VLLUj0SoiPiTX/JU2CYFpILydUa5Lw=="
  "resolved" "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.0.0"
    "@types/babel__traverse" "^7.0.6"

"babel-preset-current-node-syntax@^1.0.0":
  "integrity" "sha512-M7LQ0bxarkxQoN+vz5aJPsLBn77n8QgTFmo8WK0/44auK2xlCXrYcUxHFxgU7qW5Yzw/CjmLRK2uJzaCd7LvqQ=="
  "resolved" "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.8.3"
    "@babel/plugin-syntax-import-meta" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-top-level-await" "^7.8.3"

"babel-preset-jest@^26.6.2":
  "integrity" "sha512-YvdtlVm9t3k777c5NPQIv6cxFFFapys25HiUmuSgHwIZhfifweR5c5Sf5nwE3MAbfu327CYSvps8Yx6ANLyleQ=="
  "resolved" "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "babel-plugin-jest-hoist" "^26.6.2"
    "babel-preset-current-node-syntax" "^1.0.0"

"babel-runtime@^6.23.0", "babel-runtime@^6.26.0", "babel-runtime@6.x":
  "integrity" "sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g=="
  "resolved" "https://registry.npmjs.org/babel-runtime/-/babel-runtime-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "core-js" "^2.4.0"
    "regenerator-runtime" "^0.11.0"

"balanced-match@^1.0.0":
  "integrity" "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="
  "resolved" "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  "version" "1.0.2"

"base@^0.11.1":
  "integrity" "sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg=="
  "resolved" "https://registry.npmjs.org/base/-/base-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "cache-base" "^1.0.1"
    "class-utils" "^0.3.5"
    "component-emitter" "^1.2.1"
    "define-property" "^1.0.0"
    "isobject" "^3.0.1"
    "mixin-deep" "^1.2.0"
    "pascalcase" "^0.1.1"

"base64-js@^1.0.2":
  "integrity" "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA=="
  "resolved" "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz"
  "version" "1.5.1"

"big.js@^5.2.2":
  "integrity" "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ=="
  "resolved" "https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz"
  "version" "5.2.2"

"binary-extensions@^2.0.0":
  "integrity" "sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA=="
  "resolved" "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.2.0.tgz"
  "version" "2.2.0"

"bn.js@^4.0.0":
  "integrity" "sha512-c98Bf3tPniI+scsdk237ku1Dc3ujXQTSgyiPUDEOe7tRkhrqridvh8klBv0HCEso1OLOYcHuCv/cS6DNxKH+ZA=="
  "resolved" "https://registry.npmjs.org/bn.js/-/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^4.1.0":
  "integrity" "sha512-c98Bf3tPniI+scsdk237ku1Dc3ujXQTSgyiPUDEOe7tRkhrqridvh8klBv0HCEso1OLOYcHuCv/cS6DNxKH+ZA=="
  "resolved" "https://registry.npmjs.org/bn.js/-/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^4.11.9":
  "integrity" "sha512-c98Bf3tPniI+scsdk237ku1Dc3ujXQTSgyiPUDEOe7tRkhrqridvh8klBv0HCEso1OLOYcHuCv/cS6DNxKH+ZA=="
  "resolved" "https://registry.npmjs.org/bn.js/-/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^5.0.0", "bn.js@^5.1.1":
  "integrity" "sha512-eXRvHzWyYPBuB4NBy0cmYQjGitUrtqwbvlzP3G6VFnNRbsZQIxQ10PbKKHt8gZ/HW/D/747aDl+QkDqg3KQLMQ=="
  "resolved" "https://registry.npmjs.org/bn.js/-/bn.js-5.2.1.tgz"
  "version" "5.2.1"

"brace-expansion@^1.1.7":
  "integrity" "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA=="
  "resolved" "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"braces@^2.3.1":
  "integrity" "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w=="
  "resolved" "https://registry.npmjs.org/braces/-/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "^1.1.0"
    "array-unique" "^0.3.2"
    "extend-shallow" "^2.0.1"
    "fill-range" "^4.0.0"
    "isobject" "^3.0.1"
    "repeat-element" "^1.1.2"
    "snapdragon" "^0.8.1"
    "snapdragon-node" "^2.0.1"
    "split-string" "^3.0.2"
    "to-regex" "^3.0.1"

"braces@^3.0.2", "braces@~3.0.2":
  "integrity" "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A=="
  "resolved" "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"brorand@^1.0.1", "brorand@^1.1.0":
  "integrity" "sha512-cKV8tMCEpQs4hK/ik71d6LrPOnpkpGBR0wzxqr68g2m/LB2GxVYQroAjMJZRVM1Y4BCjCKc3vAamxSzOY2RP+w=="
  "resolved" "https://registry.npmjs.org/brorand/-/brorand-1.1.0.tgz"
  "version" "1.1.0"

"browser-process-hrtime@^1.0.0":
  "integrity" "sha512-9o5UecI3GhkpM6DrXr69PblIuWxPKk9Y0jHBRhdocZ2y7YECBFCsHm79Pr3OyR2AvjhDkabFJaDJMYRazHgsow=="
  "resolved" "https://registry.npmjs.org/browser-process-hrtime/-/browser-process-hrtime-1.0.0.tgz"
  "version" "1.0.0"

"browserify-aes@^1.0.0", "browserify-aes@^1.0.4":
  "integrity" "sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA=="
  "resolved" "https://registry.npmjs.org/browserify-aes/-/browserify-aes-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "buffer-xor" "^1.0.3"
    "cipher-base" "^1.0.0"
    "create-hash" "^1.1.0"
    "evp_bytestokey" "^1.0.3"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"browserify-cipher@^1.0.0":
  "integrity" "sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w=="
  "resolved" "https://registry.npmjs.org/browserify-cipher/-/browserify-cipher-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "browserify-aes" "^1.0.4"
    "browserify-des" "^1.0.0"
    "evp_bytestokey" "^1.0.0"

"browserify-des@^1.0.0":
  "integrity" "sha512-BioO1xf3hFwz4kc6iBhI3ieDFompMhrMlnDFC4/0/vd5MokpuAc3R+LYbwTA9A5Yc9pq9UYPqffKpW2ObuwX5A=="
  "resolved" "https://registry.npmjs.org/browserify-des/-/browserify-des-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "cipher-base" "^1.0.1"
    "des.js" "^1.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"browserify-rsa@^4.0.0", "browserify-rsa@^4.0.1":
  "integrity" "sha512-AdEER0Hkspgno2aR97SAf6vi0y0k8NuOpGnVH3O99rcA5Q6sh8QxcngtHuJ6uXwnfAXNM4Gn1Gb7/MV1+Ymbog=="
  "resolved" "https://registry.npmjs.org/browserify-rsa/-/browserify-rsa-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "bn.js" "^5.0.0"
    "randombytes" "^2.0.1"

"browserify-sign@^4.0.0":
  "integrity" "sha512-/vrA5fguVAKKAVTNJjgSm1tRQDHUU6DbwO9IROu/0WAzC8PKhucDSh18J0RMvVeHAn5puMd+QHC2erPRNf8lmg=="
  "resolved" "https://registry.npmjs.org/browserify-sign/-/browserify-sign-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "bn.js" "^5.1.1"
    "browserify-rsa" "^4.0.1"
    "create-hash" "^1.2.0"
    "create-hmac" "^1.1.7"
    "elliptic" "^6.5.3"
    "inherits" "^2.0.4"
    "parse-asn1" "^5.1.5"
    "readable-stream" "^3.6.0"
    "safe-buffer" "^5.2.0"

"browserify-zlib@^0.2.0":
  "integrity" "sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA=="
  "resolved" "https://registry.npmjs.org/browserify-zlib/-/browserify-zlib-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "pako" "~1.0.5"

"browserslist@^4.12.0", "browserslist@^4.21.9", "browserslist@^4.6.4", "browserslist@>= 4.21.0":
  "integrity" "sha512-M0MFoZzbUrRU4KNfCrDLnvyE7gub+peetoTid3TBIqtunaDJyXlwhakT+/VkvSXcfIzFfK/nkCs4nmyTmxdNSg=="
  "resolved" "https://registry.npmjs.org/browserslist/-/browserslist-4.21.9.tgz"
  "version" "4.21.9"
  dependencies:
    "caniuse-lite" "^1.0.30001503"
    "electron-to-chromium" "^1.4.431"
    "node-releases" "^2.0.12"
    "update-browserslist-db" "^1.0.11"

"bser@2.1.1":
  "integrity" "sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ=="
  "resolved" "https://registry.npmjs.org/bser/-/bser-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "node-int64" "^0.4.0"

"buffer-from@^1.0.0":
  "integrity" "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ=="
  "resolved" "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz"
  "version" "1.1.2"

"buffer-xor@^1.0.3":
  "integrity" "sha512-571s0T7nZWK6vB67HI5dyUF7wXiNcfaPPPTl6zYCNApANjIvYJTg7hlud/+cJpdAhS7dVzqMLmfhfHR3rAcOjQ=="
  "resolved" "https://registry.npmjs.org/buffer-xor/-/buffer-xor-1.0.3.tgz"
  "version" "1.0.3"

"buffer@^4.3.0":
  "integrity" "sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg=="
  "resolved" "https://registry.npmjs.org/buffer/-/buffer-4.9.2.tgz"
  "version" "4.9.2"
  dependencies:
    "base64-js" "^1.0.2"
    "ieee754" "^1.1.4"
    "isarray" "^1.0.0"

"builtin-status-codes@^3.0.0":
  "integrity" "sha512-HpGFw18DgFWlncDfjTa2rcQ4W88O1mC8e8yZ2AvQY5KDaktSTwo+KRf6nHK6FRI5FyRyb/5T6+TSxfP7QyGsmQ=="
  "resolved" "https://registry.npmjs.org/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz"
  "version" "3.0.0"

"cache-base@^1.0.1":
  "integrity" "sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ=="
  "resolved" "https://registry.npmjs.org/cache-base/-/cache-base-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "collection-visit" "^1.0.0"
    "component-emitter" "^1.2.1"
    "get-value" "^2.0.6"
    "has-value" "^1.0.0"
    "isobject" "^3.0.1"
    "set-value" "^2.0.0"
    "to-object-path" "^0.3.0"
    "union-value" "^1.0.0"
    "unset-value" "^1.0.0"

"call-bind@^1.0.7":
  "integrity" "sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w=="
  "resolved" "https://registry.npmmirror.com/call-bind/-/call-bind-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "es-define-property" "^1.0.0"
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"
    "get-intrinsic" "^1.2.4"
    "set-function-length" "^1.2.1"

"caller-callsite@^2.0.0":
  "integrity" "sha512-JuG3qI4QOftFsZyOn1qq87fq5grLIyk1JYd5lJmdA+fG7aQ9pA/i3JIJGcO3q0MrRcHlOt1U+ZeHW8Dq9axALQ=="
  "resolved" "https://registry.npmjs.org/caller-callsite/-/caller-callsite-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "callsites" "^2.0.0"

"caller-path@^2.0.0":
  "integrity" "sha512-MCL3sf6nCSXOwCTzvPKhN18TU7AHTvdtam8DAogxcrJ8Rjfbbg7Lgng64H9Iy+vUV6VGFClN/TyxBkAebLRR4A=="
  "resolved" "https://registry.npmjs.org/caller-path/-/caller-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-callsite" "^2.0.0"

"callsites@^2.0.0":
  "integrity" "sha512-ksWePWBloaWPxJYQ8TL0JHvtci6G5QTKwQ95RcWAa/lzoAKuAOflGdAK92hpHXjkwb8zLxoLNUoNYZgVsaJzvQ=="
  "resolved" "https://registry.npmjs.org/callsites/-/callsites-2.0.0.tgz"
  "version" "2.0.0"

"callsites@^3.0.0":
  "integrity" "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="
  "resolved" "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camelcase@^5.0.0", "camelcase@^5.3.1":
  "integrity" "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg=="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"camelcase@^6.0.0":
  "integrity" "sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA=="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz"
  "version" "6.3.0"

"caniuse-lite@^1.0.30000981", "caniuse-lite@^1.0.30001109", "caniuse-lite@^1.0.30001503":
  "integrity" "sha512-Vdhm5S11DaFVLlyiKu4hiUTkpZu+y1KA/rZZqVQfOD5YdDT/eQKlkt7NaE0WGOFgX32diqt9MiP9CAiFeRklaA=="
  "resolved" "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001517.tgz"
  "version" "1.0.30001517"

"capture-exit@^2.0.0":
  "integrity" "sha512-PiT/hQmTonHhl/HFGN+Lx3JJUznrVYJ3+AQsnthneZbvW7x+f08Tk7yLJTLEOUvBTbduLeeBkxEaYXUOUrRq6g=="
  "resolved" "https://registry.npmjs.org/capture-exit/-/capture-exit-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "rsvp" "^4.8.4"

"cfb@^1.1.4":
  "integrity" "sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA=="
  "resolved" "https://registry.npmmirror.com/cfb/-/cfb-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "adler-32" "~1.3.0"
    "crc-32" "~1.2.0"

"chalk@^2.0.0", "chalk@^2.4.2":
  "integrity" "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^4.0.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.1.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"char-regex@^1.0.2":
  "integrity" "sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw=="
  "resolved" "https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz"
  "version" "1.0.2"

"chokidar@3.5.1":
  "integrity" "sha512-9+s+Od+W0VJJzawDma/gvBNQqkTiqYTWLuZoyAsivsI4AaWTCzHG06/TMjsf1cYe9Cb97UCEhjz7HvnPk2p/tw=="
  "resolved" "https://registry.npmjs.org/chokidar/-/chokidar-3.5.1.tgz"
  "version" "3.5.1"
  dependencies:
    "anymatch" "~3.1.1"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.0"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.5.0"
  optionalDependencies:
    "fsevents" "~2.3.1"

"ci-info@^1.5.0":
  "integrity" "sha512-vsGdkwSCDpWmP80ncATX7iea5DWQemg1UgCW5J8tqjU3lYw4FBYuj89J0CTVomA7BEfvSZd84GmHko+MxFQU2A=="
  "resolved" "https://registry.npmjs.org/ci-info/-/ci-info-1.6.0.tgz"
  "version" "1.6.0"

"ci-info@^2.0.0":
  "integrity" "sha512-5tK7EtrZ0N+OLFMthtqOj4fI2Jeb88C4CAZPu25LDVUgXJ0A3Js4PMGqrn0JU1W0Mh1/Z8wZzYPxqUrXeBboCQ=="
  "resolved" "https://registry.npmjs.org/ci-info/-/ci-info-2.0.0.tgz"
  "version" "2.0.0"

"cipher-base@^1.0.0", "cipher-base@^1.0.1", "cipher-base@^1.0.3":
  "integrity" "sha512-Kkht5ye6ZGmwv40uUDZztayT2ThLQGfnj/T71N/XzeZeo3nf8foyW7zGTsPYkEya3m5f3cAypH+qe7YOrM1U2Q=="
  "resolved" "https://registry.npmjs.org/cipher-base/-/cipher-base-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"cjs-module-lexer@^0.6.0":
  "integrity" "sha512-uc2Vix1frTfnuzxxu1Hp4ktSvM3QaI4oXl4ZUqL1wjTu/BGki9TrCWoqLTg/drR1KwAEarXuRFCG2Svr1GxPFw=="
  "resolved" "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.6.0.tgz"
  "version" "0.6.0"

"class-utils@^0.3.5":
  "integrity" "sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg=="
  "resolved" "https://registry.npmjs.org/class-utils/-/class-utils-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "arr-union" "^3.1.0"
    "define-property" "^0.2.5"
    "isobject" "^3.0.0"
    "static-extend" "^0.1.1"

"classnames@^2.2.0", "classnames@^2.2.1", "classnames@^2.2.3", "classnames@^2.2.4", "classnames@^2.2.5", "classnames@^2.2.6", "classnames@^2.3.1", "classnames@^2.3.2", "classnames@2.x":
  "integrity" "sha512-CSbhY4cFEJRe6/GQzIk5qXZ4Jeg5pcsP7b5peFSDpffpe1cqjASH/n9UTjBwOp6XpMSTwQ8Za2K5V02ueA7Tmw=="
  "resolved" "https://registry.npmjs.org/classnames/-/classnames-2.3.2.tgz"
  "version" "2.3.2"

"clean-stack@^2.0.0":
  "integrity" "sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A=="
  "resolved" "https://registry.npmjs.org/clean-stack/-/clean-stack-2.2.0.tgz"
  "version" "2.2.0"

"cli-cursor@^3.1.0":
  "integrity" "sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw=="
  "resolved" "https://registry.npmjs.org/cli-cursor/-/cli-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "restore-cursor" "^3.1.0"

"cli-truncate@^2.1.0":
  "integrity" "sha512-n8fOixwDD6b/ObinzTrp1ZKFzbgvKZvuz/TvejnLn1aQfC6r52XEx85FmuC+3HI+JM7coBRXUvNqEU2PHVrHpg=="
  "resolved" "https://registry.npmjs.org/cli-truncate/-/cli-truncate-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "slice-ansi" "^3.0.0"
    "string-width" "^4.2.0"

"clipboardy@2.3.0":
  "integrity" "sha512-mKhiIL2DrQIsuXMgBgnfEHOZOryC7kY7YO//TN6c63wlEm3NG5tz+YgY5rVi29KCmq/QQjKYvM7a19+MDOTHOQ=="
  "resolved" "https://registry.npmjs.org/clipboardy/-/clipboardy-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "arch" "^2.1.1"
    "execa" "^1.0.0"
    "is-wsl" "^2.1.1"

"cliui@^6.0.0":
  "integrity" "sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ=="
  "resolved" "https://registry.npmjs.org/cliui/-/cliui-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^6.2.0"

"co@^4.6.0":
  "integrity" "sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ=="
  "resolved" "https://registry.npmjs.org/co/-/co-4.6.0.tgz"
  "version" "4.6.0"

"codepage@~1.15.0":
  "integrity" "sha512-3g6NUTPd/YtuuGrhMnOMRjFc+LJw/bnMp3+0r/Wcz3IXUuCosKRJvMphm5+Q+bvTVGcJJuRvVLuYba+WojaFaA=="
  "resolved" "https://registry.npmmirror.com/codepage/-/codepage-1.15.0.tgz"
  "version" "1.15.0"

"collect-v8-coverage@^1.0.0":
  "integrity" "sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q=="
  "resolved" "https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz"
  "version" "1.0.2"

"collection-visit@^1.0.0":
  "integrity" "sha512-lNkKvzEeMBBjUGHZ+q6z9pSJla0KWAQPvtzhEV9+iGyQYG+pBpl7xKDhxoNSOZH2hhv0v5k0y2yAM4o4SjoSkw=="
  "resolved" "https://registry.npmjs.org/collection-visit/-/collection-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "map-visit" "^1.0.0"
    "object-visit" "^1.0.0"

"color-convert@^1.9.0":
  "integrity" "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^2.0.1":
  "integrity" "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@~1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-name@1.1.3":
  "integrity" "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw=="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
  "version" "1.1.3"

"colorette@^2.0.16":
  "integrity" "sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w=="
  "resolved" "https://registry.npmjs.org/colorette/-/colorette-2.0.20.tgz"
  "version" "2.0.20"

"combined-stream@^1.0.8":
  "integrity" "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg=="
  "resolved" "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"commander@^2.20.0":
  "integrity" "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz"
  "version" "2.20.3"

"commander@^6.2.0":
  "integrity" "sha512-U7VdrJFnJgo4xjrHpTzu0yrHPGImdsmD95ZlgYSEajAn2JKzDhDTPG9kBTefmObL2w/ngeZnilk+OV9CG3d7UA=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-6.2.1.tgz"
  "version" "6.2.1"

"component-classes@^1.2.5":
  "integrity" "sha512-hPFGULxdwugu1QWW3SvVOCUHLzO34+a2J6Wqy0c5ASQkfi9/8nZcBB0ZohaEbXOQlCflMAEMmEWk7u7BVs4koA=="
  "resolved" "https://registry.npmjs.org/component-classes/-/component-classes-1.2.6.tgz"
  "version" "1.2.6"
  dependencies:
    "component-indexof" "0.0.3"

"component-emitter@^1.2.1":
  "integrity" "sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg=="
  "resolved" "https://registry.npmjs.org/component-emitter/-/component-emitter-1.3.0.tgz"
  "version" "1.3.0"

"component-indexof@0.0.3":
  "integrity" "sha512-puDQKvx/64HZXb4hBwIcvQLaLgux8o1CbWl39s41hrIIZDl1lJiD5jc22gj3RBeGK0ovxALDYpIbyjqDUUl0rw=="
  "resolved" "https://registry.npmjs.org/component-indexof/-/component-indexof-0.0.3.tgz"
  "version" "0.0.3"

"compute-scroll-into-view@^1.0.20":
  "integrity" "sha512-UCB0ioiyj8CRjtrvaceBLqqhZCVP+1B8+NWQhmdsm0VXOJtobBCf1dBQmebCCo34qZmUwZfIH2MZLqNHazrfjg=="
  "resolved" "https://registry.npmjs.org/compute-scroll-into-view/-/compute-scroll-into-view-1.0.20.tgz"
  "version" "1.0.20"

"compute-scroll-into-view@^2.0.4":
  "integrity" "sha512-y/ZA3BGnxoM/QHHQ2Uy49CLtnWPbt4tTPpEEZiEmmiWBFKjej7nEyH8Ryz54jH0MLXflUYA3Er2zUxPSJu5R+g=="
  "resolved" "https://registry.npmjs.org/compute-scroll-into-view/-/compute-scroll-into-view-2.0.4.tgz"
  "version" "2.0.4"

"concat-map@0.0.1":
  "integrity" "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg=="
  "resolved" "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"connected-react-router@6.5.2":
  "integrity" "sha512-qzsLPZCofSI80fwy+HgxtEgSGS4ndYUUZAWaw1dqaOGPLKX/FVwIOEb7q+hjHdnZ4v5pKZcNv5GG4urjujIoyA=="
  "resolved" "https://registry.npmjs.org/connected-react-router/-/connected-react-router-6.5.2.tgz"
  "version" "6.5.2"
  dependencies:
    "immutable" "^3.8.1"
    "prop-types" "^15.7.2"
    "seamless-immutable" "^7.1.3"

"console-browserify@^1.1.0":
  "integrity" "sha512-ZMkYO/LkF17QvCPqM0gxw8yUzigAOZOSWSHg91FH6orS7vcEj5dVZTidN2fQ14yBSdg97RqhSNwLUXInd52OTA=="
  "resolved" "https://registry.npmjs.org/console-browserify/-/console-browserify-1.2.0.tgz"
  "version" "1.2.0"

"constants-browserify@^1.0.0":
  "integrity" "sha512-xFxOwqIzR/e1k1gLiWEophSCMqXcwVHIH7akf7b/vxcUeGunlj3hvZaaqxwHsTgn+IndtkQJgSztIDWeumWJDQ=="
  "resolved" "https://registry.npmjs.org/constants-browserify/-/constants-browserify-1.0.0.tgz"
  "version" "1.0.0"

"convert-source-map@^1.4.0", "convert-source-map@^1.6.0", "convert-source-map@^1.7.0":
  "integrity" "sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A=="
  "resolved" "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz"
  "version" "1.9.0"

"copy-descriptor@^0.1.0":
  "integrity" "sha512-XgZ0pFcakEUlbwQEVNg3+QAis1FyTL3Qel9FYy8pSkQqoG3PNoT0bOCQtOXcOkur21r2Eq2kI+IE+gsmAEVlYw=="
  "resolved" "https://registry.npmjs.org/copy-descriptor/-/copy-descriptor-0.1.1.tgz"
  "version" "0.1.1"

"copy-to-clipboard@^3.2.0":
  "integrity" "sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA=="
  "resolved" "https://registry.npmjs.org/copy-to-clipboard/-/copy-to-clipboard-3.3.3.tgz"
  "version" "3.3.3"
  dependencies:
    "toggle-selection" "^1.0.6"

"core-js-pure@^3.8.1":
  "integrity" "sha512-w+C62kvWti0EPs4KPMCMVv9DriHSXfQOCQ94bGGBiEW5rrbtt/Rz8n5Krhfw9cpFyzXBjf3DB3QnPdEzGDY4Fw=="
  "resolved" "https://registry.npmjs.org/core-js-pure/-/core-js-pure-3.31.1.tgz"
  "version" "3.31.1"

"core-js@^1.0.0":
  "integrity" "sha512-ZiPp9pZlgxpWRu0M+YWbm6+aQ84XEfH1JRXvfOc/fILWI0VKhLC2LX13X1NYq4fULzLMq7Hfh43CSo2/aIaUPA=="
  "resolved" "https://registry.npmjs.org/core-js/-/core-js-1.2.7.tgz"
  "version" "1.2.7"

"core-js@^2.4.0":
  "integrity" "sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ=="
  "resolved" "https://registry.npmjs.org/core-js/-/core-js-2.6.12.tgz"
  "version" "2.6.12"

"core-js@3.6.5":
  "integrity" "sha512-vZVEEwZoIsI+vPEuoF9Iqf5H7/M3eeQqWlQnYa8FSKKePuYTf5MWnxb5SDAzCa60b3JBRS5g9b+Dq7b1y/RCrA=="
  "resolved" "https://registry.npmjs.org/core-js/-/core-js-3.6.5.tgz"
  "version" "3.6.5"

"core-js@3.8.2":
  "integrity" "sha512-FfApuSRgrR6G5s58casCBd9M2k+4ikuu4wbW6pJyYU7bd9zvFc9qf7vr5xmrZOhT9nn+8uwlH1oRR9jTnFoA3A=="
  "resolved" "https://registry.npmjs.org/core-js/-/core-js-3.8.2.tgz"
  "version" "3.8.2"

"core-util-is@~1.0.0":
  "integrity" "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ=="
  "resolved" "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz"
  "version" "1.0.3"

"cosmiconfig@^5.0.0":
  "integrity" "sha512-H65gsXo1SKjf8zmrJ67eJk8aIRKV5ff2D4uKZIBZShbhGSpEmsQOPW/SKMKYhSTrqR7ufy6RP69rPogdaPh/kA=="
  "resolved" "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "import-fresh" "^2.0.0"
    "is-directory" "^0.3.1"
    "js-yaml" "^3.13.1"
    "parse-json" "^4.0.0"

"cosmiconfig@^7.0.0":
  "integrity" "sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA=="
  "resolved" "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.1.0.tgz"
  "version" "7.1.0"
  dependencies:
    "@types/parse-json" "^4.0.0"
    "import-fresh" "^3.2.1"
    "parse-json" "^5.0.0"
    "path-type" "^4.0.0"
    "yaml" "^1.10.0"

"crc-32@~1.2.0", "crc-32@~1.2.1":
  "integrity" "sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ=="
  "resolved" "https://registry.npmmirror.com/crc-32/-/crc-32-1.2.2.tgz"
  "version" "1.2.2"

"create-ecdh@^4.0.0":
  "integrity" "sha512-mf+TCx8wWc9VpuxfP2ht0iSISLZnt0JgWlrOKZiNqyUZWnjIaCIVNQArMHnCZKfEYRg6IM7A+NeJoN8gf/Ws0A=="
  "resolved" "https://registry.npmjs.org/create-ecdh/-/create-ecdh-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "bn.js" "^4.1.0"
    "elliptic" "^6.5.3"

"create-hash@^1.1.0", "create-hash@^1.1.2", "create-hash@^1.2.0":
  "integrity" "sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg=="
  "resolved" "https://registry.npmjs.org/create-hash/-/create-hash-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cipher-base" "^1.0.1"
    "inherits" "^2.0.1"
    "md5.js" "^1.3.4"
    "ripemd160" "^2.0.1"
    "sha.js" "^2.4.0"

"create-hmac@^1.1.0", "create-hmac@^1.1.4", "create-hmac@^1.1.7":
  "integrity" "sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg=="
  "resolved" "https://registry.npmjs.org/create-hmac/-/create-hmac-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "cipher-base" "^1.0.3"
    "create-hash" "^1.1.0"
    "inherits" "^2.0.1"
    "ripemd160" "^2.0.0"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"create-react-class@^15.6.0":
  "integrity" "sha512-QZv4sFWG9S5RUvkTYWbflxeZX+JG7Cz0Tn33rQBJ+WFQTqTfUTjMjiv9tnfXazjsO5r0KhPs+AqCjyrQX6h2ng=="
  "resolved" "https://registry.npmjs.org/create-react-class/-/create-react-class-15.7.0.tgz"
  "version" "15.7.0"
  dependencies:
    "loose-envify" "^1.3.1"
    "object-assign" "^4.1.1"

"cross-env@^7.0.3":
  "integrity" "sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw=="
  "resolved" "https://registry.npmjs.org/cross-env/-/cross-env-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "cross-spawn" "^7.0.1"

"cross-spawn@^5.0.1":
  "integrity" "sha512-pTgQJ5KC0d2hcY8eyL1IzlBPYjTkyH72XRZPnLyKus2mBfNjQs3klqbJU2VILqZryAZUt9JOb3h/mWMy23/f5A=="
  "resolved" "https://registry.npmjs.org/cross-spawn/-/cross-spawn-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "lru-cache" "^4.0.1"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^6.0.0":
  "integrity" "sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ=="
  "resolved" "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.5.tgz"
  "version" "6.0.5"
  dependencies:
    "nice-try" "^1.0.4"
    "path-key" "^2.0.1"
    "semver" "^5.5.0"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^7.0.0":
  "integrity" "sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w=="
  "resolved" "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"cross-spawn@^7.0.1":
  "integrity" "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA=="
  "resolved" "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz"
  "version" "7.0.6"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"crypto-browserify@^3.11.0":
  "integrity" "sha512-fz4spIh+znjO2VjL+IdhEpRJ3YN6sMzITSBijk6FK2UvTqruSQW+/cCZTSNsMiZNvUeq0CqurF+dAbyiGOY6Wg=="
  "resolved" "https://registry.npmjs.org/crypto-browserify/-/crypto-browserify-3.12.0.tgz"
  "version" "3.12.0"
  dependencies:
    "browserify-cipher" "^1.0.0"
    "browserify-sign" "^4.0.0"
    "create-ecdh" "^4.0.0"
    "create-hash" "^1.1.0"
    "create-hmac" "^1.1.0"
    "diffie-hellman" "^5.0.0"
    "inherits" "^2.0.1"
    "pbkdf2" "^3.0.3"
    "public-encrypt" "^4.0.0"
    "randombytes" "^2.0.0"
    "randomfill" "^1.0.3"

"css-animation@^1.3.2", "css-animation@1.x":
  "integrity" "sha512-/48+/BaEaHRY6kNQ2OIPzKf9A6g8WjZYjhiNDNuIVbsm5tXCGIAsHDjB4Xu1C4vXJtUWZo26O68OQkDpNBaPog=="
  "resolved" "https://registry.npmjs.org/css-animation/-/css-animation-1.6.1.tgz"
  "version" "1.6.1"
  dependencies:
    "babel-runtime" "6.x"
    "component-classes" "^1.2.5"

"css-blank-pseudo@^0.1.4":
  "integrity" "sha512-LHz35Hr83dnFeipc7oqFDmsjHdljj3TQtxGGiNWSOsTLIAubSm4TEz8qCaKFpk7idaQ1GfWscF4E6mgpBysA1w=="
  "resolved" "https://registry.npmjs.org/css-blank-pseudo/-/css-blank-pseudo-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "postcss" "^7.0.5"

"css-has-pseudo@^0.10.0":
  "integrity" "sha512-Z8hnfsZu4o/kt+AuFzeGpLVhFOGO9mluyHBaA2bA8aCGTwah5sT3WV/fTHH8UNZUytOIImuGPrl/prlb4oX4qQ=="
  "resolved" "https://registry.npmjs.org/css-has-pseudo/-/css-has-pseudo-0.10.0.tgz"
  "version" "0.10.0"
  dependencies:
    "postcss" "^7.0.6"
    "postcss-selector-parser" "^5.0.0-rc.4"

"css-prefers-color-scheme@^3.1.1":
  "integrity" "sha512-MTu6+tMs9S3EUqzmqLXEcgNRbNkkD/TGFvowpeoWJn5Vfq7FMgsmRQs9X5NXAURiOBmOxm/lLjsDNXDE6k9bhg=="
  "resolved" "https://registry.npmjs.org/css-prefers-color-scheme/-/css-prefers-color-scheme-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "postcss" "^7.0.5"

"cssdb@^4.4.0":
  "integrity" "sha512-LsTAR1JPEM9TpGhl/0p3nQecC2LJ0kD8X5YARu1hk/9I1gril5vDtMZyNxcEpxxDj34YNck/ucjuoUd66K03oQ=="
  "resolved" "https://registry.npmjs.org/cssdb/-/cssdb-4.4.0.tgz"
  "version" "4.4.0"

"cssesc@^2.0.0":
  "integrity" "sha512-MsCAG1z9lPdoO/IUMLSBWBSVxVtJ1395VGIQ+Fc2gNdkQ1hNDnQdw3YhA71WJCBW1vdwA0cAnk/DnW6bqoEUYg=="
  "resolved" "https://registry.npmjs.org/cssesc/-/cssesc-2.0.0.tgz"
  "version" "2.0.0"

"cssesc@^3.0.0":
  "integrity" "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg=="
  "resolved" "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
  "version" "3.0.0"

"cssom@^0.4.4":
  "integrity" "sha512-p3pvU7r1MyyqbTk+WbNJIgJjG2VmTIaB10rI93LzVPrmDJKkzKYMtxxyAvQXR/NS6otuzveI7+7BBq3SjBS2mw=="
  "resolved" "https://registry.npmjs.org/cssom/-/cssom-0.4.4.tgz"
  "version" "0.4.4"

"cssom@~0.3.6":
  "integrity" "sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg=="
  "resolved" "https://registry.npmjs.org/cssom/-/cssom-0.3.8.tgz"
  "version" "0.3.8"

"cssstyle@^2.3.0":
  "integrity" "sha512-AZL67abkUzIuvcHqk7c09cezpGNcxUxU4Ioi/05xHk4DQeTkWmGYftIE6ctU6AEt+Gn4n1lDStOtj7FKycP71A=="
  "resolved" "https://registry.npmjs.org/cssstyle/-/cssstyle-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "cssom" "~0.3.6"

"csstype@^3.0.10", "csstype@^3.0.2":
  "integrity" "sha512-I7K1Uu0MBPzaFKg4nI5Q7Vs2t+3gWWW648spaF+Rg7pI9ds18Ugn+lvg4SHczUdKlHI5LWBXyqfS8+DufyBsgQ=="
  "resolved" "https://registry.npmjs.org/csstype/-/csstype-3.1.2.tgz"
  "version" "3.1.2"

"data-urls@^2.0.0":
  "integrity" "sha512-X5eWTSXO/BJmpdIKCRuKUgSCgAN0OwliVK3yPKbwIWU1Tdw5BRajxlzMidvh+gwko9AfQ9zIj52pzF91Q3YAvQ=="
  "resolved" "https://registry.npmjs.org/data-urls/-/data-urls-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "abab" "^2.0.3"
    "whatwg-mimetype" "^2.3.0"
    "whatwg-url" "^8.0.0"

"date-fns@>= 2.x", "date-fns@2.x":
  "integrity" "sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw=="
  "resolved" "https://registry.npmjs.org/date-fns/-/date-fns-2.30.0.tgz"
  "version" "2.30.0"
  dependencies:
    "@babel/runtime" "^7.21.0"

"dayjs@^1.11.1", "dayjs@^1.11.4", "dayjs@^1.11.9", "dayjs@>= 1.x", "dayjs@1.x":
  "integrity" "sha512-Rt2g+nTbLlDWZTwwrIXjy9MeiZmSDI375FvZs72ngxx8PDC6YXOeR3q5LAuPzjZQxhiWdRKac7RKV+YyQYfYIg=="
  "resolved" "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.12.tgz"
  "version" "1.11.12"

"debug@^2.2.0":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.3.3":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^4.1.0", "debug@^4.1.1", "debug@^4.2.0", "debug@4":
  "integrity" "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "ms" "2.1.2"

"decamelize@^1.2.0":
  "integrity" "sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA=="
  "resolved" "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz"
  "version" "1.2.0"

"decimal.js@^10.2.1":
  "integrity" "sha512-VBBaLc1MgL5XpzgIP7ny5Z6Nx3UrRkIViUkPUdtl9aya5amy3De1gsUUSB1g3+3sExYNjCAsAznmukyxCb1GRA=="
  "resolved" "https://registry.npmjs.org/decimal.js/-/decimal.js-10.4.3.tgz"
  "version" "10.4.3"

"decode-uri-component@^0.2.0":
  "integrity" "sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ=="
  "resolved" "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.2.tgz"
  "version" "0.2.2"

"dedent@^0.7.0":
  "integrity" "sha512-Q6fKUPqnAHAyhiUgFU7BUzLiv0kd8saH9al7tnu5Q/okj6dnupxyTgFIBjVzJATdfIAm9NAsvXNzjaKa+bxVyA=="
  "resolved" "https://registry.npmjs.org/dedent/-/dedent-0.7.0.tgz"
  "version" "0.7.0"

"deepmerge@^1.5.2":
  "integrity" "sha512-95k0GDqvBjZavkuvzx/YqVLv/6YYa17fz6ILMSf7neqQITCPbnfEnQvEgMPNjH4kgobe7+WIL0yJEHku+H3qtQ=="
  "resolved" "https://registry.npmjs.org/deepmerge/-/deepmerge-1.5.2.tgz"
  "version" "1.5.2"

"deepmerge@^4.2.2":
  "integrity" "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A=="
  "resolved" "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz"
  "version" "4.3.1"

"define-data-property@^1.1.4":
  "integrity" "sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A=="
  "resolved" "https://registry.npmmirror.com/define-data-property/-/define-data-property-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "es-define-property" "^1.0.0"
    "es-errors" "^1.3.0"
    "gopd" "^1.0.1"

"define-property@^0.2.5":
  "integrity" "sha512-Rr7ADjQZenceVOAKop6ALkkRAmH1A4Gx9hV/7ZujPUN2rkATqFO0JZLZInbAjpZYoJ1gUx8MRMQVkYemcbMSTA=="
  "resolved" "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "is-descriptor" "^0.1.0"

"define-property@^1.0.0":
  "integrity" "sha512-cZTYKFWspt9jZsMscWo8sc/5lbPC9Q0N5nBLgb+Yd915iL3udB1uFgS3B8YCx66UVHq018DAVFoee7x+gxggeA=="
  "resolved" "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-descriptor" "^1.0.0"

"define-property@^2.0.2":
  "integrity" "sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ=="
  "resolved" "https://registry.npmjs.org/define-property/-/define-property-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "is-descriptor" "^1.0.2"
    "isobject" "^3.0.1"

"delayed-stream@~1.0.0":
  "integrity" "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="
  "resolved" "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"des.js@^1.0.0":
  "integrity" "sha512-r17GxjhUCjSRy8aiJpr8/UadFIzMzJGexI3Nmz4ADi9LYSFx4gTBp80+NaX/YsXWWLhpZ7v/v/ubEc/bCNfKwg=="
  "resolved" "https://registry.npmjs.org/des.js/-/des.js-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"

"detect-newline@^3.0.0":
  "integrity" "sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA=="
  "resolved" "https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz"
  "version" "3.1.0"

"diff-sequences@^26.6.2":
  "integrity" "sha512-Mv/TDa3nZ9sbc5soK+OoA74BsS3mL37yixCvUAQkiuA4Wz6YtwP/K47n2rv2ovzHZvoiQeA5FTQOschKkEwB0Q=="
  "resolved" "https://registry.npmjs.org/diff-sequences/-/diff-sequences-26.6.2.tgz"
  "version" "26.6.2"

"diffie-hellman@^5.0.0":
  "integrity" "sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg=="
  "resolved" "https://registry.npmjs.org/diffie-hellman/-/diffie-hellman-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "miller-rabin" "^4.0.0"
    "randombytes" "^2.0.0"

"dom-align@^1.7.0", "dom-align@1.x":
  "integrity" "sha512-R8LUSEay/68zE5c8/3BDxiTEvgb4xZTF0RKmAHfiEVN3klfIpXfi2/QCoiWPccVQ0J/ZGdz9OjzL4uJEP/MRAw=="
  "resolved" "https://registry.npmjs.org/dom-align/-/dom-align-1.12.4.tgz"
  "version" "1.12.4"

"dom-walk@^0.1.0":
  "integrity" "sha512-6QvTW9mrGeIegrFXdtQi9pk7O/nSK6lSdXW2eqUspN5LWD7UTji2Fqw5V2YLjBpHEoU9Xl/eUWNpDeZvoyOv2w=="
  "resolved" "https://registry.npmjs.org/dom-walk/-/dom-walk-0.1.2.tgz"
  "version" "0.1.2"

"domain-browser@^1.1.1":
  "integrity" "sha512-jnjyiM6eRyZl2H+W8Q/zLMA481hzi0eszAaBUzIVnmYVDBbnLxVNnfu1HgEBvCbL+71FrxMl3E6lpKH7Ge3OXA=="
  "resolved" "https://registry.npmjs.org/domain-browser/-/domain-browser-1.2.0.tgz"
  "version" "1.2.0"

"domexception@^2.0.1":
  "integrity" "sha512-yxJ2mFy/sibVQlu5qHjOkf9J3K6zgmCxgJ94u2EdvDOV09H+32LtRswEcUsmUWN72pVLOEnTSRaIVVzVQgS0dg=="
  "resolved" "https://registry.npmjs.org/domexception/-/domexception-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "webidl-conversions" "^5.0.0"

"dva-core@^1.1.0 || ^1.5.0-0 || ^1.6.0-0", "dva-core@^1.5.0-beta.2":
  "integrity" "sha512-xmtr/J63EZXBdVXNBW+QCD7p9CaE8kAo2U1faRyv3PIGcy0G3Y6IBDNtoBB/Cj3nzk/jvX0dv96Hnh1kpSnI7Q=="
  "resolved" "https://registry.npmjs.org/dva-core/-/dva-core-1.5.0-beta.2.tgz"
  "version" "1.5.0-beta.2"
  dependencies:
    "@babel/runtime" "^7.0.0"
    "flatten" "^1.0.2"
    "global" "^4.3.2"
    "invariant" "^2.2.1"
    "is-plain-object" "^2.0.3"
    "redux" "^3.7.1"
    "redux-saga" "^0.16.0"
    "warning" "^3.0.0"

"dva-core@2.0.4":
  "integrity" "sha512-Zh39llFyItu9HKXKfCZVf9UFtDTcypdAjGBew1S+wK8BGVzFpm1GPTdd6uIMeg7O6STtCvt2Qv+RwUut1GFynA=="
  "resolved" "https://registry.npmjs.org/dva-core/-/dva-core-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "@babel/runtime" "^7.0.0"
    "flatten" "^1.0.2"
    "global" "^4.3.2"
    "invariant" "^2.2.1"
    "is-plain-object" "^2.0.3"
    "redux-saga" "^0.16.0"
    "warning" "^3.0.0"

"dva-immer@^0.5.2":
  "integrity" "sha512-xg23ktGsrdzgiU0ACl8vFm2+E2GBWbtXndXRKM9wtHDR+yMH27NfttVuAZQWvv4aaj6f5/bC+TdsZ9t7n6cAhQ=="
  "resolved" "https://registry.npmjs.org/dva-immer/-/dva-immer-0.5.2.tgz"
  "version" "0.5.2"
  dependencies:
    "@babel/runtime" "^7.0.0"
    "immer" "^7.0.5"

"dva-loading@^3.0.20":
  "integrity" "sha512-3j4bmuXOYH93xe+CC//z3Si8XMx6DLJveep+UbzKy0jhA7oQrCCZTdKxu0UPYXeAMYXpCO25pG4JOnVhzmC7ug=="
  "resolved" "https://registry.npmjs.org/dva-loading/-/dva-loading-3.0.24.tgz"
  "version" "3.0.24"
  dependencies:
    "@babel/runtime" "^7.0.0"

"dva@^2.5.0-0":
  "integrity" "sha512-kc2+CHhF1cNIU3Rg1miMhHgOKJ/VDrq9d6ynVBZf1EN2YKWU3MVFq/uTTBqMr2qkR0m9f8VKHOFmfKLtfMI93Q=="
  "resolved" "https://registry.npmjs.org/dva/-/dva-2.5.0-beta.2.tgz"
  "version" "2.5.0-beta.2"
  dependencies:
    "@babel/runtime" "^7.0.0"
    "@types/isomorphic-fetch" "^0.0.34"
    "@types/react-router-dom" "^4.2.7"
    "@types/react-router-redux" "^5.0.13"
    "dva-core" "^1.5.0-beta.2"
    "global" "^4.3.2"
    "history" "^4.6.3"
    "invariant" "^2.2.2"
    "isomorphic-fetch" "^2.2.1"
    "react-redux" "^5.0.5"
    "react-router-dom" "^4.1.2"
    "react-router-redux" "5.0.0-alpha.9"
    "redux" "^3.7.2"

"dva@^2.6.0-beta.20":
  "integrity" "sha512-noeOz3Erxpc6YLEsyyS//zGszeRYWKEvZoeCZ0LaCvWrV7Cxah9Xt/I9V1SS9eYDySaSgTUOoU+dLweQpCScAA=="
  "resolved" "https://registry.npmjs.org/dva/-/dva-2.6.0-beta.23.tgz"
  "version" "2.6.0-beta.23"
  dependencies:
    "@babel/runtime" "^7.0.0"
    "@types/isomorphic-fetch" "^0.0.35"
    "@types/react-redux" "^7.1.0"
    "@types/react-router-dom" "^5.1.2"
    "connected-react-router" "6.5.2"
    "dva-core" "2.0.4"
    "global" "^4.3.2"
    "history" "^4.7.2"
    "invariant" "^2.2.4"
    "isomorphic-fetch" "^2.2.1"
    "react-redux" "^7.1.0"
    "react-router-dom" "^5.1.2"
    "redux" "^4.0.1"

"electron-to-chromium@^1.4.431":
  "integrity" "sha512-GpmGRC1vTl60w/k6YpQ18pSiqnmr0j3un//5TV1idPi6aheNfkT1Ye71tMEabWyNDO6sBMgAR+95Eb0eUUr1tA=="
  "resolved" "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.471.tgz"
  "version" "1.4.471"

"elliptic@^6.5.3":
  "integrity" "sha512-iLhC6ULemrljPZb+QutR5TQGB+pdW6KGD5RSegS+8sorOZT+rdQFbsQFJgvN3eRqNALqJer4oQ16YvJHlU8hzQ=="
  "resolved" "https://registry.npmjs.org/elliptic/-/elliptic-6.5.4.tgz"
  "version" "6.5.4"
  dependencies:
    "bn.js" "^4.11.9"
    "brorand" "^1.1.0"
    "hash.js" "^1.0.0"
    "hmac-drbg" "^1.0.1"
    "inherits" "^2.0.4"
    "minimalistic-assert" "^1.0.1"
    "minimalistic-crypto-utils" "^1.0.1"

"emittery@^0.7.1":
  "integrity" "sha512-A8OG5SR/ij3SsJdWDJdkkSYUjQdCUx6APQXem0SaEePBSRg4eymGYwBkKo1Y6DU+af/Jn2dBQqDBvjnr9Vi8nQ=="
  "resolved" "https://registry.npmjs.org/emittery/-/emittery-0.7.2.tgz"
  "version" "0.7.2"

"emoji-regex@^8.0.0":
  "integrity" "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="
  "resolved" "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"emojis-list@^3.0.0":
  "integrity" "sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q=="
  "resolved" "https://registry.npmjs.org/emojis-list/-/emojis-list-3.0.0.tgz"
  "version" "3.0.0"

"encoding@^0.1.11":
  "integrity" "sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A=="
  "resolved" "https://registry.npmjs.org/encoding/-/encoding-0.1.13.tgz"
  "version" "0.1.13"
  dependencies:
    "iconv-lite" "^0.6.2"

"end-of-stream@^1.1.0":
  "integrity" "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q=="
  "resolved" "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz"
  "version" "1.4.4"
  dependencies:
    "once" "^1.4.0"

"enquirer@^2.3.6", "enquirer@>= 2.3.0 < 3":
  "integrity" "sha512-yjNnPr315/FjS4zIsUxYguYUPP2e1NK4d7E7ZOLiyYCcbFBiTMyID+2wvm2w6+pZ/odMA7cRkjhsPbltwBOrLg=="
  "resolved" "https://registry.npmjs.org/enquirer/-/enquirer-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "ansi-colors" "^4.1.1"

"error-ex@^1.3.1":
  "integrity" "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g=="
  "resolved" "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"error-stack-parser@^1.3.6":
  "integrity" "sha512-xhuSYd8wLgOXwNgjcPeXMPL/IiiA1Huck+OPvClpJViVNNlJVtM41o+1emp7bPvlCJwCatFX2DWc05/DgfbWzA=="
  "resolved" "https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-1.3.6.tgz"
  "version" "1.3.6"
  dependencies:
    "stackframe" "^0.3.1"

"error-stack-parser@^2.0.6":
  "integrity" "sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ=="
  "resolved" "https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "stackframe" "^1.3.4"

"es-define-property@^1.0.0":
  "integrity" "sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ=="
  "resolved" "https://registry.npmmirror.com/es-define-property/-/es-define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-intrinsic" "^1.2.4"

"es-errors@^1.3.0":
  "integrity" "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw=="
  "resolved" "https://registry.npmmirror.com/es-errors/-/es-errors-1.3.0.tgz"
  "version" "1.3.0"

"es-module-lexer@0.7.1":
  "integrity" "sha512-MgtWFl5No+4S3TmhDmCz2ObFGm6lEpTnzbQi+Dd+pw4mlTIZTmM2iAs5gRlmx5zS9luzobCSBSI90JM/1/JgOw=="
  "resolved" "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.7.1.tgz"
  "version" "0.7.1"

"es5-imcompatible-versions@^0.1.62":
  "integrity" "sha512-Lbrsn5bCL4iVMBdundiFVNIKlnnoBiIMrjtLRe1Snt92s60WHotw83S2ijp5ioqe6pDil3iBPY634VDwBcb1rg=="
  "resolved" "https://registry.npmjs.org/es5-imcompatible-versions/-/es5-imcompatible-versions-0.1.86.tgz"
  "version" "0.1.86"

"esbuild@0.12.15":
  "integrity" "sha512-72V4JNd2+48eOVCXx49xoSWHgC3/cCy96e7mbXKY+WOWghN00cCmlGnwVLRhRHorvv0dgCyuMYBZlM2xDM5OQw=="
  "resolved" "https://registry.npmjs.org/esbuild/-/esbuild-0.12.15.tgz"
  "version" "0.12.15"

"escalade@^3.1.1":
  "integrity" "sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw=="
  "resolved" "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz"
  "version" "3.1.1"

"escape-string-regexp@^1.0.5":
  "integrity" "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg=="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escape-string-regexp@^2.0.0":
  "integrity" "sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w=="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz"
  "version" "2.0.0"

"escodegen@^2.0.0":
  "integrity" "sha512-2NlIDTwUWJN0mRPQOdtQBzbUHvdGY2P1VXSyU83Q3xKxM7WHX2Ql8dKq782Q9TgQUNOLEzEYu9bzLNj1q88I5w=="
  "resolved" "https://registry.npmjs.org/escodegen/-/escodegen-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "esprima" "^4.0.1"
    "estraverse" "^5.2.0"
    "esutils" "^2.0.2"
  optionalDependencies:
    "source-map" "~0.6.1"

"esprima@^4.0.0", "esprima@^4.0.1":
  "integrity" "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="
  "resolved" "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
  "version" "4.0.1"

"estraverse@^5.2.0":
  "integrity" "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA=="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"esutils@^2.0.2":
  "integrity" "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="
  "resolved" "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  "version" "2.0.3"

"events@^3.0.0":
  "integrity" "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q=="
  "resolved" "https://registry.npmjs.org/events/-/events-3.3.0.tgz"
  "version" "3.3.0"

"evp_bytestokey@^1.0.0", "evp_bytestokey@^1.0.3":
  "integrity" "sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA=="
  "resolved" "https://registry.npmjs.org/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "md5.js" "^1.3.4"
    "safe-buffer" "^5.1.1"

"exec-sh@^0.3.2":
  "integrity" "sha512-nQn+hI3yp+oD0huYhKwvYI32+JFeq+XkNcD1GAo3Y/MjxsfVGmrrzrnzjWiNY6f+pUCP440fThsFh5gZrRAU/w=="
  "resolved" "https://registry.npmjs.org/exec-sh/-/exec-sh-0.3.6.tgz"
  "version" "0.3.6"

"execa@^0.8.0":
  "integrity" "sha512-zDWS+Rb1E8BlqqhALSt9kUhss8Qq4nN3iof3gsOdyINksElaPyNBtKUMTR62qhvgVWR0CqCX7sdnKe4MnUbFEA=="
  "resolved" "https://registry.npmjs.org/execa/-/execa-0.8.0.tgz"
  "version" "0.8.0"
  dependencies:
    "cross-spawn" "^5.0.1"
    "get-stream" "^3.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"execa@^1.0.0":
  "integrity" "sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA=="
  "resolved" "https://registry.npmjs.org/execa/-/execa-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "cross-spawn" "^6.0.0"
    "get-stream" "^4.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"execa@^4.0.0":
  "integrity" "sha512-j5W0//W7f8UxAn8hXVnwG8tLwdiUy4FJLcSupCg6maBYZDpyBvTApK7KyuI4bKj8KOh1r2YH+6ucuYtJv1bTZA=="
  "resolved" "https://registry.npmjs.org/execa/-/execa-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "cross-spawn" "^7.0.0"
    "get-stream" "^5.0.0"
    "human-signals" "^1.1.1"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.0"
    "onetime" "^5.1.0"
    "signal-exit" "^3.0.2"
    "strip-final-newline" "^2.0.0"

"execa@^4.1.0":
  "integrity" "sha512-j5W0//W7f8UxAn8hXVnwG8tLwdiUy4FJLcSupCg6maBYZDpyBvTApK7KyuI4bKj8KOh1r2YH+6ucuYtJv1bTZA=="
  "resolved" "https://registry.npmjs.org/execa/-/execa-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "cross-spawn" "^7.0.0"
    "get-stream" "^5.0.0"
    "human-signals" "^1.1.1"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.0"
    "onetime" "^5.1.0"
    "signal-exit" "^3.0.2"
    "strip-final-newline" "^2.0.0"

"exenv@^1.2.0":
  "integrity" "sha512-Z+ktTxTwv9ILfgKCk32OX3n/doe+OcLTRtqK9pcL+JsP3J1/VW8Uvl4ZjLlKqeW4rzK4oesDOGMEMRIZqtP4Iw=="
  "resolved" "https://registry.npmjs.org/exenv/-/exenv-1.2.2.tgz"
  "version" "1.2.2"

"exit@^0.1.2":
  "integrity" "sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ=="
  "resolved" "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz"
  "version" "0.1.2"

"expand-brackets@^2.1.4":
  "integrity" "sha512-w/ozOKR9Obk3qoWeY/WDi6MFta9AoMR+zud60mdnbniMcBxRuFJyDt2LdX/14A1UABeqk+Uk+LDfUpvoGKppZA=="
  "resolved" "https://registry.npmjs.org/expand-brackets/-/expand-brackets-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "debug" "^2.3.3"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "posix-character-classes" "^0.1.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"expect@^26.6.2":
  "integrity" "sha512-9/hlOBkQl2l/PLHJx6JjoDF6xPKcJEsUlWKb23rKE7KzeDqUZKXKNMW27KIue5JMdBV9HgmoJPcc8HtO85t9IA=="
  "resolved" "https://registry.npmjs.org/expect/-/expect-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "ansi-styles" "^4.0.0"
    "jest-get-type" "^26.3.0"
    "jest-matcher-utils" "^26.6.2"
    "jest-message-util" "^26.6.2"
    "jest-regex-util" "^26.0.0"

"extend-shallow@^2.0.1":
  "integrity" "sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug=="
  "resolved" "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extendable" "^0.1.0"

"extend-shallow@^3.0.0", "extend-shallow@^3.0.2":
  "integrity" "sha512-BwY5b5Ql4+qZoefgMj2NUmx+tehVTH/Kf4k1ZEtOHNFcm2wSxMRo992l6X3TIgni2eZVTZ85xMOjF31fwZAj6Q=="
  "resolved" "https://registry.npmjs.org/extend-shallow/-/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"extglob@^2.0.4":
  "integrity" "sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw=="
  "resolved" "https://registry.npmjs.org/extglob/-/extglob-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "array-unique" "^0.3.2"
    "define-property" "^1.0.0"
    "expand-brackets" "^2.1.4"
    "extend-shallow" "^2.0.1"
    "fragment-cache" "^0.2.1"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"fast-deep-equal@^3.1.1", "fast-deep-equal@^3.1.3":
  "integrity" "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="
  "resolved" "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-deep-equal@3.1.1":
  "integrity" "sha512-8UEa58QDLauDNfpbrX55Q9jrGHThw2ZMdOky5Gl1CDtVeJDPVrG4Jxx1N8jw2gkWaff5UUuX1KJd+9zGe2B+ZA=="
  "resolved" "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.1.tgz"
  "version" "3.1.1"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="
  "resolved" "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fb-watchman@^2.0.0":
  "integrity" "sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA=="
  "resolved" "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "bser" "2.1.1"

"fbjs@^0.8.3":
  "integrity" "sha512-EQaWFK+fEPSoibjNy8IxUtaFOMXcWsY0JaVrQoZR9zC8N2Ygf9iDITPWjUTVIax95b6I742JFLqASHfsag/vKA=="
  "resolved" "https://registry.npmjs.org/fbjs/-/fbjs-0.8.18.tgz"
  "version" "0.8.18"
  dependencies:
    "core-js" "^1.0.0"
    "isomorphic-fetch" "^2.1.1"
    "loose-envify" "^1.0.0"
    "object-assign" "^4.1.0"
    "promise" "^7.1.1"
    "setimmediate" "^1.0.5"
    "ua-parser-js" "^0.7.30"

"fill-range@^4.0.0":
  "integrity" "sha512-VcpLTWqWDiTerugjj8e3+esbg+skS3M9e54UuR3iCeIDMXCLTsAH8hTSzDQU/X6/6t3eYkOKoZSef2PlU6U1XQ=="
  "resolved" "https://registry.npmjs.org/fill-range/-/fill-range-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"
    "to-regex-range" "^2.1.0"

"fill-range@^7.0.1":
  "integrity" "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ=="
  "resolved" "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"filter-obj@^1.1.0":
  "integrity" "sha512-8rXg1ZnX7xzy2NGDVkBVaAy+lSlPNwad13BtgSlLuxfIslyt5Vg64U7tFcCt4WS1R0hvtnQybT/IyCkGZ3DpXQ=="
  "resolved" "https://registry.npmjs.org/filter-obj/-/filter-obj-1.1.0.tgz"
  "version" "1.1.0"

"find-up@^4.0.0", "find-up@^4.1.0":
  "integrity" "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"flatten@^1.0.2":
  "integrity" "sha512-dVsPA/UwQ8+2uoFe5GHtiBMu48dWLTdsuEd7CKGlZlD78r1TTWBvDuFaFGKCo/ZfEr95Uk56vZoX86OsHkUeIg=="
  "resolved" "https://registry.npmjs.org/flatten/-/flatten-1.0.3.tgz"
  "version" "1.0.3"

"follow-redirects@^1.15.6":
  "integrity" "sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA=="
  "resolved" "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.6.tgz"
  "version" "1.15.6"

"for-in@^1.0.2":
  "integrity" "sha512-7EwmXrOjyL+ChxMhmG5lnW9MPt1aIeZEwKhQzoBUdTV0N3zuwWDZYVJatDvZ2OyzPUvdIAZDsCetk3coyMfcnQ=="
  "resolved" "https://registry.npmjs.org/for-in/-/for-in-1.0.2.tgz"
  "version" "1.0.2"

"form-data@^3.0.0":
  "integrity" "sha512-RHkBKtLWUVwd7SqRIvCZMEvAMoGUp0XU+seQiZejj0COz3RI3hWP4sCv3gZWWLjJTd7rGwcsF5eKZGii0r/hbg=="
  "resolved" "https://registry.npmjs.org/form-data/-/form-data-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.8"
    "mime-types" "^2.1.12"

"form-data@^4.0.0":
  "integrity" "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww=="
  "resolved" "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.8"
    "mime-types" "^2.1.12"

"frac@~1.1.2":
  "integrity" "sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA=="
  "resolved" "https://registry.npmmirror.com/frac/-/frac-1.1.2.tgz"
  "version" "1.1.2"

"fragment-cache@^0.2.1":
  "integrity" "sha512-GMBAbW9antB8iZRHLoGw0b3HANt57diZYFO/HL1JGIC1MjKrdmhxvrJbupnVvpys0zsz7yBApXdQyfepKly2kA=="
  "resolved" "https://registry.npmjs.org/fragment-cache/-/fragment-cache-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "map-cache" "^0.2.2"

"fs.realpath@^1.0.0":
  "integrity" "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw=="
  "resolved" "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"fsevents@^2.1.2", "fsevents@~2.3.1":
  "integrity" "sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA=="
  "resolved" "https://registry.npmjs.org/fsevents/-/fsevents-2.3.2.tgz"
  "version" "2.3.2"

"function-bind@^1.1.1", "function-bind@^1.1.2":
  "integrity" "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="
  "resolved" "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.2.tgz"
  "version" "1.1.2"

"fuse.js@^7.0.0":
  "integrity" "sha512-14F4hBIxqKvD4Zz/XjDc3y94mNZN6pRv3U13Udo0lNLCWRBUsrMv2xwcF/y/Z5sV6+FQW+/ow68cHpm4sunt8Q=="
  "resolved" "https://registry.npmjs.org/fuse.js/-/fuse.js-7.0.0.tgz"
  "version" "7.0.0"

"gensync@^1.0.0-beta.2":
  "integrity" "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg=="
  "resolved" "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
  "version" "1.0.0-beta.2"

"get-caller-file@^2.0.1":
  "integrity" "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg=="
  "resolved" "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-intrinsic@^1.1.3", "get-intrinsic@^1.2.4":
  "integrity" "sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ=="
  "resolved" "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"
    "has-proto" "^1.0.1"
    "has-symbols" "^1.0.3"
    "hasown" "^2.0.0"

"get-own-enumerable-property-symbols@^3.0.0":
  "integrity" "sha512-I0UBV/XOz1XkIJHEUDMZAbzCThU/H8DxmSfmdGcKPnVhu2VfFqr34jr9777IyaTYvxjedWhqVIilEDsCdP5G6g=="
  "resolved" "https://registry.npmjs.org/get-own-enumerable-property-symbols/-/get-own-enumerable-property-symbols-3.0.2.tgz"
  "version" "3.0.2"

"get-package-type@^0.1.0":
  "integrity" "sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q=="
  "resolved" "https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz"
  "version" "0.1.0"

"get-stream@^3.0.0":
  "integrity" "sha512-GlhdIUuVakc8SJ6kK0zAFbiGzRFzNnY4jUuEbV9UROo4Y+0Ny4fjvcZFVTeDA4odpFyOQzaw6hXukJSq/f28sQ=="
  "resolved" "https://registry.npmjs.org/get-stream/-/get-stream-3.0.0.tgz"
  "version" "3.0.0"

"get-stream@^4.0.0":
  "integrity" "sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w=="
  "resolved" "https://registry.npmjs.org/get-stream/-/get-stream-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "pump" "^3.0.0"

"get-stream@^5.0.0":
  "integrity" "sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA=="
  "resolved" "https://registry.npmjs.org/get-stream/-/get-stream-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "pump" "^3.0.0"

"get-value@^2.0.3", "get-value@^2.0.6":
  "integrity" "sha512-Ln0UQDlxH1BapMu3GPtf7CuYNwRZf2gwCuPqbyG6pB8WfmFpzqcy4xtAaAMUhnNqjMKTiCPZG2oMT3YSx8U2NA=="
  "resolved" "https://registry.npmjs.org/get-value/-/get-value-2.0.6.tgz"
  "version" "2.0.6"

"glob-parent@~5.1.0":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob@^7.1.1", "glob@^7.1.2", "glob@^7.1.3", "glob@^7.1.4":
  "integrity" "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz"
  "version" "7.2.3"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.1.1"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"global@^4.3.2":
  "integrity" "sha512-wv/LAoHdRE3BeTGz53FAamhGlPLhlssK45usmGFThIi4XqnBmjKQ16u+RNbP7WvigRZDxUsM0J3gcQ5yicaL0w=="
  "resolved" "https://registry.npmjs.org/global/-/global-4.4.0.tgz"
  "version" "4.4.0"
  dependencies:
    "min-document" "^2.19.0"
    "process" "^0.11.10"

"globals@^11.1.0":
  "integrity" "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  "version" "11.12.0"

"gopd@^1.0.1":
  "integrity" "sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA=="
  "resolved" "https://registry.npmmirror.com/gopd/-/gopd-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "get-intrinsic" "^1.1.3"

"graceful-fs@^4.2.4":
  "integrity" "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ=="
  "resolved" "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz"
  "version" "4.2.11"

"growly@^1.3.0":
  "integrity" "sha512-+xGQY0YyAWCnqy7Cd++hc2JqMYzlm0dG30Jd0beaA64sROr8C4nt8Yc9V5Ro3avlSUDTN0ulqP/VBKi1/lLygw=="
  "resolved" "https://registry.npmjs.org/growly/-/growly-1.3.0.tgz"
  "version" "1.3.0"

"harmony-reflect@^1.4.6":
  "integrity" "sha512-HIp/n38R9kQjDEziXyDTuW3vvoxxyxjxFzXLrBr18uB47GnSt+G9D29fqrpM5ZkspMcPICud3XsBJQ4Y2URg8g=="
  "resolved" "https://registry.npmjs.org/harmony-reflect/-/harmony-reflect-1.6.2.tgz"
  "version" "1.6.2"

"has-flag@^3.0.0":
  "integrity" "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw=="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-flag@^4.0.0":
  "integrity" "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-property-descriptors@^1.0.2":
  "integrity" "sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg=="
  "resolved" "https://registry.npmmirror.com/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "es-define-property" "^1.0.0"

"has-proto@^1.0.1":
  "integrity" "sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q=="
  "resolved" "https://registry.npmmirror.com/has-proto/-/has-proto-1.0.3.tgz"
  "version" "1.0.3"

"has-symbols@^1.0.3":
  "integrity" "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A=="
  "resolved" "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz"
  "version" "1.0.3"

"has-value@^0.3.1":
  "integrity" "sha512-gpG936j8/MzaeID5Yif+577c17TxaDmhuyVgSwtnL/q8UUTySg8Mecb+8Cf1otgLoD7DDH75axp86ER7LFsf3Q=="
  "resolved" "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "get-value" "^2.0.3"
    "has-values" "^0.1.4"
    "isobject" "^2.0.0"

"has-value@^1.0.0":
  "integrity" "sha512-IBXk4GTsLYdQ7Rvt+GRBrFSVEkmuOUy4re0Xjd9kJSUQpnTrWR4/y9RpfexN9vkAPMFuQoeWKwqzPozRTlasGw=="
  "resolved" "https://registry.npmjs.org/has-value/-/has-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-value" "^2.0.6"
    "has-values" "^1.0.0"
    "isobject" "^3.0.0"

"has-values@^0.1.4":
  "integrity" "sha512-J8S0cEdWuQbqD9//tlZxiMuMNmxB8PlEwvYwuxsTmR1G5RXUePEX/SJn7aD0GMLieuZYSwNH0cQuJGwnYunXRQ=="
  "resolved" "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz"
  "version" "0.1.4"

"has-values@^1.0.0":
  "integrity" "sha512-ODYZC64uqzmtfGMEAX/FvZiRyWLpAC3vYnNunURUnkGVTS+mI0smVsWaPydRBsE3g+ok7h960jChO8mFcWlHaQ=="
  "resolved" "https://registry.npmjs.org/has-values/-/has-values-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-number" "^3.0.0"
    "kind-of" "^4.0.0"

"has@^1.0.3":
  "integrity" "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw=="
  "resolved" "https://registry.npmjs.org/has/-/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"hash-base@^3.0.0":
  "integrity" "sha512-1nmYp/rhMDiE7AYkDw+lLwlAzz0AntGIe51F3RfFfEqyQ3feY2eI/NcwC6umIQVOASPMsWJLJScWKSSvzL9IVA=="
  "resolved" "https://registry.npmjs.org/hash-base/-/hash-base-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "inherits" "^2.0.4"
    "readable-stream" "^3.6.0"
    "safe-buffer" "^5.2.0"

"hash.js@^1.0.0", "hash.js@^1.0.3":
  "integrity" "sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA=="
  "resolved" "https://registry.npmjs.org/hash.js/-/hash.js-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "inherits" "^2.0.3"
    "minimalistic-assert" "^1.0.1"

"hasown@^2.0.0":
  "integrity" "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ=="
  "resolved" "https://registry.npmmirror.com/hasown/-/hasown-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "function-bind" "^1.1.2"

"history-with-query@4.10.4":
  "integrity" "sha512-JnskQK8X+PbRFHSdDAExhoJyhLnlLZL+UuHQuQhys+Se9/ukRDRBWU4JVTjsiIfbv1fcEmR3oqKW56OYmk5M5w=="
  "resolved" "https://registry.npmjs.org/history-with-query/-/history-with-query-4.10.4.tgz"
  "version" "4.10.4"
  dependencies:
    "@babel/runtime" "^7.1.2"
    "loose-envify" "^1.2.0"
    "query-string" "^6.11.0"
    "resolve-pathname" "^3.0.0"
    "tiny-invariant" "^1.0.2"
    "tiny-warning" "^1.0.0"
    "value-equal" "^1.0.1"

"history@^4.6.3", "history@^4.7.2", "history@^4.9.0":
  "integrity" "sha512-36nwAD620w12kuzPAsyINPWJqlNbij+hpK1k9XRloDtym8mxzGYl2c17LnV6IAGB2Dmg4tEa7G7DlawS0+qjew=="
  "resolved" "https://registry.npmjs.org/history/-/history-4.10.1.tgz"
  "version" "4.10.1"
  dependencies:
    "@babel/runtime" "^7.1.2"
    "loose-envify" "^1.2.0"
    "resolve-pathname" "^3.0.0"
    "tiny-invariant" "^1.0.2"
    "tiny-warning" "^1.0.0"
    "value-equal" "^1.0.1"

"hmac-drbg@^1.0.1":
  "integrity" "sha512-Tti3gMqLdZfhOQY1Mzf/AanLiqh1WTiJgEj26ZuYQ9fbkLomzGchCws4FyrSd4VkpBfiNhaE1On+lOz894jvXg=="
  "resolved" "https://registry.npmjs.org/hmac-drbg/-/hmac-drbg-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "hash.js" "^1.0.3"
    "minimalistic-assert" "^1.0.0"
    "minimalistic-crypto-utils" "^1.0.1"

"hoist-non-react-statics@^2.5.0":
  "integrity" "sha512-rqcy4pJo55FTTLWt+bU8ukscqHeE/e9KWvsOW2b/a3afxQZhwkQdT1rPPCJ0rYXdj4vNcasY8zHTH+jF/qStxw=="
  "resolved" "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.5.5.tgz"
  "version" "2.5.5"

"hoist-non-react-statics@^3.1.0", "hoist-non-react-statics@^3.3.0", "hoist-non-react-statics@^3.3.2":
  "integrity" "sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw=="
  "resolved" "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz"
  "version" "3.3.2"
  dependencies:
    "react-is" "^16.7.0"

"hosted-git-info@^2.1.4":
  "integrity" "sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw=="
  "resolved" "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.8.9.tgz"
  "version" "2.8.9"

"html-encoding-sniffer@^2.0.1":
  "integrity" "sha512-D5JbOMBIR/TVZkubHT+OyT2705QvogUW4IBn6nHd756OwieSF9aDYFj4dv6HHEVGYbHaLETa3WggZYWWMyy3ZQ=="
  "resolved" "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "whatwg-encoding" "^1.0.5"

"html-entities@^2.1.0":
  "integrity" "sha512-igBTJcNNNhvZFRtm8uA6xMY6xYleeDwn3PeBCkDz7tHttv4F2hsDI2aPgNERWzvRcNYHNT3ymRaQzllmXj4YsQ=="
  "resolved" "https://registry.npmjs.org/html-entities/-/html-entities-2.4.0.tgz"
  "version" "2.4.0"

"html-escaper@^2.0.0":
  "integrity" "sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg=="
  "resolved" "https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz"
  "version" "2.0.2"

"http-proxy-agent@^4.0.1":
  "integrity" "sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg=="
  "resolved" "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "@tootallnate/once" "1"
    "agent-base" "6"
    "debug" "4"

"https-browserify@^1.0.0":
  "integrity" "sha512-J+FkSdyD+0mA0N+81tMotaRMfSL9SGi+xpD3T6YApKsc3bGSXJlfXri3VyFOeYkfLRQisDk1W+jIFFKBeUBbBg=="
  "resolved" "https://registry.npmjs.org/https-browserify/-/https-browserify-1.0.0.tgz"
  "version" "1.0.0"

"https-proxy-agent@^5.0.0":
  "integrity" "sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA=="
  "resolved" "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "agent-base" "6"
    "debug" "4"

"human-signals@^1.1.1":
  "integrity" "sha512-SEQu7vl8KjNL2eoGBLF3+wAjpsNfA9XMlXAYj/3EdaNfAlxKthD1xjEQfGOUhllCGGJVNY34bRr6lPINhNjyZw=="
  "resolved" "https://registry.npmjs.org/human-signals/-/human-signals-1.1.1.tgz"
  "version" "1.1.1"

"iconv-lite@^0.6.2":
  "integrity" "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz"
  "version" "0.6.3"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3.0.0"

"iconv-lite@0.4.24":
  "integrity" "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"identity-obj-proxy@3.0.0":
  "integrity" "sha512-00n6YnVHKrinT9t0d9+5yZC6UBNJANpYEQvL2LlX6Ab9lnmxzIRcEmTPuyGScvl1+jKuCICX1Z0Ab1pPKKdikA=="
  "resolved" "https://registry.npmjs.org/identity-obj-proxy/-/identity-obj-proxy-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "harmony-reflect" "^1.4.6"

"ieee754@^1.1.4":
  "integrity" "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA=="
  "resolved" "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz"
  "version" "1.2.1"

"immer@^7.0.5":
  "integrity" "sha512-yM7jo9+hvYgvdCQdqvhCNRRio0SCXc8xDPzA25SvKWa7b1WVPjLwQs1VYU5JPXjcJPTqAa5NP5dqpORGYBQ2AA=="
  "resolved" "https://registry.npmjs.org/immer/-/immer-7.0.15.tgz"
  "version" "7.0.15"

"immutable@^3.8.1":
  "integrity" "sha512-15gZoQ38eYjEjxkorfbcgBKBL6R7T459OuK+CpcWt7O3KF4uPCx2tD0uFETlUDIyo+1789crbMhTvQBSR5yBMg=="
  "resolved" "https://registry.npmjs.org/immutable/-/immutable-3.8.2.tgz"
  "version" "3.8.2"

"import-cwd@^2.0.0":
  "integrity" "sha512-Ew5AZzJQFqrOV5BTW3EIoHAnoie1LojZLXKcCQ/yTRyVZosBhK1x1ViYjHGf5pAFOq8ZyChZp6m/fSN7pJyZtg=="
  "resolved" "https://registry.npmjs.org/import-cwd/-/import-cwd-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "import-from" "^2.1.0"

"import-fresh@^2.0.0":
  "integrity" "sha512-eZ5H8rcgYazHbKC3PG4ClHNykCSxtAhxSSEM+2mb+7evD2CKF5V7c0dNum7AdpDh0ZdICwZY9sRSn8f+KH96sg=="
  "resolved" "https://registry.npmjs.org/import-fresh/-/import-fresh-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-path" "^2.0.0"
    "resolve-from" "^3.0.0"

"import-fresh@^3.2.1":
  "integrity" "sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw=="
  "resolved" "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"import-from@^2.1.0":
  "integrity" "sha512-0vdnLL2wSGnhlRmzHJAg5JHjt1l2vYhzJ7tNLGbeVg0fse56tpGaH0uzH+r9Slej+BSXXEHvBKDEnVSLLE9/+w=="
  "resolved" "https://registry.npmjs.org/import-from/-/import-from-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "resolve-from" "^3.0.0"

"import-local@^3.0.2":
  "integrity" "sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg=="
  "resolved" "https://registry.npmjs.org/import-local/-/import-local-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "pkg-dir" "^4.2.0"
    "resolve-cwd" "^3.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA=="
  "resolved" "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indent-string@^4.0.0":
  "integrity" "sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg=="
  "resolved" "https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz"
  "version" "4.0.0"

"indexes-of@^1.0.1":
  "integrity" "sha512-bup+4tap3Hympa+JBJUG7XuOsdNQ6fxt0MHyXMKuLBKn0OqsTfvUxkUrroEX1+B2VsSHvCjiIcZVxRtYa4nllA=="
  "resolved" "https://registry.npmjs.org/indexes-of/-/indexes-of-1.0.1.tgz"
  "version" "1.0.1"

"inflight@^1.0.4":
  "integrity" "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA=="
  "resolved" "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.1", "inherits@^2.0.3", "inherits@^2.0.4", "inherits@~2.0.1", "inherits@~2.0.3", "inherits@2":
  "integrity" "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  "version" "2.0.4"

"inherits@2.0.1":
  "integrity" "sha512-8nWq2nLTAwd02jTqJExUYFSD/fKq6VH9Y/oG2accc/kdI0V98Bag8d5a4gi3XHz73rDWa2PvTtvcWYquKqSENA=="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.1.tgz"
  "version" "2.0.1"

"inherits@2.0.3":
  "integrity" "sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw=="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz"
  "version" "2.0.3"

"intl-format-cache@^4.2.21":
  "integrity" "sha512-OEUYNA7D06agqPOYhbTkl0T8HA3QKSuwWh1HiClEnpd9vw7N+3XsQt5iZ0GUEchp5CW1fQk/tary+NsbF3yQ1Q=="
  "resolved" "https://registry.npmjs.org/intl-format-cache/-/intl-format-cache-4.3.1.tgz"
  "version" "4.3.1"

"intl-messageformat-parser@^3.6.4":
  "integrity" "sha512-RgPGwue0mJtoX2Ax8EmMzJzttxjnva7gx0Q7mKJ4oALrTZvtmCeAw5Msz2PcjW4dtCh/h7vN/8GJCxZO1uv+OA=="
  "resolved" "https://registry.npmjs.org/intl-messageformat-parser/-/intl-messageformat-parser-3.6.4.tgz"
  "version" "3.6.4"
  dependencies:
    "@formatjs/intl-unified-numberformat" "^3.2.0"

"intl-messageformat@^7.8.4":
  "integrity" "sha512-yS0cLESCKCYjseCOGXuV4pxJm/buTfyCJ1nzQjryHmSehlptbZbn9fnlk1I9peLopZGGbjj46yHHiTAEZ1qOTA=="
  "resolved" "https://registry.npmjs.org/intl-messageformat/-/intl-messageformat-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "intl-format-cache" "^4.2.21"
    "intl-messageformat-parser" "^3.6.4"

"intl@1.2.5":
  "integrity" "sha512-rK0KcPHeBFBcqsErKSpvZnrOmWOj+EmDkyJ57e90YWaQNqbcivcqmKDlHEeNprDWOsKzPsh1BfSpPQdDvclHVw=="
  "resolved" "https://registry.npmjs.org/intl/-/intl-1.2.5.tgz"
  "version" "1.2.5"

"invariant@^2.2.1", "invariant@^2.2.2", "invariant@^2.2.4":
  "integrity" "sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA=="
  "resolved" "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "loose-envify" "^1.0.0"

"is-accessor-descriptor@^0.1.6":
  "integrity" "sha512-e1BM1qnDbMRG3ll2U9dSK0UMHuWOs3pY3AtcFsmvwPtKL3MML/Q86i+GilLfvqEs4GW+ExB91tQ3Ig9noDIZ+A=="
  "resolved" "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "kind-of" "^3.0.2"

"is-accessor-descriptor@^1.0.0":
  "integrity" "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ=="
  "resolved" "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-arrayish@^0.2.1":
  "integrity" "sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg=="
  "resolved" "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-binary-path@~2.1.0":
  "integrity" "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw=="
  "resolved" "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-buffer@^1.1.5":
  "integrity" "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="
  "resolved" "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-ci@^1.0.10":
  "integrity" "sha512-s6tfsaQaQi3JNciBH6shVqEDvhGut0SUXr31ag8Pd8BBbVVlcGfWhpPmEOoM6RJ5TFhbypvf5yyRw/VXW1IiWg=="
  "resolved" "https://registry.npmjs.org/is-ci/-/is-ci-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "ci-info" "^1.5.0"

"is-ci@^2.0.0":
  "integrity" "sha512-YfJT7rkpQB0updsdHLGWrvhBJfcfzNNawYDNIyQXJz0IViGf75O8EBPKSdvw2rF+LGCsX4FZ8tcr3b19LcZq4w=="
  "resolved" "https://registry.npmjs.org/is-ci/-/is-ci-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ci-info" "^2.0.0"

"is-core-module@^2.11.0":
  "integrity" "sha512-Q4ZuBAe2FUsKtyQJoQHlvP8OvBERxO3jEmy1I7hcRXcJBGGHFh/aJBswbXuS9sgrDH2QUO8ilkwNPHvHMd8clg=="
  "resolved" "https://registry.npmjs.org/is-core-module/-/is-core-module-2.12.1.tgz"
  "version" "2.12.1"
  dependencies:
    "has" "^1.0.3"

"is-data-descriptor@^0.1.4":
  "integrity" "sha512-+w9D5ulSoBNlmw9OHn3U2v51SyoCd0he+bB3xMl62oijhrspxowjU+AIcDY0N3iEJbUEkB15IlMASQsxYigvXg=="
  "resolved" "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"

"is-data-descriptor@^1.0.0":
  "integrity" "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ=="
  "resolved" "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-descriptor@^0.1.0":
  "integrity" "sha512-avDYr0SB3DwO9zsMov0gKCESFYqCnE4hq/4z3TdUlukEy5t9C0YRq7HLrsN52NAcqXKaepeCD0n+B0arnVG3Hg=="
  "resolved" "https://registry.npmjs.org/is-descriptor/-/is-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "is-accessor-descriptor" "^0.1.6"
    "is-data-descriptor" "^0.1.4"
    "kind-of" "^5.0.0"

"is-descriptor@^1.0.0":
  "integrity" "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg=="
  "resolved" "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-descriptor@^1.0.2":
  "integrity" "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg=="
  "resolved" "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-directory@^0.3.1":
  "integrity" "sha512-yVChGzahRFvbkscn2MlwGismPO12i9+znNruC5gVEntG3qu0xQMzsGg/JFbrsqDOHtHFPci+V5aP5T9I+yeKqw=="
  "resolved" "https://registry.npmjs.org/is-directory/-/is-directory-0.3.1.tgz"
  "version" "0.3.1"

"is-docker@^2.0.0":
  "integrity" "sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ=="
  "resolved" "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz"
  "version" "2.2.1"

"is-extendable@^0.1.0", "is-extendable@^0.1.1":
  "integrity" "sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw=="
  "resolved" "https://registry.npmjs.org/is-extendable/-/is-extendable-0.1.1.tgz"
  "version" "0.1.1"

"is-extendable@^1.0.1":
  "integrity" "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA=="
  "resolved" "https://registry.npmjs.org/is-extendable/-/is-extendable-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"

"is-extglob@^2.1.1":
  "integrity" "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="
  "resolved" "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="
  "resolved" "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-generator-fn@^2.0.0":
  "integrity" "sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ=="
  "resolved" "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz"
  "version" "2.1.0"

"is-glob@^4.0.1", "is-glob@~4.0.1":
  "integrity" "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-number@^3.0.0":
  "integrity" "sha512-4cboCqIpliH+mAvFNegjZQ4kgKc3ZUhQVr3HvWbSh5q3WH2v82ct+T2Y1hdU5Gdtorx/cLifQjqCbL7bpznLTg=="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "kind-of" "^3.0.2"

"is-number@^7.0.0":
  "integrity" "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-obj@^1.0.1":
  "integrity" "sha512-l4RyHgRqGN4Y3+9JHVrNqO+tN0rV5My76uW5/nuO4K1b6vw5G8d/cmFjP9tRfEsdhZNt0IFdZuK/c2Vr4Nb+Qg=="
  "resolved" "https://registry.npmjs.org/is-obj/-/is-obj-1.0.1.tgz"
  "version" "1.0.1"

"is-plain-obj@^1.0.0":
  "integrity" "sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg=="
  "resolved" "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-1.1.0.tgz"
  "version" "1.1.0"

"is-plain-object@^2.0.3", "is-plain-object@^2.0.4":
  "integrity" "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og=="
  "resolved" "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "^3.0.1"

"is-potential-custom-element-name@^1.0.1":
  "integrity" "sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ=="
  "resolved" "https://registry.npmjs.org/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.1.tgz"
  "version" "1.0.1"

"is-regexp@^1.0.0":
  "integrity" "sha512-7zjFAPO4/gwyQAAgRRmqeEeyIICSdmCqa3tsVHMdBzaXXRiqopZL4Cyghg/XulGWrtABTpbnYYzzIRffLkP4oA=="
  "resolved" "https://registry.npmjs.org/is-regexp/-/is-regexp-1.0.0.tgz"
  "version" "1.0.0"

"is-stream@^1.0.1", "is-stream@^1.1.0":
  "integrity" "sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ=="
  "resolved" "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^2.0.0":
  "integrity" "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg=="
  "resolved" "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz"
  "version" "2.0.1"

"is-typedarray@^1.0.0":
  "integrity" "sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA=="
  "resolved" "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz"
  "version" "1.0.0"

"is-unicode-supported@^0.1.0":
  "integrity" "sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw=="
  "resolved" "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz"
  "version" "0.1.0"

"is-windows@^1.0.2":
  "integrity" "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA=="
  "resolved" "https://registry.npmjs.org/is-windows/-/is-windows-1.0.2.tgz"
  "version" "1.0.2"

"is-wsl@^2.1.1", "is-wsl@^2.2.0":
  "integrity" "sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww=="
  "resolved" "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "is-docker" "^2.0.0"

"isarray@^1.0.0", "isarray@~1.0.0", "isarray@1.0.0":
  "integrity" "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ=="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isarray@0.0.1":
  "integrity" "sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ=="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz"
  "version" "0.0.1"

"isexe@^2.0.0":
  "integrity" "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="
  "resolved" "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"isobject@^2.0.0":
  "integrity" "sha512-+OUdGJlgjOBZDfxnDjYYG6zp487z0JGNQq3cYQYg5f5hKR+syHMsaztzGeml/4kGG55CSpKSpWTY+jYGgsHLgA=="
  "resolved" "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^3.0.0", "isobject@^3.0.1":
  "integrity" "sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg=="
  "resolved" "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz"
  "version" "3.0.1"

"isomorphic-fetch@^2.1.1", "isomorphic-fetch@^2.2.1":
  "integrity" "sha512-9c4TNAKYXM5PRyVcwUZrF3W09nQ+sO7+jydgs4ZGW9dhsLG2VOlISJABombdQqQRXCwuYG3sYV/puGf5rp0qmA=="
  "resolved" "https://registry.npmjs.org/isomorphic-fetch/-/isomorphic-fetch-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "node-fetch" "^1.0.1"
    "whatwg-fetch" ">=0.10.0"

"istanbul-lib-coverage@^3.0.0", "istanbul-lib-coverage@^3.2.0":
  "integrity" "sha512-eOeJ5BHCmHYvQK7xt9GkdHuzuCGS1Y6g9Gvnx3Ym33fz/HpLRYxiS0wHNr+m/MBC8B647Xt608vCDEvhl9c6Mw=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.0.tgz"
  "version" "3.2.0"

"istanbul-lib-instrument@^4.0.3":
  "integrity" "sha512-BXgQl9kf4WTCPCCpmFGoJkz/+uhvm7h7PFKUYxh7qarQd3ER33vHG//qaE8eN25l07YqZPpHXU9I09l/RD5aGQ=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "@babel/core" "^7.7.5"
    "@istanbuljs/schema" "^0.1.2"
    "istanbul-lib-coverage" "^3.0.0"
    "semver" "^6.3.0"

"istanbul-lib-instrument@^5.0.4":
  "integrity" "sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    "istanbul-lib-coverage" "^3.2.0"
    "semver" "^6.3.0"

"istanbul-lib-report@^3.0.0":
  "integrity" "sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "istanbul-lib-coverage" "^3.0.0"
    "make-dir" "^4.0.0"
    "supports-color" "^7.1.0"

"istanbul-lib-source-maps@^4.0.0":
  "integrity" "sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "debug" "^4.1.1"
    "istanbul-lib-coverage" "^3.0.0"
    "source-map" "^0.6.1"

"istanbul-reports@^3.0.2":
  "integrity" "sha512-TLgnMkKg3iTDsQ9PbPTdpfAK2DzjF9mqUG7RMgcQl8oFjad8ob4laGxv5XV5U9MAfx8D6tSJiUyuAwzLicaxlg=="
  "resolved" "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.6.tgz"
  "version" "3.1.6"
  dependencies:
    "html-escaper" "^2.0.0"
    "istanbul-lib-report" "^3.0.0"

"javascript-stringify@^2.0.1":
  "integrity" "sha512-JVAfqNPTvNq3sB/VHQJAFxN/sPgKnsKrCwyRt15zwNCdrMMJDdcEOdubuy+DuJYYdm0ox1J4uzEuYKkN+9yhVg=="
  "resolved" "https://registry.npmjs.org/javascript-stringify/-/javascript-stringify-2.1.0.tgz"
  "version" "2.1.0"

"jest-changed-files@^26.6.2":
  "integrity" "sha512-fDS7szLcY9sCtIip8Fjry9oGf3I2ht/QT21bAHm5Dmf0mD4X3ReNUf17y+bO6fR8WgbIZTlbyG1ak/53cbRzKQ=="
  "resolved" "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "execa" "^4.0.0"
    "throat" "^5.0.0"

"jest-cli@^26.6.3":
  "integrity" "sha512-GF9noBSa9t08pSyl3CY4frMrqp+aQXFGFkf5hEPbh/pIUFYWMK6ZLTfbmadxJVcJrdRoChlWQsA2VkJcDFK8hg=="
  "resolved" "https://registry.npmjs.org/jest-cli/-/jest-cli-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@jest/core" "^26.6.3"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "chalk" "^4.0.0"
    "exit" "^0.1.2"
    "graceful-fs" "^4.2.4"
    "import-local" "^3.0.2"
    "is-ci" "^2.0.0"
    "jest-config" "^26.6.3"
    "jest-util" "^26.6.2"
    "jest-validate" "^26.6.2"
    "prompts" "^2.0.1"
    "yargs" "^15.4.1"

"jest-config@^26.6.3":
  "integrity" "sha512-t5qdIj/bCj2j7NFVHb2nFB4aUdfucDn3JRKgrZnplb8nieAirAzRSHP8uDEd+qV6ygzg9Pz4YG7UTJf94LPSyg=="
  "resolved" "https://registry.npmjs.org/jest-config/-/jest-config-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/test-sequencer" "^26.6.3"
    "@jest/types" "^26.6.2"
    "babel-jest" "^26.6.3"
    "chalk" "^4.0.0"
    "deepmerge" "^4.2.2"
    "glob" "^7.1.1"
    "graceful-fs" "^4.2.4"
    "jest-environment-jsdom" "^26.6.2"
    "jest-environment-node" "^26.6.2"
    "jest-get-type" "^26.3.0"
    "jest-jasmine2" "^26.6.3"
    "jest-regex-util" "^26.0.0"
    "jest-resolve" "^26.6.2"
    "jest-util" "^26.6.2"
    "jest-validate" "^26.6.2"
    "micromatch" "^4.0.2"
    "pretty-format" "^26.6.2"

"jest-diff@^26.6.2":
  "integrity" "sha512-6m+9Z3Gv9wN0WFVasqjCL/06+EFCMTqDEUl/b87HYK2rAPTyfz4ZIuSlPhY51PIQRWx5TaxeF1qmXKe9gfN3sA=="
  "resolved" "https://registry.npmjs.org/jest-diff/-/jest-diff-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "chalk" "^4.0.0"
    "diff-sequences" "^26.6.2"
    "jest-get-type" "^26.3.0"
    "pretty-format" "^26.6.2"

"jest-docblock@^26.0.0":
  "integrity" "sha512-RDZ4Iz3QbtRWycd8bUEPxQsTlYazfYn/h5R65Fc6gOfwozFhoImx+affzky/FFBuqISPTqjXomoIGJVKBWoo0w=="
  "resolved" "https://registry.npmjs.org/jest-docblock/-/jest-docblock-26.0.0.tgz"
  "version" "26.0.0"
  dependencies:
    "detect-newline" "^3.0.0"

"jest-each@^26.6.2":
  "integrity" "sha512-Mer/f0KaATbjl8MCJ+0GEpNdqmnVmDYqCTJYTvoo7rqmRiDllmp2AYN+06F93nXcY3ur9ShIjS+CO/uD+BbH4A=="
  "resolved" "https://registry.npmjs.org/jest-each/-/jest-each-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "chalk" "^4.0.0"
    "jest-get-type" "^26.3.0"
    "jest-util" "^26.6.2"
    "pretty-format" "^26.6.2"

"jest-environment-jsdom@^26.6.2":
  "integrity" "sha512-jgPqCruTlt3Kwqg5/WVFyHIOJHsiAvhcp2qiR2QQstuG9yWox5+iHpU3ZrcBxW14T4fe5Z68jAfLRh7joCSP2Q=="
  "resolved" "https://registry.npmjs.org/jest-environment-jsdom/-/jest-environment-jsdom-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "jest-mock" "^26.6.2"
    "jest-util" "^26.6.2"
    "jsdom" "^16.4.0"

"jest-environment-node@^26.6.2":
  "integrity" "sha512-zhtMio3Exty18dy8ee8eJ9kjnRyZC1N4C1Nt/VShN1apyXc8rWGtJ9lI7vqiWcyyXS4BVSEn9lxAM2D+07/Tag=="
  "resolved" "https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "jest-mock" "^26.6.2"
    "jest-util" "^26.6.2"

"jest-get-type@^26.3.0":
  "integrity" "sha512-TpfaviN1R2pQWkIihlfEanwOXK0zcxrKEE4MlU6Tn7keoXdN6/3gK/xl0yEh8DOunn5pOVGKf8hB4R9gVh04ig=="
  "resolved" "https://registry.npmjs.org/jest-get-type/-/jest-get-type-26.3.0.tgz"
  "version" "26.3.0"

"jest-haste-map@^26.6.2":
  "integrity" "sha512-easWIJXIw71B2RdR8kgqpjQrbMRWQBgiBwXYEhtGUTaX+doCjBheluShdDMeR8IMfJiTqH4+zfhtg29apJf/8w=="
  "resolved" "https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/graceful-fs" "^4.1.2"
    "@types/node" "*"
    "anymatch" "^3.0.3"
    "fb-watchman" "^2.0.0"
    "graceful-fs" "^4.2.4"
    "jest-regex-util" "^26.0.0"
    "jest-serializer" "^26.6.2"
    "jest-util" "^26.6.2"
    "jest-worker" "^26.6.2"
    "micromatch" "^4.0.2"
    "sane" "^4.0.3"
    "walker" "^1.0.7"
  optionalDependencies:
    "fsevents" "^2.1.2"

"jest-jasmine2@^26.6.3":
  "integrity" "sha512-kPKUrQtc8aYwBV7CqBg5pu+tmYXlvFlSFYn18ev4gPFtrRzB15N2gW/Roew3187q2w2eHuu0MU9TJz6w0/nPEg=="
  "resolved" "https://registry.npmjs.org/jest-jasmine2/-/jest-jasmine2-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@babel/traverse" "^7.1.0"
    "@jest/environment" "^26.6.2"
    "@jest/source-map" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "co" "^4.6.0"
    "expect" "^26.6.2"
    "is-generator-fn" "^2.0.0"
    "jest-each" "^26.6.2"
    "jest-matcher-utils" "^26.6.2"
    "jest-message-util" "^26.6.2"
    "jest-runtime" "^26.6.3"
    "jest-snapshot" "^26.6.2"
    "jest-util" "^26.6.2"
    "pretty-format" "^26.6.2"
    "throat" "^5.0.0"

"jest-leak-detector@^26.6.2":
  "integrity" "sha512-i4xlXpsVSMeKvg2cEKdfhh0H39qlJlP5Ex1yQxwF9ubahboQYMgTtz5oML35AVA3B4Eu+YsmwaiKVev9KCvLxg=="
  "resolved" "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "jest-get-type" "^26.3.0"
    "pretty-format" "^26.6.2"

"jest-matcher-utils@^26.6.2":
  "integrity" "sha512-llnc8vQgYcNqDrqRDXWwMr9i7rS5XFiCwvh6DTP7Jqa2mqpcCBBlpCbn+trkG0KNhPu/h8rzyBkriOtBstvWhw=="
  "resolved" "https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "chalk" "^4.0.0"
    "jest-diff" "^26.6.2"
    "jest-get-type" "^26.3.0"
    "pretty-format" "^26.6.2"

"jest-message-util@^26.6.2":
  "integrity" "sha512-rGiLePzQ3AzwUshu2+Rn+UMFk0pHN58sOG+IaJbk5Jxuqo3NYO1U2/MIR4S1sKgsoYSXSzdtSa0TgrmtUwEbmA=="
  "resolved" "https://registry.npmjs.org/jest-message-util/-/jest-message-util-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@jest/types" "^26.6.2"
    "@types/stack-utils" "^2.0.0"
    "chalk" "^4.0.0"
    "graceful-fs" "^4.2.4"
    "micromatch" "^4.0.2"
    "pretty-format" "^26.6.2"
    "slash" "^3.0.0"
    "stack-utils" "^2.0.2"

"jest-mock@^26.6.2":
  "integrity" "sha512-YyFjePHHp1LzpzYcmgqkJ0nm0gg/lJx2aZFzFy1S6eUqNjXsOqTK10zNRff2dNfssgokjkG65OlWNcIlgd3zew=="
  "resolved" "https://registry.npmjs.org/jest-mock/-/jest-mock-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"

"jest-pnp-resolver@^1.2.2":
  "integrity" "sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w=="
  "resolved" "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz"
  "version" "1.2.3"

"jest-regex-util@^26.0.0":
  "integrity" "sha512-Gv3ZIs/nA48/Zvjrl34bf+oD76JHiGDUxNOVgUjh3j890sblXryjY4rss71fPtD/njchl6PSE2hIhvyWa1eT0A=="
  "resolved" "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-26.0.0.tgz"
  "version" "26.0.0"

"jest-resolve-dependencies@^26.6.3":
  "integrity" "sha512-pVwUjJkxbhe4RY8QEWzN3vns2kqyuldKpxlxJlzEYfKSvY6/bMvxoFrYYzUO1Gx28yKWN37qyV7rIoIp2h8fTg=="
  "resolved" "https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@jest/types" "^26.6.2"
    "jest-regex-util" "^26.0.0"
    "jest-snapshot" "^26.6.2"

"jest-resolve@*", "jest-resolve@^26.6.2":
  "integrity" "sha512-sOxsZOq25mT1wRsfHcbtkInS+Ek7Q8jCHUB0ZUTP0tc/c41QHriU/NunqMfCUWsL4H3MHpvQD4QR9kSYhS7UvQ=="
  "resolved" "https://registry.npmjs.org/jest-resolve/-/jest-resolve-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "chalk" "^4.0.0"
    "graceful-fs" "^4.2.4"
    "jest-pnp-resolver" "^1.2.2"
    "jest-util" "^26.6.2"
    "read-pkg-up" "^7.0.1"
    "resolve" "^1.18.1"
    "slash" "^3.0.0"

"jest-runner@^26.6.3":
  "integrity" "sha512-atgKpRHnaA2OvByG/HpGA4g6CSPS/1LK0jK3gATJAoptC1ojltpmVlYC3TYgdmGp+GLuhzpH30Gvs36szSL2JQ=="
  "resolved" "https://registry.npmjs.org/jest-runner/-/jest-runner-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/environment" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "emittery" "^0.7.1"
    "exit" "^0.1.2"
    "graceful-fs" "^4.2.4"
    "jest-config" "^26.6.3"
    "jest-docblock" "^26.0.0"
    "jest-haste-map" "^26.6.2"
    "jest-leak-detector" "^26.6.2"
    "jest-message-util" "^26.6.2"
    "jest-resolve" "^26.6.2"
    "jest-runtime" "^26.6.3"
    "jest-util" "^26.6.2"
    "jest-worker" "^26.6.2"
    "source-map-support" "^0.5.6"
    "throat" "^5.0.0"

"jest-runtime@^26.6.3":
  "integrity" "sha512-lrzyR3N8sacTAMeonbqpnSka1dHNux2uk0qqDXVkMv2c/A3wYnvQ4EXuI013Y6+gSKSCxdaczvf4HF0mVXHRdw=="
  "resolved" "https://registry.npmjs.org/jest-runtime/-/jest-runtime-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/globals" "^26.6.2"
    "@jest/source-map" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/yargs" "^15.0.0"
    "chalk" "^4.0.0"
    "cjs-module-lexer" "^0.6.0"
    "collect-v8-coverage" "^1.0.0"
    "exit" "^0.1.2"
    "glob" "^7.1.3"
    "graceful-fs" "^4.2.4"
    "jest-config" "^26.6.3"
    "jest-haste-map" "^26.6.2"
    "jest-message-util" "^26.6.2"
    "jest-mock" "^26.6.2"
    "jest-regex-util" "^26.0.0"
    "jest-resolve" "^26.6.2"
    "jest-snapshot" "^26.6.2"
    "jest-util" "^26.6.2"
    "jest-validate" "^26.6.2"
    "slash" "^3.0.0"
    "strip-bom" "^4.0.0"
    "yargs" "^15.4.1"

"jest-serializer@^26.6.2":
  "integrity" "sha512-S5wqyz0DXnNJPd/xfIzZ5Xnp1HrJWBczg8mMfMpN78OJ5eDxXyf+Ygld9wX1DnUWbIbhM1YDY95NjR4CBXkb2g=="
  "resolved" "https://registry.npmjs.org/jest-serializer/-/jest-serializer-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@types/node" "*"
    "graceful-fs" "^4.2.4"

"jest-snapshot@^26.6.2":
  "integrity" "sha512-OLhxz05EzUtsAmOMzuupt1lHYXCNib0ECyuZ/PZOx9TrZcC8vL0x+DUG3TL+GLX3yHG45e6YGjIm0XwDc3q3og=="
  "resolved" "https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@babel/types" "^7.0.0"
    "@jest/types" "^26.6.2"
    "@types/babel__traverse" "^7.0.4"
    "@types/prettier" "^2.0.0"
    "chalk" "^4.0.0"
    "expect" "^26.6.2"
    "graceful-fs" "^4.2.4"
    "jest-diff" "^26.6.2"
    "jest-get-type" "^26.3.0"
    "jest-haste-map" "^26.6.2"
    "jest-matcher-utils" "^26.6.2"
    "jest-message-util" "^26.6.2"
    "jest-resolve" "^26.6.2"
    "natural-compare" "^1.4.0"
    "pretty-format" "^26.6.2"
    "semver" "^7.3.2"

"jest-util@^26.6.2":
  "integrity" "sha512-MDW0fKfsn0OI7MS7Euz6h8HNDXVQ0gaM9uW6RjfDmd1DAFcaxX9OqIakHIqhbnmF08Cf2DLDG+ulq8YQQ0Lp0Q=="
  "resolved" "https://registry.npmjs.org/jest-util/-/jest-util-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "graceful-fs" "^4.2.4"
    "is-ci" "^2.0.0"
    "micromatch" "^4.0.2"

"jest-validate@^26.6.2":
  "integrity" "sha512-NEYZ9Aeyj0i5rQqbq+tpIOom0YS1u2MVu6+euBsvpgIme+FOfRmoC4R5p0JiAUpaFvFy24xgrpMknarR/93XjQ=="
  "resolved" "https://registry.npmjs.org/jest-validate/-/jest-validate-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "camelcase" "^6.0.0"
    "chalk" "^4.0.0"
    "jest-get-type" "^26.3.0"
    "leven" "^3.1.0"
    "pretty-format" "^26.6.2"

"jest-watcher@^26.6.2":
  "integrity" "sha512-WKJob0P/Em2csiVthsI68p6aGKTIcsfjH9Gsx1f0A3Italz43e3ho0geSAVsmj09RWOELP1AZ/DXyJgOgDKxXQ=="
  "resolved" "https://registry.npmjs.org/jest-watcher/-/jest-watcher-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "ansi-escapes" "^4.2.1"
    "chalk" "^4.0.0"
    "jest-util" "^26.6.2"
    "string-length" "^4.0.1"

"jest-worker@^26.6.2", "jest-worker@26.6.2":
  "integrity" "sha512-KWYVV1c4i+jbMpaBC+U++4Va0cp8OisU185o73T1vo99hqi7w8tSJfUXYswwqqrjzwxa6KpRK54WhPvwf5w6PQ=="
  "resolved" "https://registry.npmjs.org/jest-worker/-/jest-worker-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@types/node" "*"
    "merge-stream" "^2.0.0"
    "supports-color" "^7.0.0"

"jest-worker@24.9.0":
  "integrity" "sha512-51PE4haMSXcHohnSMdM42anbvZANYTqMrr52tVKPqqsPJMzoP6FYYDVqahX/HrAoKEKz3uUPzSvKs9A3qR4iVw=="
  "resolved" "https://registry.npmjs.org/jest-worker/-/jest-worker-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "merge-stream" "^2.0.0"
    "supports-color" "^6.1.0"

"jest@^26.6.3":
  "integrity" "sha512-lGS5PXGAzR4RF7V5+XObhqz2KZIDUA1yD0DG6pBVmy10eh0ZIXQImRuzocsI/N2XZ1GrLFwTS27In2i2jlpq1Q=="
  "resolved" "https://registry.npmjs.org/jest/-/jest-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@jest/core" "^26.6.3"
    "import-local" "^3.0.2"
    "jest-cli" "^26.6.3"

"js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^4.0.0":
  "integrity" "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="
  "resolved" "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@^3.13.1":
  "integrity" "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g=="
  "resolved" "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz"
  "version" "3.14.1"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"jsdom@^16.4.0":
  "integrity" "sha512-u9Smc2G1USStM+s/x1ru5Sxrl6mPYCbByG1U/hUmqaVsm4tbNyS7CicOSRyuGQYZhTu0h84qkZZQ/I+dzizSVw=="
  "resolved" "https://registry.npmjs.org/jsdom/-/jsdom-16.7.0.tgz"
  "version" "16.7.0"
  dependencies:
    "abab" "^2.0.5"
    "acorn" "^8.2.4"
    "acorn-globals" "^6.0.0"
    "cssom" "^0.4.4"
    "cssstyle" "^2.3.0"
    "data-urls" "^2.0.0"
    "decimal.js" "^10.2.1"
    "domexception" "^2.0.1"
    "escodegen" "^2.0.0"
    "form-data" "^3.0.0"
    "html-encoding-sniffer" "^2.0.1"
    "http-proxy-agent" "^4.0.1"
    "https-proxy-agent" "^5.0.0"
    "is-potential-custom-element-name" "^1.0.1"
    "nwsapi" "^2.2.0"
    "parse5" "6.0.1"
    "saxes" "^5.0.1"
    "symbol-tree" "^3.2.4"
    "tough-cookie" "^4.0.0"
    "w3c-hr-time" "^1.0.2"
    "w3c-xmlserializer" "^2.0.0"
    "webidl-conversions" "^6.1.0"
    "whatwg-encoding" "^1.0.5"
    "whatwg-mimetype" "^2.3.0"
    "whatwg-url" "^8.5.0"
    "ws" "^7.4.6"
    "xml-name-validator" "^3.0.0"

"jsesc@^2.5.1":
  "integrity" "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA=="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz"
  "version" "2.5.2"

"json-parse-better-errors@^1.0.1":
  "integrity" "sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw=="
  "resolved" "https://registry.npmjs.org/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz"
  "version" "1.0.2"

"json-parse-even-better-errors@^2.3.0":
  "integrity" "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w=="
  "resolved" "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  "version" "2.3.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json2mq@^0.2.0":
  "integrity" "sha512-SzoRg7ux5DWTII9J2qkrZrqV1gt+rTaoufMxEzXbS26Uid0NwaJd123HcoB80TgubEppxxIGdNxCx50fEoEWQA=="
  "resolved" "https://registry.npmjs.org/json2mq/-/json2mq-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "string-convert" "^0.2.0"

"json5@^1.0.1":
  "integrity" "sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA=="
  "resolved" "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "minimist" "^1.2.0"

"json5@^2.2.1":
  "integrity" "sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg=="
  "resolved" "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz"
  "version" "2.2.3"

"kind-of@^3.0.2":
  "integrity" "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ=="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.0.3":
  "integrity" "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ=="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.2.0":
  "integrity" "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ=="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^4.0.0":
  "integrity" "sha512-24XsCxmEbRwEDbz/qz3stgin8TTzZ1ESR56OMCN0ujYg+vRutNSiOj9bHH9u85DKgXguraugV5sFuvbD4FW/hw=="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^5.0.0":
  "integrity" "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz"
  "version" "5.1.0"

"kind-of@^6.0.0", "kind-of@^6.0.2":
  "integrity" "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw=="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"kleur@^3.0.3":
  "integrity" "sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w=="
  "resolved" "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz"
  "version" "3.0.3"

"leven@^3.1.0":
  "integrity" "sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A=="
  "resolved" "https://registry.npmjs.org/leven/-/leven-3.1.0.tgz"
  "version" "3.1.0"

"lines-and-columns@^1.1.6":
  "integrity" "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg=="
  "resolved" "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  "version" "1.2.4"

"lint-staged@^10.0.7":
  "integrity" "sha512-EechC3DdFic/TdOPgj/RB3FicqE6932LTHCUm0Y2fsD9KGlLB+RwJl2q1IYBIvEsKzDOgn0D4gll+YxG5RsrKg=="
  "resolved" "https://registry.npmjs.org/lint-staged/-/lint-staged-10.5.4.tgz"
  "version" "10.5.4"
  dependencies:
    "chalk" "^4.1.0"
    "cli-truncate" "^2.1.0"
    "commander" "^6.2.0"
    "cosmiconfig" "^7.0.0"
    "debug" "^4.2.0"
    "dedent" "^0.7.0"
    "enquirer" "^2.3.6"
    "execa" "^4.1.0"
    "listr2" "^3.2.2"
    "log-symbols" "^4.0.0"
    "micromatch" "^4.0.2"
    "normalize-path" "^3.0.0"
    "please-upgrade-node" "^3.2.0"
    "string-argv" "0.3.1"
    "stringify-object" "^3.3.0"

"listr2@^3.2.2":
  "integrity" "sha512-TyWI8G99GX9GjE54cJ+RrNMcIFBfwMPxc3XTFiAYGN4s10hWROGtOg7+O6u6LE3mNkyld7RSLE6nrKBvTfcs3g=="
  "resolved" "https://registry.npmjs.org/listr2/-/listr2-3.14.0.tgz"
  "version" "3.14.0"
  dependencies:
    "cli-truncate" "^2.1.0"
    "colorette" "^2.0.16"
    "log-update" "^4.0.0"
    "p-map" "^4.0.0"
    "rfdc" "^1.3.0"
    "rxjs" "^7.5.1"
    "through" "^2.3.8"
    "wrap-ansi" "^7.0.0"

"loader-utils@^1.1.0":
  "integrity" "sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg=="
  "resolved" "https://registry.npmjs.org/loader-utils/-/loader-utils-1.4.2.tgz"
  "version" "1.4.2"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^1.0.1"

"locate-path@^5.0.0":
  "integrity" "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g=="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"lodash-es@^4.2.1":
  "integrity" "sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw=="
  "resolved" "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz"
  "version" "4.17.21"

"lodash.debounce@^4.0.8":
  "integrity" "sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow=="
  "resolved" "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  "version" "4.0.8"

"lodash.isequal@^4.5.0":
  "integrity" "sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ=="
  "resolved" "https://registry.npmjs.org/lodash.isequal/-/lodash.isequal-4.5.0.tgz"
  "version" "4.5.0"

"lodash.merge@^4.6.2":
  "integrity" "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ=="
  "resolved" "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lodash.throttle@^4.1.1":
  "integrity" "sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ=="
  "resolved" "https://registry.npmjs.org/lodash.throttle/-/lodash.throttle-4.1.1.tgz"
  "version" "4.1.1"

"lodash.tonumber@^4.0.3":
  "integrity" "sha512-SY0SwuPOHRwKcCNTdsntPYb+Zddz5mDUIVFABzRMqmAiL41pMeyoQFGxYAw5zdc9NnH4pbJqiqqp5ckfxa+zSA=="
  "resolved" "https://registry.npmjs.org/lodash.tonumber/-/lodash.tonumber-4.0.3.tgz"
  "version" "4.0.3"

"lodash@^4.0.1", "lodash@^4.17.15", "lodash@^4.17.21", "lodash@^4.2.1", "lodash@^4.7.0":
  "integrity" "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="
  "resolved" "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  "version" "4.17.21"

"log-symbols@^4.0.0":
  "integrity" "sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg=="
  "resolved" "https://registry.npmjs.org/log-symbols/-/log-symbols-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "chalk" "^4.1.0"
    "is-unicode-supported" "^0.1.0"

"log-update@^4.0.0":
  "integrity" "sha512-9fkkDevMefjg0mmzWFBW8YkFP91OrizzkW3diF7CpG+S2EYdy4+TVfGwz1zeF8x7hCx1ovSPTOE9Ngib74qqUg=="
  "resolved" "https://registry.npmjs.org/log-update/-/log-update-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-escapes" "^4.3.0"
    "cli-cursor" "^3.1.0"
    "slice-ansi" "^4.0.0"
    "wrap-ansi" "^6.2.0"

"loose-envify@^1.0.0", "loose-envify@^1.1.0", "loose-envify@^1.2.0", "loose-envify@^1.3.1", "loose-envify@^1.4.0":
  "integrity" "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q=="
  "resolved" "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"lru-cache@^4.0.1":
  "integrity" "sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@^5.1.1":
  "integrity" "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "yallist" "^3.0.2"

"lru-cache@^6.0.0":
  "integrity" "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "yallist" "^4.0.0"

"make-dir@^4.0.0":
  "integrity" "sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw=="
  "resolved" "https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "semver" "^7.5.3"

"makeerror@1.0.12":
  "integrity" "sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg=="
  "resolved" "https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz"
  "version" "1.0.12"
  dependencies:
    "tmpl" "1.0.5"

"map-cache@^0.2.2":
  "integrity" "sha512-8y/eV9QQZCiyn1SprXSrCmqJN0yNRATe+PO8ztwqrvrbdRLA3eYJF0yaR0YayLWkMbsQSKWS9N2gPcGEc4UsZg=="
  "resolved" "https://registry.npmjs.org/map-cache/-/map-cache-0.2.2.tgz"
  "version" "0.2.2"

"map-visit@^1.0.0":
  "integrity" "sha512-4y7uGv8bd2WdM9vpQsiQNo41Ln1NvhvDRuVt0k2JZQ+ezN2uaQes7lZeZ+QQUHOLQAtDaBJ+7wCbi+ab/KFs+w=="
  "resolved" "https://registry.npmjs.org/map-visit/-/map-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "object-visit" "^1.0.0"

"md5.js@^1.3.4":
  "integrity" "sha512-xitP+WxNPcTTOgnTJcrhM0xvdPepipPSf3I8EIpGKeFLjt3PlJLIDG3u8EX53ZIubkb+5U2+3rELYpEhHhzdkg=="
  "resolved" "https://registry.npmjs.org/md5.js/-/md5.js-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"memoize-one@^5.1.1":
  "integrity" "sha512-zYiwtZUcYyXKo/np96AGZAckk+FWWsUdJ3cHGGmld7+AhvcWmQyGCYUh1hc4Q/pkOhb65dQR/pqCyK0cOaHz4Q=="
  "resolved" "https://registry.npmjs.org/memoize-one/-/memoize-one-5.2.1.tgz"
  "version" "5.2.1"

"merge-stream@^2.0.0":
  "integrity" "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w=="
  "resolved" "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
  "version" "2.0.0"

"micromatch@^3.1.4":
  "integrity" "sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg=="
  "resolved" "https://registry.npmjs.org/micromatch/-/micromatch-3.1.10.tgz"
  "version" "3.1.10"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.3.1"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "extglob" "^2.0.4"
    "fragment-cache" "^0.2.1"
    "kind-of" "^6.0.2"
    "nanomatch" "^1.2.9"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.2"

"micromatch@^4.0.2":
  "integrity" "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA=="
  "resolved" "https://registry.npmjs.org/micromatch/-/micromatch-4.0.5.tgz"
  "version" "4.0.5"
  dependencies:
    "braces" "^3.0.2"
    "picomatch" "^2.3.1"

"miller-rabin@^4.0.0":
  "integrity" "sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA=="
  "resolved" "https://registry.npmjs.org/miller-rabin/-/miller-rabin-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "bn.js" "^4.0.0"
    "brorand" "^1.0.1"

"mime-db@1.52.0":
  "integrity" "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="
  "resolved" "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  "version" "1.52.0"

"mime-types@^2.1.12":
  "integrity" "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw=="
  "resolved" "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  "version" "2.1.35"
  dependencies:
    "mime-db" "1.52.0"

"mime@1.4.1":
  "integrity" "sha512-KI1+qOZu5DcW6wayYHSzR/tXKCDC5Om4s1z2QJjDULzLcmf3DvzS7oluY4HCTrc+9FiKmWUgeNLg7W3uIQvxtQ=="
  "resolved" "https://registry.npmjs.org/mime/-/mime-1.4.1.tgz"
  "version" "1.4.1"

"mimic-fn@^2.1.0":
  "integrity" "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg=="
  "resolved" "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
  "version" "2.1.0"

"min-document@^2.19.0":
  "integrity" "sha512-9Wy1B3m3f66bPPmU5hdA4DR4PB2OfDU/+GS3yAB7IQozE3tqXaVv2zOjgla7MEGSRv95+ILmOuvhLkOK6wJtCQ=="
  "resolved" "https://registry.npmjs.org/min-document/-/min-document-2.19.0.tgz"
  "version" "2.19.0"
  dependencies:
    "dom-walk" "^0.1.0"

"mini-create-react-context@^0.4.0":
  "integrity" "sha512-YWCYEmd5CQeHGSAKrYvXgmzzkrvssZcuuQDDeqkT+PziKGMgE+0MCCtcKbROzocGBG1meBLl2FotlRwf4gAzbQ=="
  "resolved" "https://registry.npmjs.org/mini-create-react-context/-/mini-create-react-context-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "@babel/runtime" "^7.12.1"
    "tiny-warning" "^1.0.3"

"minimalistic-assert@^1.0.0", "minimalistic-assert@^1.0.1":
  "integrity" "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A=="
  "resolved" "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz"
  "version" "1.0.1"

"minimalistic-crypto-utils@^1.0.1":
  "integrity" "sha512-JIYlbt6g8i5jKfJ3xz7rF0LXmv2TkDxBLUkiBeZ7bAx4GnnNMr8xFpGnOxn6GhTEHx3SjRrZEoU+j04prX1ktg=="
  "resolved" "https://registry.npmjs.org/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.0.4", "minimatch@^3.1.1":
  "integrity" "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw=="
  "resolved" "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimist@^1.1.1", "minimist@^1.2.0":
  "integrity" "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA=="
  "resolved" "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz"
  "version" "1.2.8"

"mixin-deep@^1.2.0":
  "integrity" "sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA=="
  "resolved" "https://registry.npmjs.org/mixin-deep/-/mixin-deep-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "for-in" "^1.0.2"
    "is-extendable" "^1.0.1"

"mobx-react-lite@^3.4.0":
  "integrity" "sha512-NkJREyFTSUXR772Qaai51BnE1voWx56LOL80xG7qkZr6vo8vEaLF3sz1JNUVh+rxmUzxYaqOhfuxTfqUh0FXUg=="
  "resolved" "https://registry.npmjs.org/mobx-react-lite/-/mobx-react-lite-3.4.3.tgz"
  "version" "3.4.3"

"mobx-react@^7.5.0":
  "integrity" "sha512-+HQUNuh7AoQ9ZnU6c4rvbiVVl+wEkb9WqYsVDzGLng+Dqj1XntHu79PvEWKtSMoMj67vFp/ZPXcElosuJO8ckA=="
  "resolved" "https://registry.npmjs.org/mobx-react/-/mobx-react-7.6.0.tgz"
  "version" "7.6.0"
  dependencies:
    "mobx-react-lite" "^3.4.0"

"mobx@^6.1.0", "mobx@^6.6.0":
  "integrity" "sha512-/HTWzW2s8J1Gqt+WmUj5Y0mddZk+LInejADc79NJadrWla3rHzmRHki/mnEUH1AvOmbNTZ1BRbKxr8DSgfdjMA=="
  "resolved" "https://registry.npmjs.org/mobx/-/mobx-6.13.5.tgz"
  "version" "6.13.5"

"moment@^2.24.0", "moment@^2.27.0", "moment@^2.29.1", "moment@^2.29.2", "moment@^2.29.4", "moment@>= 2.x":
  "integrity" "sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how=="
  "resolved" "https://registry.npmmirror.com/moment/-/moment-2.30.1.tgz"
  "version" "2.30.1"

"ms@2.0.0":
  "integrity" "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
  "version" "2.0.0"

"ms@2.1.2":
  "integrity" "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
  "version" "2.1.2"

"nanomatch@^1.2.9":
  "integrity" "sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA=="
  "resolved" "https://registry.npmjs.org/nanomatch/-/nanomatch-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "fragment-cache" "^0.2.1"
    "is-windows" "^1.0.2"
    "kind-of" "^6.0.2"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"natural-compare@^1.4.0":
  "integrity" "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw=="
  "resolved" "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"nice-try@^1.0.4":
  "integrity" "sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ=="
  "resolved" "https://registry.npmjs.org/nice-try/-/nice-try-1.0.5.tgz"
  "version" "1.0.5"

"node-fetch@^1.0.1":
  "integrity" "sha512-NhZ4CsKx7cYm2vSrBAr2PvFOe6sWDf0UYLRqA6svUYg7+/TSfVAu49jYC4BvQ4Sms9SZgdqGBgroqfDhJdTyKQ=="
  "resolved" "https://registry.npmjs.org/node-fetch/-/node-fetch-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "encoding" "^0.1.11"
    "is-stream" "^1.0.1"

"node-int64@^0.4.0":
  "integrity" "sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw=="
  "resolved" "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz"
  "version" "0.4.0"

"node-libs-browser@2.2.1":
  "integrity" "sha512-h/zcD8H9kaDZ9ALUWwlBUDo6TKF8a7qBSCSEGfjTVIYeqsioSKaAX+BN7NgiMGp6iSIXZ3PxgCu8KS3b71YK5Q=="
  "resolved" "https://registry.npmjs.org/node-libs-browser/-/node-libs-browser-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "assert" "^1.1.1"
    "browserify-zlib" "^0.2.0"
    "buffer" "^4.3.0"
    "console-browserify" "^1.1.0"
    "constants-browserify" "^1.0.0"
    "crypto-browserify" "^3.11.0"
    "domain-browser" "^1.1.1"
    "events" "^3.0.0"
    "https-browserify" "^1.0.0"
    "os-browserify" "^0.3.0"
    "path-browserify" "0.0.1"
    "process" "^0.11.10"
    "punycode" "^1.2.4"
    "querystring-es3" "^0.2.0"
    "readable-stream" "^2.3.3"
    "stream-browserify" "^2.0.1"
    "stream-http" "^2.7.2"
    "string_decoder" "^1.0.0"
    "timers-browserify" "^2.0.4"
    "tty-browserify" "0.0.0"
    "url" "^0.11.0"
    "util" "^0.11.0"
    "vm-browserify" "^1.0.1"

"node-notifier@^8.0.0":
  "integrity" "sha512-oJP/9NAdd9+x2Q+rfphB2RJCHjod70RcRLjosiPMMu5gjIfwVnOUGq2nbTjTUbmy0DJ/tFIVT30+Qe3nzl4TJg=="
  "resolved" "https://registry.npmjs.org/node-notifier/-/node-notifier-8.0.2.tgz"
  "version" "8.0.2"
  dependencies:
    "growly" "^1.3.0"
    "is-wsl" "^2.2.0"
    "semver" "^7.3.2"
    "shellwords" "^0.1.1"
    "uuid" "^8.3.0"
    "which" "^2.0.2"

"node-releases@^2.0.12":
  "integrity" "sha512-uYr7J37ae/ORWdZeQ1xxMJe3NtdmqMC/JZK+geofDrkLUApKRHPd18/TxtBOJ4A0/+uUIliorNrfYV6s1b02eQ=="
  "resolved" "https://registry.npmjs.org/node-releases/-/node-releases-2.0.13.tgz"
  "version" "2.0.13"

"normalize-package-data@^2.5.0":
  "integrity" "sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA=="
  "resolved" "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "hosted-git-info" "^2.1.4"
    "resolve" "^1.10.0"
    "semver" "2 || 3 || 4 || 5"
    "validate-npm-package-license" "^3.0.1"

"normalize-path@^1.0.0":
  "integrity" "sha512-7WyT0w8jhpDStXRq5836AMmihQwq2nrUVQrgjvUo/p/NZf9uy/MeJ246lBJVmWuYXMlJuG9BNZHF0hWjfTbQUA=="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-1.0.0.tgz"
  "version" "1.0.0"

"normalize-path@^2.1.1":
  "integrity" "sha512-3pKJwH184Xo/lnH6oyP1q2pMd7HcypqqmRs91/6/i2CGtWwIKGCkOOMTm/zXbgTEWHw1uNpNi/igc3ePOYHb6w=="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "remove-trailing-separator" "^1.0.1"

"normalize-path@^3.0.0", "normalize-path@~3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-range@^0.1.2":
  "integrity" "sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA=="
  "resolved" "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz"
  "version" "0.1.2"

"normalize-url@1.9.1":
  "integrity" "sha512-A48My/mtCklowHBlI8Fq2jFWK4tX4lJ5E6ytFsSOq1fzpvT0SQSgKhSg7lN5c2uYFOrUAOQp6zhhJnpp1eMloQ=="
  "resolved" "https://registry.npmjs.org/normalize-url/-/normalize-url-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "object-assign" "^4.0.1"
    "prepend-http" "^1.0.0"
    "query-string" "^4.1.0"
    "sort-keys" "^1.0.0"

"normalize.css@^7.0.0":
  "integrity" "sha512-LYaFZxj2Q1Q9e1VJ0f6laG46Rt5s9URhKyckNaA2vZnL/0gwQHWhM7ALQkp3WBQKM5sXRLQ5Ehrfkp+E/ZiCRg=="
  "resolved" "https://registry.npmjs.org/normalize.css/-/normalize.css-7.0.0.tgz"
  "version" "7.0.0"

"npm-run-path@^2.0.0":
  "integrity" "sha512-lJxZYlT4DW/bRUtFh1MQIWqmLwQfAxnqWG4HhEdjMlkrJYnJn0Jrr2u3mgxqaWsdiBc76TYkTG/mhrnYTuzfHw=="
  "resolved" "https://registry.npmjs.org/npm-run-path/-/npm-run-path-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "path-key" "^2.0.0"

"npm-run-path@^4.0.0":
  "integrity" "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw=="
  "resolved" "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "path-key" "^3.0.0"

"num2fraction@^1.2.2":
  "integrity" "sha512-Y1wZESM7VUThYY+4W+X4ySH2maqcA+p7UR+w8VWNWVAd6lwuXXWz/w/Cz43J/dI2I+PS6wD5N+bJUF+gjWvIqg=="
  "resolved" "https://registry.npmjs.org/num2fraction/-/num2fraction-1.2.2.tgz"
  "version" "1.2.2"

"nwsapi@^2.2.0":
  "integrity" "sha512-ub5E4+FBPKwAZx0UwIQOjYWGHTEq5sPqHQNRN8Z9e4A7u3Tj1weLJsL59yH9vmvqEtBHaOmT6cYQKIZOxp35FQ=="
  "resolved" "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.7.tgz"
  "version" "2.2.7"

"object-assign@^4.0.1", "object-assign@^4.1.0", "object-assign@^4.1.1", "object-assign@4.x":
  "integrity" "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg=="
  "resolved" "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-copy@^0.1.0":
  "integrity" "sha512-79LYn6VAb63zgtmAteVOWo9Vdj71ZVBy3Pbse+VqxDpEP83XuujMrGqHIwAXJ5I/aM0zU7dIyIAhifVTPrNItQ=="
  "resolved" "https://registry.npmjs.org/object-copy/-/object-copy-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "copy-descriptor" "^0.1.0"
    "define-property" "^0.2.5"
    "kind-of" "^3.0.3"

"object-inspect@^1.13.1":
  "integrity" "sha512-IRZSRuzJiynemAXPYtPe5BoI/RESNYR7TYm50MC5Mqbd3Jmw5y790sErYw3V6SryFJD64b74qQQs9wn5Bg/k3g=="
  "resolved" "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.13.2.tgz"
  "version" "1.13.2"

"object-visit@^1.0.0":
  "integrity" "sha512-GBaMwwAVK9qbQN3Scdo0OyvgPW7l3lnaVMj84uTOZlswkX0KpF6fyDBJhtTthf7pymztoN36/KEr1DyhF96zEA=="
  "resolved" "https://registry.npmjs.org/object-visit/-/object-visit-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "isobject" "^3.0.0"

"object.pick@^1.3.0":
  "integrity" "sha512-tqa/UMy/CCoYmj+H5qc07qvSL9dqcs/WZENZ1JbtWBlATP+iVOe778gE6MSijnyCnORzDuX6hU+LA4SZ09YjFQ=="
  "resolved" "https://registry.npmjs.org/object.pick/-/object.pick-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "isobject" "^3.0.1"

"omit.js@^2.0.2":
  "integrity" "sha512-hJmu9D+bNB40YpL9jYebQl4lsTW6yEHRTroJzNLqQJYHm7c+NQnJGfZmIWh8S3q3KoaxV1aLhV6B3+0N0/kyJg=="
  "resolved" "https://registry.npmjs.org/omit.js/-/omit.js-2.0.2.tgz"
  "version" "2.0.2"

"once@^1.3.0", "once@^1.3.1", "once@^1.4.0":
  "integrity" "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w=="
  "resolved" "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^5.1.0":
  "integrity" "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg=="
  "resolved" "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "mimic-fn" "^2.1.0"

"os-browserify@^0.3.0":
  "integrity" "sha512-gjcpUc3clBf9+210TRaDWbf+rZZZEshZ+DlXMRCeAjp0xhTrnQsKHypIy1J3d5hKdUzj69t708EHtU8P6bUn0A=="
  "resolved" "https://registry.npmjs.org/os-browserify/-/os-browserify-0.3.0.tgz"
  "version" "0.3.0"

"p-each-series@^2.1.0":
  "integrity" "sha512-ycIL2+1V32th+8scbpTvyHNaHe02z0sjgh91XXjAk+ZeXoPN4Z46DVUnzdso0aX4KckKw0FNNFHdjZ2UsZvxiA=="
  "resolved" "https://registry.npmjs.org/p-each-series/-/p-each-series-2.2.0.tgz"
  "version" "2.2.0"

"p-finally@^1.0.0":
  "integrity" "sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow=="
  "resolved" "https://registry.npmjs.org/p-finally/-/p-finally-1.0.0.tgz"
  "version" "1.0.0"

"p-limit@^2.2.0":
  "integrity" "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-locate@^4.1.0":
  "integrity" "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A=="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-map@^4.0.0":
  "integrity" "sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ=="
  "resolved" "https://registry.npmjs.org/p-map/-/p-map-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "aggregate-error" "^3.0.0"

"p-try@^2.0.0":
  "integrity" "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ=="
  "resolved" "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz"
  "version" "2.2.0"

"pako@~1.0.5":
  "integrity" "sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw=="
  "resolved" "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz"
  "version" "1.0.11"

"parent-module@^1.0.0":
  "integrity" "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g=="
  "resolved" "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parse-asn1@^5.0.0", "parse-asn1@^5.1.5":
  "integrity" "sha512-RnZRo1EPU6JBnra2vGHj0yhp6ebyjBZpmUCLHWiFhxlzvBCCpAuZ7elsBp1PVAbQN0/04VD/19rfzlBSwLstMw=="
  "resolved" "https://registry.npmjs.org/parse-asn1/-/parse-asn1-5.1.6.tgz"
  "version" "5.1.6"
  dependencies:
    "asn1.js" "^5.2.0"
    "browserify-aes" "^1.0.0"
    "evp_bytestokey" "^1.0.0"
    "pbkdf2" "^3.0.3"
    "safe-buffer" "^5.1.1"

"parse-json@^4.0.0":
  "integrity" "sha512-aOIos8bujGN93/8Ox/jPLh7RwVnPEysynVFE+fQZyg6jKELEHwzgKdLRFHUgXJL6kylijVSBC4BvN9OmsB48Rw=="
  "resolved" "https://registry.npmjs.org/parse-json/-/parse-json-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "error-ex" "^1.3.1"
    "json-parse-better-errors" "^1.0.1"

"parse-json@^5.0.0":
  "integrity" "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg=="
  "resolved" "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "error-ex" "^1.3.1"
    "json-parse-even-better-errors" "^2.3.0"
    "lines-and-columns" "^1.1.6"

"parse5@6.0.1":
  "integrity" "sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw=="
  "resolved" "https://registry.npmjs.org/parse5/-/parse5-6.0.1.tgz"
  "version" "6.0.1"

"pascalcase@^0.1.1":
  "integrity" "sha512-XHXfu/yOQRy9vYOtUDVMN60OEJjW013GoObG1o+xwQTpB9eYJX/BjXMsdW13ZDPruFhYYn0AG22w0xgQMwl3Nw=="
  "resolved" "https://registry.npmjs.org/pascalcase/-/pascalcase-0.1.1.tgz"
  "version" "0.1.1"

"path-browserify@0.0.1":
  "integrity" "sha512-BapA40NHICOS+USX9SN4tyhq+A2RrN/Ws5F0Z5aMHDp98Fl86lX8Oti8B7uN93L4Ifv4fHOEA+pQw87gmMO/lQ=="
  "resolved" "https://registry.npmjs.org/path-browserify/-/path-browserify-0.0.1.tgz"
  "version" "0.0.1"

"path-exists@^4.0.0":
  "integrity" "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="
  "resolved" "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0":
  "integrity" "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg=="
  "resolved" "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-key@^2.0.0", "path-key@^2.0.1":
  "integrity" "sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw=="
  "resolved" "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz"
  "version" "2.0.1"

"path-key@^3.0.0", "path-key@^3.1.0":
  "integrity" "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="
  "resolved" "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-parse@^1.0.7":
  "integrity" "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="
  "resolved" "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  "version" "1.0.7"

"path-to-regexp@^1.7.0", "path-to-regexp@1.x":
  "integrity" "sha512-n43JRhlUKUAlibEJhPeir1ncUID16QnEjNpwzNdO3Lm4ywrBpBZ5oLD0I6br9evr1Y9JTqwRtAh7JLoOzAQdVA=="
  "resolved" "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "isarray" "0.0.1"

"path-to-regexp@2.4.0":
  "integrity" "sha512-G6zHoVqC6GGTQkZwF4lkuEyMbVOjoBKAEybQUypI1WTkqinCOrq2x6U2+phkJ1XsEMTy4LjtwPI7HW+NVrRR2w=="
  "resolved" "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-2.4.0.tgz"
  "version" "2.4.0"

"path-type@^4.0.0":
  "integrity" "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw=="
  "resolved" "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz"
  "version" "4.0.0"

"pbkdf2@^3.0.3":
  "integrity" "sha512-iuh7L6jA7JEGu2WxDwtQP1ddOpaJNC4KlDEFfdQajSGgGPNi4OyDc2R7QnbY2bR9QjBVGwgvTdNJZoE7RaxUMA=="
  "resolved" "https://registry.npmjs.org/pbkdf2/-/pbkdf2-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "create-hash" "^1.1.2"
    "create-hmac" "^1.1.4"
    "ripemd160" "^2.0.1"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"performance-now@^2.1.0":
  "integrity" "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow=="
  "resolved" "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz"
  "version" "2.1.0"

"picocolors@^0.2.1":
  "integrity" "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA=="
  "resolved" "https://registry.npmjs.org/picocolors/-/picocolors-0.2.1.tgz"
  "version" "0.2.1"

"picocolors@^1.0.0":
  "integrity" "sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ=="
  "resolved" "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz"
  "version" "1.0.0"

"picomatch@^2.0.4", "picomatch@^2.2.1", "picomatch@^2.3.1":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"pirates@^4.0.1":
  "integrity" "sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg=="
  "resolved" "https://registry.npmjs.org/pirates/-/pirates-4.0.6.tgz"
  "version" "4.0.6"

"pkg-dir@^4.2.0":
  "integrity" "sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ=="
  "resolved" "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "find-up" "^4.0.0"

"please-upgrade-node@^3.2.0":
  "integrity" "sha512-gQR3WpIgNIKwBMVLkpMUeR3e1/E1y42bqDQZfql+kDeXd8COYfM8PQA4X6y7a8u9Ua9FHmsrrmirW2vHs45hWg=="
  "resolved" "https://registry.npmjs.org/please-upgrade-node/-/please-upgrade-node-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "semver-compare" "^1.0.0"

"posix-character-classes@^0.1.0":
  "integrity" "sha512-xTgYBc3fuo7Yt7JbiuFxSYGToMoz8fLoE6TC9Wx1P/u+LfeThMOAqmuyECnlBaaJb+u1m9hHiXUEtwW4OzfUJg=="
  "resolved" "https://registry.npmjs.org/posix-character-classes/-/posix-character-classes-0.1.1.tgz"
  "version" "0.1.1"

"postcss-attribute-case-insensitive@^4.0.1":
  "integrity" "sha512-clkFxk/9pcdb4Vkn0hAHq3YnxBQ2p0CGD1dy24jN+reBck+EWxMbxSUqN4Yj7t0w8csl87K6p0gxBe1utkJsYA=="
  "resolved" "https://registry.npmjs.org/postcss-attribute-case-insensitive/-/postcss-attribute-case-insensitive-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.2"
    "postcss-selector-parser" "^6.0.2"

"postcss-color-functional-notation@^2.0.1":
  "integrity" "sha512-ZBARCypjEDofW4P6IdPVTLhDNXPRn8T2s1zHbZidW6rPaaZvcnCS2soYFIQJrMZSxiePJ2XIYTlcb2ztr/eT2g=="
  "resolved" "https://registry.npmjs.org/postcss-color-functional-notation/-/postcss-color-functional-notation-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "postcss" "^7.0.2"
    "postcss-values-parser" "^2.0.0"

"postcss-color-gray@^5.0.0":
  "integrity" "sha512-q6BuRnAGKM/ZRpfDascZlIZPjvwsRye7UDNalqVz3s7GDxMtqPY6+Q871liNxsonUw8oC61OG+PSaysYpl1bnw=="
  "resolved" "https://registry.npmjs.org/postcss-color-gray/-/postcss-color-gray-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "@csstools/convert-colors" "^1.4.0"
    "postcss" "^7.0.5"
    "postcss-values-parser" "^2.0.0"

"postcss-color-hex-alpha@^5.0.3":
  "integrity" "sha512-PF4GDel8q3kkreVXKLAGNpHKilXsZ6xuu+mOQMHWHLPNyjiUBOr75sp5ZKJfmv1MCus5/DWUGcK9hm6qHEnXYw=="
  "resolved" "https://registry.npmjs.org/postcss-color-hex-alpha/-/postcss-color-hex-alpha-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "postcss" "^7.0.14"
    "postcss-values-parser" "^2.0.1"

"postcss-color-mod-function@^3.0.3":
  "integrity" "sha512-YP4VG+xufxaVtzV6ZmhEtc+/aTXH3d0JLpnYfxqTvwZPbJhWqp8bSY3nfNzNRFLgB4XSaBA82OE4VjOOKpCdVQ=="
  "resolved" "https://registry.npmjs.org/postcss-color-mod-function/-/postcss-color-mod-function-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "@csstools/convert-colors" "^1.4.0"
    "postcss" "^7.0.2"
    "postcss-values-parser" "^2.0.0"

"postcss-color-rebeccapurple@^4.0.1":
  "integrity" "sha512-aAe3OhkS6qJXBbqzvZth2Au4V3KieR5sRQ4ptb2b2O8wgvB3SJBsdG+jsn2BZbbwekDG8nTfcCNKcSfe/lEy8g=="
  "resolved" "https://registry.npmjs.org/postcss-color-rebeccapurple/-/postcss-color-rebeccapurple-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.2"
    "postcss-values-parser" "^2.0.0"

"postcss-custom-media@^7.0.8":
  "integrity" "sha512-c9s5iX0Ge15o00HKbuRuTqNndsJUbaXdiNsksnVH8H4gdc+zbLzr/UasOwNG6CTDpLFekVY4672eWdiiWu2GUg=="
  "resolved" "https://registry.npmjs.org/postcss-custom-media/-/postcss-custom-media-7.0.8.tgz"
  "version" "7.0.8"
  dependencies:
    "postcss" "^7.0.14"

"postcss-custom-properties@^8.0.11":
  "integrity" "sha512-nm+o0eLdYqdnJ5abAJeXp4CEU1c1k+eB2yMCvhgzsds/e0umabFrN6HoTy/8Q4K5ilxERdl/JD1LO5ANoYBeMA=="
  "resolved" "https://registry.npmjs.org/postcss-custom-properties/-/postcss-custom-properties-8.0.11.tgz"
  "version" "8.0.11"
  dependencies:
    "postcss" "^7.0.17"
    "postcss-values-parser" "^2.0.1"

"postcss-custom-selectors@^5.1.2":
  "integrity" "sha512-DSGDhqinCqXqlS4R7KGxL1OSycd1lydugJ1ky4iRXPHdBRiozyMHrdu0H3o7qNOCiZwySZTUI5MV0T8QhCLu+w=="
  "resolved" "https://registry.npmjs.org/postcss-custom-selectors/-/postcss-custom-selectors-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "postcss" "^7.0.2"
    "postcss-selector-parser" "^5.0.0-rc.3"

"postcss-dir-pseudo-class@^5.0.0":
  "integrity" "sha512-3pm4oq8HYWMZePJY+5ANriPs3P07q+LW6FAdTlkFH2XqDdP4HeeJYMOzn0HYLhRSjBO3fhiqSwwU9xEULSrPgw=="
  "resolved" "https://registry.npmjs.org/postcss-dir-pseudo-class/-/postcss-dir-pseudo-class-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "postcss" "^7.0.2"
    "postcss-selector-parser" "^5.0.0-rc.3"

"postcss-double-position-gradients@^1.0.0":
  "integrity" "sha512-G+nV8EnQq25fOI8CH/B6krEohGWnF5+3A6H/+JEpOncu5dCnkS1QQ6+ct3Jkaepw1NGVqqOZH6lqrm244mCftA=="
  "resolved" "https://registry.npmjs.org/postcss-double-position-gradients/-/postcss-double-position-gradients-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "postcss" "^7.0.5"
    "postcss-values-parser" "^2.0.0"

"postcss-env-function@^2.0.2":
  "integrity" "sha512-rwac4BuZlITeUbiBq60h/xbLzXY43qOsIErngWa4l7Mt+RaSkT7QBjXVGTcBHupykkblHMDrBFh30zchYPaOUw=="
  "resolved" "https://registry.npmjs.org/postcss-env-function/-/postcss-env-function-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "postcss" "^7.0.2"
    "postcss-values-parser" "^2.0.0"

"postcss-flexbugs-fixes@4.2.1":
  "integrity" "sha512-9SiofaZ9CWpQWxOwRh1b/r85KD5y7GgvsNt1056k6OYLvWUun0czCvogfJgylC22uJTwW1KzY3Gz65NZRlvoiQ=="
  "resolved" "https://registry.npmjs.org/postcss-flexbugs-fixes/-/postcss-flexbugs-fixes-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "postcss" "^7.0.26"

"postcss-focus-visible@^4.0.0":
  "integrity" "sha512-Z5CkWBw0+idJHSV6+Bgf2peDOFf/x4o+vX/pwcNYrWpXFrSfTkQ3JQ1ojrq9yS+upnAlNRHeg8uEwFTgorjI8g=="
  "resolved" "https://registry.npmjs.org/postcss-focus-visible/-/postcss-focus-visible-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "postcss" "^7.0.2"

"postcss-focus-within@^3.0.0":
  "integrity" "sha512-W0APui8jQeBKbCGZudW37EeMCjDeVxKgiYfIIEo8Bdh5SpB9sxds/Iq8SEuzS0Q4YFOlG7EPFulbbxujpkrV2w=="
  "resolved" "https://registry.npmjs.org/postcss-focus-within/-/postcss-focus-within-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "postcss" "^7.0.2"

"postcss-font-variant@^4.0.0":
  "integrity" "sha512-I3ADQSTNtLTTd8uxZhtSOrTCQ9G4qUVKPjHiDk0bV75QSxXjVWiJVJ2VLdspGUi9fbW9BcjKJoRvxAH1pckqmA=="
  "resolved" "https://registry.npmjs.org/postcss-font-variant/-/postcss-font-variant-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.2"

"postcss-gap-properties@^2.0.0":
  "integrity" "sha512-QZSqDaMgXCHuHTEzMsS2KfVDOq7ZFiknSpkrPJY6jmxbugUPTuSzs/vuE5I3zv0WAS+3vhrlqhijiprnuQfzmg=="
  "resolved" "https://registry.npmjs.org/postcss-gap-properties/-/postcss-gap-properties-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "postcss" "^7.0.2"

"postcss-image-set-function@^3.0.1":
  "integrity" "sha512-oPTcFFip5LZy8Y/whto91L9xdRHCWEMs3e1MdJxhgt4jy2WYXfhkng59fH5qLXSCPN8k4n94p1Czrfe5IOkKUw=="
  "resolved" "https://registry.npmjs.org/postcss-image-set-function/-/postcss-image-set-function-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "postcss" "^7.0.2"
    "postcss-values-parser" "^2.0.0"

"postcss-initial@^3.0.0":
  "integrity" "sha512-3RLn6DIpMsK1l5UUy9jxQvoDeUN4gP939tDcKUHD/kM8SGSKbFAnvkpFpj3Bhtz3HGk1jWY5ZNWX6mPta5M9fg=="
  "resolved" "https://registry.npmjs.org/postcss-initial/-/postcss-initial-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "postcss" "^7.0.2"

"postcss-lab-function@^2.0.1":
  "integrity" "sha512-whLy1IeZKY+3fYdqQFuDBf8Auw+qFuVnChWjmxm/UhHWqNHZx+B99EwxTvGYmUBqe3Fjxs4L1BoZTJmPu6usVg=="
  "resolved" "https://registry.npmjs.org/postcss-lab-function/-/postcss-lab-function-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "@csstools/convert-colors" "^1.4.0"
    "postcss" "^7.0.2"
    "postcss-values-parser" "^2.0.0"

"postcss-load-config@^2.0.0":
  "integrity" "sha512-/rDeGV6vMUo3mwJZmeHfEDvwnTKKqQ0S7OHUi/kJvvtx3aWtyWG2/0ZWnzCt2keEclwN6Tf0DST2v9kITdOKYw=="
  "resolved" "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "cosmiconfig" "^5.0.0"
    "import-cwd" "^2.0.0"

"postcss-loader@3.0.0":
  "integrity" "sha512-cLWoDEY5OwHcAjDnkyRQzAXfs2jrKjXpO/HQFcc5b5u/r7aa471wdmChmwfnv7x2u840iat/wi0lQ5nbRgSkUA=="
  "resolved" "https://registry.npmjs.org/postcss-loader/-/postcss-loader-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "loader-utils" "^1.1.0"
    "postcss" "^7.0.0"
    "postcss-load-config" "^2.0.0"
    "schema-utils" "^1.0.0"

"postcss-logical@^3.0.0":
  "integrity" "sha512-1SUKdJc2vuMOmeItqGuNaC+N8MzBWFWEkAnRnLpFYj1tGGa7NqyVBujfRtgNa2gXR+6RkGUiB2O5Vmh7E2RmiA=="
  "resolved" "https://registry.npmjs.org/postcss-logical/-/postcss-logical-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "postcss" "^7.0.2"

"postcss-media-minmax@^4.0.0":
  "integrity" "sha512-fo9moya6qyxsjbFAYl97qKO9gyre3qvbMnkOZeZwlsW6XYFsvs2DMGDlchVLfAd8LHPZDxivu/+qW2SMQeTHBw=="
  "resolved" "https://registry.npmjs.org/postcss-media-minmax/-/postcss-media-minmax-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "postcss" "^7.0.2"

"postcss-nesting@^7.0.0":
  "integrity" "sha512-FrorPb0H3nuVq0Sff7W2rnc3SmIcruVC6YwpcS+k687VxyxO33iE1amna7wHuRVzM8vfiYofXSBHNAZ3QhLvYg=="
  "resolved" "https://registry.npmjs.org/postcss-nesting/-/postcss-nesting-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "postcss" "^7.0.2"

"postcss-overflow-shorthand@^2.0.0":
  "integrity" "sha512-aK0fHc9CBNx8jbzMYhshZcEv8LtYnBIRYQD5i7w/K/wS9c2+0NSR6B3OVMu5y0hBHYLcMGjfU+dmWYNKH0I85g=="
  "resolved" "https://registry.npmjs.org/postcss-overflow-shorthand/-/postcss-overflow-shorthand-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "postcss" "^7.0.2"

"postcss-page-break@^2.0.0":
  "integrity" "sha512-tkpTSrLpfLfD9HvgOlJuigLuk39wVTbbd8RKcy8/ugV2bNBUW3xU+AIqyxhDrQr1VUj1RmyJrBn1YWrqUm9zAQ=="
  "resolved" "https://registry.npmjs.org/postcss-page-break/-/postcss-page-break-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "postcss" "^7.0.2"

"postcss-place@^4.0.1":
  "integrity" "sha512-Zb6byCSLkgRKLODj/5mQugyuj9bvAAw9LqJJjgwz5cYryGeXfFZfSXoP1UfveccFmeq0b/2xxwcTEVScnqGxBg=="
  "resolved" "https://registry.npmjs.org/postcss-place/-/postcss-place-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.2"
    "postcss-values-parser" "^2.0.0"

"postcss-preset-env@6.7.0":
  "integrity" "sha512-eU4/K5xzSFwUFJ8hTdTQzo2RBLbDVt83QZrAvI07TULOkmyQlnYlpwep+2yIK+K+0KlZO4BvFcleOCCcUtwchg=="
  "resolved" "https://registry.npmjs.org/postcss-preset-env/-/postcss-preset-env-6.7.0.tgz"
  "version" "6.7.0"
  dependencies:
    "autoprefixer" "^9.6.1"
    "browserslist" "^4.6.4"
    "caniuse-lite" "^1.0.30000981"
    "css-blank-pseudo" "^0.1.4"
    "css-has-pseudo" "^0.10.0"
    "css-prefers-color-scheme" "^3.1.1"
    "cssdb" "^4.4.0"
    "postcss" "^7.0.17"
    "postcss-attribute-case-insensitive" "^4.0.1"
    "postcss-color-functional-notation" "^2.0.1"
    "postcss-color-gray" "^5.0.0"
    "postcss-color-hex-alpha" "^5.0.3"
    "postcss-color-mod-function" "^3.0.3"
    "postcss-color-rebeccapurple" "^4.0.1"
    "postcss-custom-media" "^7.0.8"
    "postcss-custom-properties" "^8.0.11"
    "postcss-custom-selectors" "^5.1.2"
    "postcss-dir-pseudo-class" "^5.0.0"
    "postcss-double-position-gradients" "^1.0.0"
    "postcss-env-function" "^2.0.2"
    "postcss-focus-visible" "^4.0.0"
    "postcss-focus-within" "^3.0.0"
    "postcss-font-variant" "^4.0.0"
    "postcss-gap-properties" "^2.0.0"
    "postcss-image-set-function" "^3.0.1"
    "postcss-initial" "^3.0.0"
    "postcss-lab-function" "^2.0.1"
    "postcss-logical" "^3.0.0"
    "postcss-media-minmax" "^4.0.0"
    "postcss-nesting" "^7.0.0"
    "postcss-overflow-shorthand" "^2.0.0"
    "postcss-page-break" "^2.0.0"
    "postcss-place" "^4.0.1"
    "postcss-pseudo-class-any-link" "^6.0.0"
    "postcss-replace-overflow-wrap" "^3.0.0"
    "postcss-selector-matches" "^4.0.0"
    "postcss-selector-not" "^4.0.0"

"postcss-pseudo-class-any-link@^6.0.0":
  "integrity" "sha512-lgXW9sYJdLqtmw23otOzrtbDXofUdfYzNm4PIpNE322/swES3VU9XlXHeJS46zT2onFO7V1QFdD4Q9LiZj8mew=="
  "resolved" "https://registry.npmjs.org/postcss-pseudo-class-any-link/-/postcss-pseudo-class-any-link-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "postcss" "^7.0.2"
    "postcss-selector-parser" "^5.0.0-rc.3"

"postcss-replace-overflow-wrap@^3.0.0":
  "integrity" "sha512-2T5hcEHArDT6X9+9dVSPQdo7QHzG4XKclFT8rU5TzJPDN7RIRTbO9c4drUISOVemLj03aezStHCR2AIcr8XLpw=="
  "resolved" "https://registry.npmjs.org/postcss-replace-overflow-wrap/-/postcss-replace-overflow-wrap-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "postcss" "^7.0.2"

"postcss-safe-parser@4.0.2":
  "integrity" "sha512-Uw6ekxSWNLCPesSv/cmqf2bY/77z11O7jZGPax3ycZMFU/oi2DMH9i89AdHc1tRwFg/arFoEwX0IS3LCUxJh1g=="
  "resolved" "https://registry.npmjs.org/postcss-safe-parser/-/postcss-safe-parser-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.26"

"postcss-selector-matches@^4.0.0":
  "integrity" "sha512-LgsHwQR/EsRYSqlwdGzeaPKVT0Ml7LAT6E75T8W8xLJY62CE4S/l03BWIt3jT8Taq22kXP08s2SfTSzaraoPww=="
  "resolved" "https://registry.npmjs.org/postcss-selector-matches/-/postcss-selector-matches-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "balanced-match" "^1.0.0"
    "postcss" "^7.0.2"

"postcss-selector-not@^4.0.0":
  "integrity" "sha512-YolvBgInEK5/79C+bdFMyzqTg6pkYqDbzZIST/PDMqa/o3qtXenD05apBG2jLgT0/BQ77d4U2UK12jWpilqMAQ=="
  "resolved" "https://registry.npmjs.org/postcss-selector-not/-/postcss-selector-not-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "balanced-match" "^1.0.0"
    "postcss" "^7.0.2"

"postcss-selector-parser@^5.0.0-rc.3", "postcss-selector-parser@^5.0.0-rc.4":
  "integrity" "sha512-w+zLE5Jhg6Liz8+rQOWEAwtwkyqpfnmsinXjXg6cY7YIONZZtgvE0v2O0uhQBs0peNomOJwWRKt6JBfTdTd3OQ=="
  "resolved" "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "cssesc" "^2.0.0"
    "indexes-of" "^1.0.1"
    "uniq" "^1.0.1"

"postcss-selector-parser@^6.0.2":
  "integrity" "sha512-EaV1Gl4mUEV4ddhDnv/xtj7sxwrwxdetHdWUGnT4VJQf+4d05v6lHYZr8N573k5Z0BViss7BDhfWtKS3+sfAqQ=="
  "resolved" "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.13.tgz"
  "version" "6.0.13"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss-value-parser@^4.1.0":
  "integrity" "sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ=="
  "resolved" "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  "version" "4.2.0"

"postcss-values-parser@^2.0.0", "postcss-values-parser@^2.0.1":
  "integrity" "sha512-2tLuBsA6P4rYTNKCXYG/71C7j1pU6pK503suYOmn4xYrQIzW+opD+7FAFNuGSdZC/3Qfy334QbeMu7MEb8gOxg=="
  "resolved" "https://registry.npmjs.org/postcss-values-parser/-/postcss-values-parser-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "flatten" "^1.0.2"
    "indexes-of" "^1.0.1"
    "uniq" "^1.0.1"

"postcss@^7.0.0", "postcss@^7.0.14", "postcss@^7.0.17", "postcss@^7.0.2", "postcss@^7.0.26", "postcss@^7.0.32", "postcss@^7.0.5", "postcss@^7.0.6", "postcss@7.0.32":
  "integrity" "sha512-03eXong5NLnNCD05xscnGKGDZ98CyzoqPSMjOe6SuoQY7Z2hIj0Ld1g/O/UQRuOle2aRtiIRDg9tDcTGAkLfKw=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-7.0.32.tgz"
  "version" "7.0.32"
  dependencies:
    "chalk" "^2.4.2"
    "source-map" "^0.6.1"
    "supports-color" "^6.1.0"

"prepend-http@^1.0.0":
  "integrity" "sha512-PhmXi5XmoyKw1Un4E+opM2KcsJInDvKyuOumcjjw3waw86ZNjHwVUOOWLc4bCzLdcKNaWBH9e99sbWzDQsVaYg=="
  "resolved" "https://registry.npmjs.org/prepend-http/-/prepend-http-1.0.4.tgz"
  "version" "1.0.4"

"prettier@^2.2.0":
  "integrity" "sha512-vIS4Rlc2FNh0BySk3Wkd6xmwxB0FpOndW5fisM5H8hsZSxU2VWVB5CWIkIjWvrHjIhxk2g3bfMKM87zNTrZddw=="
  "resolved" "https://registry.npmjs.org/prettier/-/prettier-2.8.4.tgz"
  "version" "2.8.4"

"prettier@2.2.1":
  "integrity" "sha512-PqyhM2yCjg/oKkFPtTGUojv7gnZAoG80ttl45O6x2Ug/rMJw4wcc9k6aaf2hibP7BGVCCM33gZoGjyvt9mm16Q=="
  "resolved" "https://registry.npmjs.org/prettier/-/prettier-2.2.1.tgz"
  "version" "2.2.1"

"pretty-format@^26.6.2":
  "integrity" "sha512-7AeGuCYNGmycyQbCqd/3PWH4eOoX/OiCa0uphp57NVTeAGdJGaAliecxwBDHYQCIvrW7aDBZCYeNTP/WX69mkg=="
  "resolved" "https://registry.npmjs.org/pretty-format/-/pretty-format-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "ansi-regex" "^5.0.0"
    "ansi-styles" "^4.0.0"
    "react-is" "^17.0.1"

"process-nextick-args@~2.0.0":
  "integrity" "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="
  "resolved" "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"process@^0.11.10":
  "integrity" "sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A=="
  "resolved" "https://registry.npmjs.org/process/-/process-0.11.10.tgz"
  "version" "0.11.10"

"promise@^7.1.1":
  "integrity" "sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg=="
  "resolved" "https://registry.npmjs.org/promise/-/promise-7.3.1.tgz"
  "version" "7.3.1"
  dependencies:
    "asap" "~2.0.3"

"prompts@^2.0.1":
  "integrity" "sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q=="
  "resolved" "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "kleur" "^3.0.3"
    "sisteransi" "^1.0.5"

"prop-types@^15.0.0", "prop-types@^15.5.10", "prop-types@^15.5.4", "prop-types@^15.5.6", "prop-types@^15.5.7", "prop-types@^15.5.8", "prop-types@^15.6.0", "prop-types@^15.6.1", "prop-types@^15.6.2", "prop-types@^15.7.2", "prop-types@15.x":
  "integrity" "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg=="
  "resolved" "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz"
  "version" "15.8.1"
  dependencies:
    "loose-envify" "^1.4.0"
    "object-assign" "^4.1.1"
    "react-is" "^16.13.1"

"proxy-from-env@^1.1.0":
  "integrity" "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="
  "resolved" "https://registry.npmmirror.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  "version" "1.1.0"

"pseudomap@^1.0.2":
  "integrity" "sha512-b/YwNhb8lk1Zz2+bXXpS/LK9OisiZZ1SNsSLxN1x2OXVEhW2Ckr/7mWE5vrC1ZTiJlD9g19jWszTmJsB+oEpFQ=="
  "resolved" "https://registry.npmjs.org/pseudomap/-/pseudomap-1.0.2.tgz"
  "version" "1.0.2"

"psl@^1.1.33":
  "integrity" "sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag=="
  "resolved" "https://registry.npmjs.org/psl/-/psl-1.9.0.tgz"
  "version" "1.9.0"

"public-encrypt@^4.0.0":
  "integrity" "sha512-zVpa8oKZSz5bTMTFClc1fQOnyyEzpl5ozpi1B5YcvBrdohMjH2rfsBtyXcuNuwjsDIXmBYlF2N5FlJYhR29t8Q=="
  "resolved" "https://registry.npmjs.org/public-encrypt/-/public-encrypt-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "browserify-rsa" "^4.0.0"
    "create-hash" "^1.1.0"
    "parse-asn1" "^5.0.0"
    "randombytes" "^2.0.1"
    "safe-buffer" "^5.1.2"

"pump@^3.0.0":
  "integrity" "sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww=="
  "resolved" "https://registry.npmjs.org/pump/-/pump-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"punycode@^1.2.4":
  "integrity" "sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ=="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz"
  "version" "1.4.1"

"punycode@^1.4.1":
  "integrity" "sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ=="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz"
  "version" "1.4.1"

"punycode@^2.1.0", "punycode@^2.1.1":
  "integrity" "sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA=="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-2.3.0.tgz"
  "version" "2.3.0"

"qrcode.react@^3.1.0":
  "integrity" "sha512-oyF+Urr3oAMUG/OiOuONL3HXM+53wvuH3mtIWQrYmsXoAq0DkvZp2RYUWFSMFtbdOpuS++9v+WAkzNVkMlNW6Q=="
  "resolved" "https://registry.npmjs.org/qrcode.react/-/qrcode.react-3.1.0.tgz"
  "version" "3.1.0"

"qs@^6.11.0", "qs@^6.11.2", "qs@^6.9.1":
  "integrity" "sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg=="
  "resolved" "https://registry.npmmirror.com/qs/-/qs-6.13.0.tgz"
  "version" "6.13.0"
  dependencies:
    "side-channel" "^1.0.6"

"query-string@^4.1.0":
  "integrity" "sha512-O2XLNDBIg1DnTOa+2XrIwSiXEV8h2KImXUnjhhn2+UsvZ+Es2uyd5CCRTNQlDGbzUQOW3aYCBx9rVA6dzsiY7Q=="
  "resolved" "https://registry.npmjs.org/query-string/-/query-string-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "object-assign" "^4.1.0"
    "strict-uri-encode" "^1.0.0"

"query-string@^6.11.0":
  "integrity" "sha512-XDxAeVmpfu1/6IjyT/gXHOl+S0vQ9owggJ30hhWKdHAsNPOcasn5o9BW0eejZqL2e4vMjhAxoW3jVHcD6mbcYw=="
  "resolved" "https://registry.npmjs.org/query-string/-/query-string-6.14.1.tgz"
  "version" "6.14.1"
  dependencies:
    "decode-uri-component" "^0.2.0"
    "filter-obj" "^1.1.0"
    "split-on-first" "^1.0.0"
    "strict-uri-encode" "^2.0.0"

"querystring-es3@^0.2.0":
  "integrity" "sha512-773xhDQnZBMFobEiztv8LIl70ch5MSF/jUQVlhwFyBILqq96anmoctVIYz+ZRp0qbCKATTn6ev02M3r7Ga5vqA=="
  "resolved" "https://registry.npmjs.org/querystring-es3/-/querystring-es3-0.2.1.tgz"
  "version" "0.2.1"

"querystringify@^2.1.1":
  "integrity" "sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ=="
  "resolved" "https://registry.npmjs.org/querystringify/-/querystringify-2.2.0.tgz"
  "version" "2.2.0"

"raf@^3.1.0", "raf@^3.3.2", "raf@^3.4.0":
  "integrity" "sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA=="
  "resolved" "https://registry.npmjs.org/raf/-/raf-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "performance-now" "^2.1.0"

"randombytes@^2.0.0", "randombytes@^2.0.1", "randombytes@^2.0.5":
  "integrity" "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ=="
  "resolved" "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "safe-buffer" "^5.1.0"

"randomfill@^1.0.3":
  "integrity" "sha512-87lcbR8+MhcWcUiQ+9e+Rwx8MyR2P7qnt15ynUlbm3TU/fjbgz4GsvfSUDTemtCCtVCqb4ZcEFlyPNTh9bBTLw=="
  "resolved" "https://registry.npmjs.org/randomfill/-/randomfill-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "randombytes" "^2.0.5"
    "safe-buffer" "^5.1.0"

"rc-align@^2.4.0":
  "integrity" "sha512-nv9wYUYdfyfK+qskThf4BQUSIadeI/dCsfaMZfNEoxm9HwOIioQ+LyqmMK6jWHAZQgOzMLaqawhuBXlF63vgjw=="
  "resolved" "https://registry.npmjs.org/rc-align/-/rc-align-2.4.5.tgz"
  "version" "2.4.5"
  dependencies:
    "babel-runtime" "^6.26.0"
    "dom-align" "^1.7.0"
    "prop-types" "^15.5.8"
    "rc-util" "^4.0.4"

"rc-align@^4.0.0":
  "integrity" "sha512-wqJtVH60pka/nOX7/IspElA8gjPNQKIx/ZqJ6heATCkXpe1Zg4cPVrMD2vC96wjsFFL8WsmhPbx9tdMo1qqlIA=="
  "resolved" "https://registry.npmjs.org/rc-align/-/rc-align-4.0.15.tgz"
  "version" "4.0.15"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "dom-align" "^1.7.0"
    "rc-util" "^5.26.0"
    "resize-observer-polyfill" "^1.5.1"

"rc-animate@^2.4.4", "rc-animate@2.x":
  "integrity" "sha512-1NyuCGFJG/0Y+9RKh5y/i/AalUCA51opyyS/jO2seELpgymZm2u9QV3xwODwEuzkmeQ1BDPxMLmYLcTJedPlkQ=="
  "resolved" "https://registry.npmjs.org/rc-animate/-/rc-animate-2.11.1.tgz"
  "version" "2.11.1"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "^2.2.6"
    "css-animation" "^1.3.2"
    "prop-types" "15.x"
    "raf" "^3.4.0"
    "rc-util" "^4.15.3"
    "react-lifecycles-compat" "^3.0.4"

"rc-cascader@~3.7.0":
  "integrity" "sha512-KBpT+kzhxDW+hxPiNk4zaKa99+Lie2/8nnI11XF+FIOPl4Bj9VlFZi61GrnWzhLGA7VEN+dTxAkNOjkySDa0dA=="
  "resolved" "https://registry.npmjs.org/rc-cascader/-/rc-cascader-3.7.3.tgz"
  "version" "3.7.3"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "array-tree-filter" "^2.1.0"
    "classnames" "^2.3.1"
    "rc-select" "~14.1.0"
    "rc-tree" "~5.7.0"
    "rc-util" "^5.6.1"

"rc-cascader@~3.7.3":
  "integrity" "sha512-KBpT+kzhxDW+hxPiNk4zaKa99+Lie2/8nnI11XF+FIOPl4Bj9VlFZi61GrnWzhLGA7VEN+dTxAkNOjkySDa0dA=="
  "resolved" "https://registry.npmjs.org/rc-cascader/-/rc-cascader-3.7.3.tgz"
  "version" "3.7.3"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "array-tree-filter" "^2.1.0"
    "classnames" "^2.3.1"
    "rc-select" "~14.1.0"
    "rc-tree" "~5.7.0"
    "rc-util" "^5.6.1"

"rc-cascader@~3.8.0":
  "integrity" "sha512-zCz/NzsNRQ1TIfiR3rQNxjeRvgRHEkNdo0FjHQZ6Ay6n4tdCmMrM7+81ThNaf21JLQ1gS2AUG2t5uikGV78obA=="
  "resolved" "https://registry.npmjs.org/rc-cascader/-/rc-cascader-3.8.0.tgz"
  "version" "3.8.0"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "array-tree-filter" "^2.1.0"
    "classnames" "^2.3.1"
    "rc-select" "~14.2.0"
    "rc-tree" "~5.7.0"
    "rc-util" "^5.6.1"

"rc-checkbox@~2.0.0":
  "integrity" "sha512-sSDV5AcxK5CxBTyUNj9pb0zfhdgLLsWKHwJG18ikeGoIwklcxXvIF6cI/KGVbPLFDa8mPS5WLOlLRqbq/1/ouw=="
  "resolved" "https://registry.npmjs.org/rc-checkbox/-/rc-checkbox-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "babel-runtime" "^6.23.0"
    "classnames" "2.x"
    "prop-types" "15.x"
    "rc-util" "^4.0.4"

"rc-checkbox@~2.3.0":
  "integrity" "sha512-afVi1FYiGv1U0JlpNH/UaEXdh6WUJjcWokj/nUN2TgG80bfG+MDdbfHKlLcNNba94mbjy2/SXJ1HDgrOkXGAjg=="
  "resolved" "https://registry.npmjs.org/rc-checkbox/-/rc-checkbox-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.1"

"rc-checkbox@~3.0.0", "rc-checkbox@~3.0.1":
  "integrity" "sha512-k7nxDWxYF+jDI0ZcCvuvj71xONmWRVe5+1MKcERRR9MRyP3tZ69b+yUCSXXh+sik4/Hc9P5wHr2nnUoGS2zBjA=="
  "resolved" "https://registry.npmjs.org/rc-checkbox/-/rc-checkbox-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.3.2"
    "rc-util" "^5.25.2"

"rc-collapse@~1.9.1":
  "integrity" "sha512-8cG+FzudmgFCC9zRGKXJZA36zoI9Dmyjp6UDi8N80sXUch0JOpsZDxgcFzw4HPpPpK/dARtTilEe9zyuspnW0w=="
  "resolved" "https://registry.npmjs.org/rc-collapse/-/rc-collapse-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "classnames" "2.x"
    "css-animation" "1.x"
    "prop-types" "^15.5.6"
    "rc-animate" "2.x"

"rc-collapse@~3.4.2":
  "integrity" "sha512-jpTwLgJzkhAgp2Wpi3xmbTbbYExg6fkptL67Uu5LCRVEj6wqmy0DHTjjeynsjOLsppHGHu41t1ELntZ0lEvS/Q=="
  "resolved" "https://registry.npmjs.org/rc-collapse/-/rc-collapse-3.4.2.tgz"
  "version" "3.4.2"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-motion" "^2.3.4"
    "rc-util" "^5.2.1"
    "shallowequal" "^1.1.0"

"rc-collapse@~3.5.2":
  "integrity" "sha512-/TNiT3DW1t3sUCiVD/DPUYooJZ3BLA93/2rZsB3eM2bGJCCla2X9D2E4tgm7LGMQGy5Atb2lMUn2FQuvQNvavQ=="
  "resolved" "https://registry.npmjs.org/rc-collapse/-/rc-collapse-3.5.2.tgz"
  "version" "3.5.2"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-motion" "^2.3.4"
    "rc-util" "^5.27.0"

"rc-dialog@~9.0.0", "rc-dialog@~9.0.2":
  "integrity" "sha512-s3U+24xWUuB6Bn2Lk/Qt6rufy+uT+QvWkiFhNBcO9APLxcFFczWamaq7x9h8SCuhfc1nHcW4y8NbMsnAjNnWyg=="
  "resolved" "https://registry.npmjs.org/rc-dialog/-/rc-dialog-9.0.2.tgz"
  "version" "9.0.2"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/portal" "^1.0.0-8"
    "classnames" "^2.2.6"
    "rc-motion" "^2.3.0"
    "rc-util" "^5.21.0"

"rc-drawer@~6.1.1":
  "integrity" "sha512-EBRFM9o3lPU5kYh8sFoXYA9KxpdT765HDqj/AbZWicXkhwEYUH7MjUH0ctenPCiHBxXQUgIUvK14+6rPuURd6w=="
  "resolved" "https://registry.npmjs.org/rc-drawer/-/rc-drawer-6.1.6.tgz"
  "version" "6.1.6"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/portal" "^1.0.0-6"
    "classnames" "^2.2.6"
    "rc-motion" "^2.6.1"
    "rc-util" "^5.21.2"

"rc-drawer@~6.3.0":
  "integrity" "sha512-uBZVb3xTAR+dBV53d/bUhTctCw3pwcwJoM7g5aX+7vgwt2zzVzoJ6aqFjYJpBlZ9zp0dVYN8fV+hykFE7c4lig=="
  "resolved" "https://registry.npmjs.org/rc-drawer/-/rc-drawer-6.3.0.tgz"
  "version" "6.3.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/portal" "^1.1.1"
    "classnames" "^2.2.6"
    "rc-motion" "^2.6.1"
    "rc-util" "^5.21.2"

"rc-dropdown@~4.0.0", "rc-dropdown@~4.0.1":
  "integrity" "sha512-OdpXuOcme1rm45cR0Jzgfl1otzmU4vuBVb+etXM8vcaULGokAKVpKlw8p6xzspG7jGd/XxShvq+N3VNEfk/l5g=="
  "resolved" "https://registry.npmjs.org/rc-dropdown/-/rc-dropdown-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "@babel/runtime" "^7.18.3"
    "classnames" "^2.2.6"
    "rc-trigger" "^5.3.1"
    "rc-util" "^5.17.0"

"rc-field-form@^1.22.0", "rc-field-form@~1.34.0":
  "integrity" "sha512-BdciU5C7dBO51/9ZKcMvK2f8zaaO12Lt1eBhlAo8nNv+6htlNcgY9DAkUlZ7gfyWjnCc1Oo4hHIXau1m6tLw1A=="
  "resolved" "https://registry.npmjs.org/rc-field-form/-/rc-field-form-1.34.2.tgz"
  "version" "1.34.2"
  dependencies:
    "@babel/runtime" "^7.18.0"
    "async-validator" "^4.1.0"
    "rc-util" "^5.32.2"

"rc-field-form@~1.27.0":
  "integrity" "sha512-PQColQnZimGKArnOh8V2907+VzDCXcqtFvHgevDLtqWc/P7YASb/FqntSmdS8q3VND5SHX3Y1vgMIzY22/f/0Q=="
  "resolved" "https://registry.npmjs.org/rc-field-form/-/rc-field-form-1.27.4.tgz"
  "version" "1.27.4"
  dependencies:
    "@babel/runtime" "^7.18.0"
    "async-validator" "^4.1.0"
    "rc-util" "^5.8.0"

"rc-field-form@~1.38.2":
  "integrity" "sha512-O83Oi1qPyEv31Sg+Jwvsj6pXc8uQI2BtIAkURr5lvEYHVggXJhdU/nynK8wY1gbw0qR48k731sN5ON4egRCROA=="
  "resolved" "https://registry.npmjs.org/rc-field-form/-/rc-field-form-1.38.2.tgz"
  "version" "1.38.2"
  dependencies:
    "@babel/runtime" "^7.18.0"
    "async-validator" "^4.1.0"
    "rc-util" "^5.32.2"

"rc-gesture@~0.0.18", "rc-gesture@~0.0.22":
  "integrity" "sha512-6G6qrCE0MUTXyjh/powj91XkjRjoFL4HiJLPU5lALXHvGX+/efcUjGYUrHrrw0mwQdmrmg4POqnY/bibns+G3g=="
  "resolved" "https://registry.npmjs.org/rc-gesture/-/rc-gesture-0.0.22.tgz"
  "version" "0.0.22"
  dependencies:
    "babel-runtime" "6.x"

"rc-image@~5.13.0":
  "integrity" "sha512-iZTOmw5eWo2+gcrJMMcnd7SsxVHl3w5xlyCgsULUdJhJbnuI8i/AL0tVOsE7aLn9VfOh1qgDT3mC2G75/c7mqg=="
  "resolved" "https://registry.npmjs.org/rc-image/-/rc-image-5.13.0.tgz"
  "version" "5.13.0"
  dependencies:
    "@babel/runtime" "^7.11.2"
    "@rc-component/portal" "^1.0.2"
    "classnames" "^2.2.6"
    "rc-dialog" "~9.0.0"
    "rc-motion" "^2.6.2"
    "rc-util" "^5.0.6"

"rc-input-number@~7.3.11":
  "integrity" "sha512-aMWPEjFeles6PQnMqP5eWpxzsvHm9rh1jQOWXExUEIxhX62Fyl/ptifLHOn17+waDG1T/YUb6flfJbvwRhHrbA=="
  "resolved" "https://registry.npmjs.org/rc-input-number/-/rc-input-number-7.3.11.tgz"
  "version" "7.3.11"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.5"
    "rc-util" "^5.23.0"

"rc-input-number@~7.3.9":
  "integrity" "sha512-aMWPEjFeles6PQnMqP5eWpxzsvHm9rh1jQOWXExUEIxhX62Fyl/ptifLHOn17+waDG1T/YUb6flfJbvwRhHrbA=="
  "resolved" "https://registry.npmjs.org/rc-input-number/-/rc-input-number-7.3.11.tgz"
  "version" "7.3.11"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.5"
    "rc-util" "^5.23.0"

"rc-input-number@~7.4.0":
  "integrity" "sha512-r/Oub/sPYbzqLNUOHnnc9sbCu78a81KX+RCbRwmpvB4W6nptUySbdWS5KHV4Hak5CAE1LAd+wWm5JjvZizG1FA=="
  "resolved" "https://registry.npmjs.org/rc-input-number/-/rc-input-number-7.4.0.tgz"
  "version" "7.4.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/mini-decimal" "^1.0.1"
    "classnames" "^2.2.5"
    "rc-util" "^5.23.0"

"rc-input@^0.2.1", "rc-input@~0.2.1":
  "integrity" "sha512-xgkVcFgtRO0Hl9hmvslZhObNyxbSpTmy3nR1Tk4XrjjZ9lFJ7GcJBy6ss30Pdb0oX36cHzLN8I7VCjBGeRNB9A=="
  "resolved" "https://registry.npmjs.org/rc-input/-/rc-input-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "classnames" "^2.2.1"
    "rc-util" "^5.18.1"

"rc-input@^0.2.2":
  "integrity" "sha512-xgkVcFgtRO0Hl9hmvslZhObNyxbSpTmy3nR1Tk4XrjjZ9lFJ7GcJBy6ss30Pdb0oX36cHzLN8I7VCjBGeRNB9A=="
  "resolved" "https://registry.npmjs.org/rc-input/-/rc-input-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "classnames" "^2.2.1"
    "rc-util" "^5.18.1"

"rc-input@~0.1.4":
  "integrity" "sha512-FqDdNz+fV2dKNgfXzcSLKvC+jEs1709t7nD+WdfjrdSaOcefpgc7BUJYadc3usaING+b7ediMTfKxuJBsEFbXA=="
  "resolved" "https://registry.npmjs.org/rc-input/-/rc-input-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "classnames" "^2.2.1"
    "rc-util" "^5.18.1"

"rc-input@~1.1.0":
  "integrity" "sha512-izuNXPABQPh4KD7ANFcTrIGp9EZU0FkjTw6AvwCQ/rGPrdDsUTHLsp/Wju/kzGMLJFJWKNF3smbmXRNO23DtXA=="
  "resolved" "https://registry.npmjs.org/rc-input/-/rc-input-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "classnames" "^2.2.1"
    "rc-util" "^5.18.1"

"rc-mentions@~1.13.1":
  "integrity" "sha512-FCkaWw6JQygtOz0+Vxz/M/NWqrWHB9LwqlY2RtcuFqWJNFK9njijOOzTSsBGANliGufVUzx/xuPHmZPBV0+Hgw=="
  "resolved" "https://registry.npmjs.org/rc-mentions/-/rc-mentions-1.13.1.tgz"
  "version" "1.13.1"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.6"
    "rc-menu" "~9.8.0"
    "rc-textarea" "^0.4.0"
    "rc-trigger" "^5.0.4"
    "rc-util" "^5.22.5"

"rc-mentions@~2.0.0":
  "integrity" "sha512-58NSeM6R5MrgYAhR2TH27JgAN7ivp3iBTmty3q6gvrrGHelPMdGxpJ5aH7AIlodCrPWLAm1lT4XoiuI4s9snXA=="
  "resolved" "https://registry.npmjs.org/rc-mentions/-/rc-mentions-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.6"
    "rc-input" "^0.2.2"
    "rc-menu" "~9.8.0"
    "rc-textarea" "^1.0.0"
    "rc-trigger" "^5.0.4"
    "rc-util" "^5.22.5"

"rc-menu@~9.8.0", "rc-menu@~9.8.2", "rc-menu@~9.8.4":
  "integrity" "sha512-lmw2j8I2fhdIzHmC9ajfImfckt0WDb2KVJJBBRIsxPEw2kGkEfjLMUoB1NgiNT/Q5cC8PdjGOGQjHJIJMwyNMw=="
  "resolved" "https://registry.npmjs.org/rc-menu/-/rc-menu-9.8.4.tgz"
  "version" "9.8.4"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-motion" "^2.4.3"
    "rc-overflow" "^1.2.8"
    "rc-trigger" "^5.1.2"
    "rc-util" "^5.27.0"

"rc-motion@^2.0.0", "rc-motion@^2.0.1", "rc-motion@^2.2.0", "rc-motion@^2.3.0", "rc-motion@^2.3.4", "rc-motion@^2.4.3", "rc-motion@^2.4.4", "rc-motion@^2.6.0", "rc-motion@^2.6.1", "rc-motion@^2.6.2", "rc-motion@^2.9.0":
  "integrity" "sha512-rkW47ABVkic7WEB0EKJqzySpvDqwl60/tdkY7hWP7dYnh5pm0SzJpo54oW3TDUGXV5wfxXFmMkxrzRRbotQ0+w=="
  "resolved" "https://registry.npmjs.org/rc-motion/-/rc-motion-2.9.3.tgz"
  "version" "2.9.3"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "classnames" "^2.2.1"
    "rc-util" "^5.43.0"

"rc-notification@~4.6.0":
  "integrity" "sha512-NSmFYwrrdY3+un1GvDAJQw62Xi9LNMSsoQyo95tuaYrcad5Bn9gJUL8AREufRxSQAQnr64u3LtP3EUyLYT6bhw=="
  "resolved" "https://registry.npmjs.org/rc-notification/-/rc-notification-4.6.1.tgz"
  "version" "4.6.1"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-motion" "^2.2.0"
    "rc-util" "^5.20.1"

"rc-notification@~4.6.1":
  "integrity" "sha512-NSmFYwrrdY3+un1GvDAJQw62Xi9LNMSsoQyo95tuaYrcad5Bn9gJUL8AREufRxSQAQnr64u3LtP3EUyLYT6bhw=="
  "resolved" "https://registry.npmjs.org/rc-notification/-/rc-notification-4.6.1.tgz"
  "version" "4.6.1"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-motion" "^2.2.0"
    "rc-util" "^5.20.1"

"rc-notification@~5.0.0":
  "integrity" "sha512-74wUFiLlyr6lRGEY1m1BaTiDp+0lIT4FRAblMnh9FApyK2JGdsSLbrQ/1rgM7d2N/IX5UIr8kLLW3TdXxFt/jQ=="
  "resolved" "https://registry.npmjs.org/rc-notification/-/rc-notification-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-motion" "^2.6.0"
    "rc-util" "^5.20.1"

"rc-overflow@^1.0.0", "rc-overflow@^1.2.8":
  "integrity" "sha512-RY0nVBlfP9CkxrpgaLlGzkSoh9JhjJLu6Icqs9E7CW6Ewh9s0peF9OHIex4OhfoPsR92LR0fN6BlCY9Z4VoUtA=="
  "resolved" "https://registry.npmjs.org/rc-overflow/-/rc-overflow-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "classnames" "^2.2.1"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^5.19.2"

"rc-pagination@~3.2.0":
  "integrity" "sha512-5tIXjB670WwwcAJzAqp2J+cOBS9W3cH/WU1EiYwXljuZ4vtZXKlY2Idq8FZrnYBz8KhN3vwPo9CoV/SJS6SL1w=="
  "resolved" "https://registry.npmjs.org/rc-pagination/-/rc-pagination-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.1"

"rc-picker@~2.7.0", "rc-picker@~2.7.6":
  "integrity" "sha512-H9if/BUJUZBOhPfWcPeT15JUI3/ntrG9muzERrXDkSoWmDj4yzmBvumozpxYrHwjcKnjyDGAke68d+whWwvhHA=="
  "resolved" "https://registry.npmjs.org/rc-picker/-/rc-picker-2.7.6.tgz"
  "version" "2.7.6"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.1"
    "date-fns" "2.x"
    "dayjs" "1.x"
    "moment" "^2.24.0"
    "rc-trigger" "^5.0.4"
    "rc-util" "^5.37.0"
    "shallowequal" "^1.1.0"

"rc-picker@~3.1.1":
  "integrity" "sha512-Hh3ml+u+5mxLfl4ahVWlRGiX5+0EJrALR6tSW9yP0eea+6j+YjvjfetbvuVidViMDMweZa38dr8HTfAFLG6GFw=="
  "resolved" "https://registry.npmjs.org/rc-picker/-/rc-picker-3.1.5.tgz"
  "version" "3.1.5"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.1"
    "rc-trigger" "^5.0.4"
    "rc-util" "^5.27.0"

"rc-progress@~3.4.1", "rc-progress@~3.4.2":
  "integrity" "sha512-iAGhwWU+tsayP+Jkl9T4+6rHeQTG9kDz8JAHZk4XtQOcYN5fj9H34NXNEdRdZx94VUDHMqCb1yOIvi8eJRh67w=="
  "resolved" "https://registry.npmjs.org/rc-progress/-/rc-progress-3.4.2.tgz"
  "version" "3.4.2"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.6"
    "rc-util" "^5.16.1"

"rc-rate@~2.9.0", "rc-rate@~2.9.3":
  "integrity" "sha512-2THssUSnRhtqIouQIIXqsZGzRczvp4WsH4WvGuhiwm+LG2fVpDUJliP9O1zeDOZvYfBE/Bup4SgHun/eCkbjgQ=="
  "resolved" "https://registry.npmjs.org/rc-rate/-/rc-rate-2.9.3.tgz"
  "version" "2.9.3"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.5"
    "rc-util" "^5.0.1"

"rc-resize-observer@^1.0.0", "rc-resize-observer@^1.1.0", "rc-resize-observer@^1.2.0", "rc-resize-observer@^1.3.1":
  "integrity" "sha512-iFUdt3NNhflbY3mwySv5CA1TC06zdJ+pfo0oc27xpf4PIOvfZwZGtD9Kz41wGYqC4SLio93RVAirSSpYlV/uYg=="
  "resolved" "https://registry.npmjs.org/rc-resize-observer/-/rc-resize-observer-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "@babel/runtime" "^7.20.7"
    "classnames" "^2.2.1"
    "rc-util" "^5.27.0"
    "resize-observer-polyfill" "^1.5.1"

"rc-segmented@~2.1.0", "rc-segmented@~2.1.2":
  "integrity" "sha512-qGo1bCr83ESXpXVOCXjFe1QJlCAQXyi9KCiy8eX3rIMYlTeJr/ftySIaTnYsitL18SvWf5ZEHsfqIWoX0EMfFQ=="
  "resolved" "https://registry.npmjs.org/rc-segmented/-/rc-segmented-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "classnames" "^2.2.1"
    "rc-motion" "^2.4.4"
    "rc-util" "^5.17.0"

"rc-segmented@~2.3.0":
  "integrity" "sha512-I3FtM5Smua/ESXutFfb8gJ8ZPcvFR+qUgeeGFQHBOvRiRKyAk4aBE5nfqrxXx+h8/vn60DQjOt6i4RNtrbOobg=="
  "resolved" "https://registry.npmjs.org/rc-segmented/-/rc-segmented-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "classnames" "^2.2.1"
    "rc-motion" "^2.4.4"
    "rc-util" "^5.17.0"

"rc-select@~14.1.0", "rc-select@~14.1.17", "rc-select@~14.1.18":
  "integrity" "sha512-4JgY3oG2Yz68ECMUSCON7mtxuJvCSj+LJpHEg/AONaaVBxIIrmI/ZTuMJkyojall/X50YdBe5oMKqHHPNiPzEg=="
  "resolved" "https://registry.npmjs.org/rc-select/-/rc-select-14.1.18.tgz"
  "version" "14.1.18"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-motion" "^2.0.1"
    "rc-overflow" "^1.0.0"
    "rc-trigger" "^5.0.4"
    "rc-util" "^5.16.1"
    "rc-virtual-list" "^3.2.0"

"rc-select@~14.2.0":
  "integrity" "sha512-tvxHmbAA0EIhBkB7dyaRhcBUIWHocQbUFY/fBlezj2jg5p65a5VQ/UhBg2I9TA1wjpsr5CCx0ruZPkYcUMjDoQ=="
  "resolved" "https://registry.npmjs.org/rc-select/-/rc-select-14.2.0.tgz"
  "version" "14.2.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-motion" "^2.0.1"
    "rc-overflow" "^1.0.0"
    "rc-trigger" "^5.0.4"
    "rc-util" "^5.16.1"
    "rc-virtual-list" "^3.4.13"

"rc-slider@~10.0.0":
  "integrity" "sha512-igTKF3zBet7oS/3yNiIlmU8KnZ45npmrmHlUUio8PNbIhzMcsh+oE/r2UD42Y6YD2D/s+kzCQkzQrPD6RY435Q=="
  "resolved" "https://registry.npmjs.org/rc-slider/-/rc-slider-10.0.1.tgz"
  "version" "10.0.1"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.5"
    "rc-util" "^5.18.1"
    "shallowequal" "^1.1.0"

"rc-slider@~10.0.1":
  "integrity" "sha512-igTKF3zBet7oS/3yNiIlmU8KnZ45npmrmHlUUio8PNbIhzMcsh+oE/r2UD42Y6YD2D/s+kzCQkzQrPD6RY435Q=="
  "resolved" "https://registry.npmjs.org/rc-slider/-/rc-slider-10.0.1.tgz"
  "version" "10.0.1"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.5"
    "rc-util" "^5.18.1"
    "shallowequal" "^1.1.0"

"rc-slider@~10.1.0":
  "integrity" "sha512-gn8oXazZISEhnmRinI89Z/JD/joAaM35jp+gDtIVSTD/JJMCCBqThqLk1SVJmvtfeiEF/kKaFY0+qt4SDHFUDw=="
  "resolved" "https://registry.npmjs.org/rc-slider/-/rc-slider-10.1.1.tgz"
  "version" "10.1.1"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.5"
    "rc-util" "^5.27.0"

"rc-slider@~8.2.0":
  "integrity" "sha512-rnO36M3VhMoPWh1kRuCeJoluT4duAW7+5aLaAn9oLu2pKEKsuOFUh5DmA2kEo88UmvPV6nr7HHDeZuC8SNM/lA=="
  "resolved" "https://registry.npmjs.org/rc-slider/-/rc-slider-8.2.0.tgz"
  "version" "8.2.0"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "^2.2.5"
    "prop-types" "^15.5.4"
    "rc-tooltip" "^3.4.2"
    "rc-util" "^4.0.4"
    "shallowequal" "^1.0.1"
    "warning" "^3.0.0"

"rc-steps@~5.0.0-alpha.2":
  "integrity" "sha512-9TgRvnVYirdhbV0C3syJFj9EhCRqoJAsxt4i1rED5o8/ZcSv5TLIYyo4H8MCjLPvbe2R+oBAm/IYBEtC+OS1Rw=="
  "resolved" "https://registry.npmjs.org/rc-steps/-/rc-steps-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "@babel/runtime" "^7.16.7"
    "classnames" "^2.2.3"
    "rc-util" "^5.16.1"

"rc-steps@~5.0.0":
  "integrity" "sha512-9TgRvnVYirdhbV0C3syJFj9EhCRqoJAsxt4i1rED5o8/ZcSv5TLIYyo4H8MCjLPvbe2R+oBAm/IYBEtC+OS1Rw=="
  "resolved" "https://registry.npmjs.org/rc-steps/-/rc-steps-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "@babel/runtime" "^7.16.7"
    "classnames" "^2.2.3"
    "rc-util" "^5.16.1"

"rc-steps@~6.0.0":
  "integrity" "sha512-+KfMZIty40mYCQSDvYbZ1jwnuObLauTiIskT1hL4FFOBHP6ZOr8LK0m143yD3kEN5XKHSEX1DIwCj3AYZpoeNQ=="
  "resolved" "https://registry.npmjs.org/rc-steps/-/rc-steps-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@babel/runtime" "^7.16.7"
    "classnames" "^2.2.3"
    "rc-util" "^5.16.1"

"rc-swipeout@~2.0.0":
  "integrity" "sha512-d37Lgn4RX4OOQyuA2BFo0rGlUwrmZk5q83srH3ixJ1Y1jidr2GKjgJDbNeGUVZPNfYBL91Elu6+xfVGftWf4Lg=="
  "resolved" "https://registry.npmjs.org/rc-swipeout/-/rc-swipeout-2.0.11.tgz"
  "version" "2.0.11"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "2.x"
    "rc-gesture" "~0.0.22"
    "react-native-swipeout" "^2.2.2"

"rc-switch@~3.2.0":
  "integrity" "sha512-+gUJClsZZzvAHGy1vZfnwySxj+MjLlGRyXKXScrtCTcmiYNPzxDFOxdQ/3pK1Kt/0POvwJ/6ALOR8gwdXGhs+A=="
  "resolved" "https://registry.npmjs.org/rc-switch/-/rc-switch-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.1"
    "rc-util" "^5.0.1"

"rc-switch@~3.2.2":
  "integrity" "sha512-+gUJClsZZzvAHGy1vZfnwySxj+MjLlGRyXKXScrtCTcmiYNPzxDFOxdQ/3pK1Kt/0POvwJ/6ALOR8gwdXGhs+A=="
  "resolved" "https://registry.npmjs.org/rc-switch/-/rc-switch-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.1"
    "rc-util" "^5.0.1"

"rc-switch@~4.0.0":
  "integrity" "sha512-IfrYC99vN0gKaTyjQdqYuADU0eH00SAFHg3jOp8HrmUpJruhV1SohJzrCbPqPraZeX/6X/QKkdLfkdnUub05WA=="
  "resolved" "https://registry.npmjs.org/rc-switch/-/rc-switch-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.1"
    "rc-util" "^5.0.1"

"rc-table@~7.26.0":
  "integrity" "sha512-0cD8e6S+DTGAt5nBZQIPFYEaIukn17sfa5uFL98faHlH/whZzD8ii3dbFL4wmUDEL4BLybhYop+QUfZJ4CPvNQ=="
  "resolved" "https://registry.npmjs.org/rc-table/-/rc-table-7.26.0.tgz"
  "version" "7.26.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.5"
    "rc-resize-observer" "^1.1.0"
    "rc-util" "^5.22.5"
    "shallowequal" "^1.1.0"

"rc-table@~7.30.2":
  "integrity" "sha512-PHe+lZKwPo3qui5j79m54vKu8b4hebk04x+4Hy65NvwUU3+NNFGS5FZpylXQMkueMnE8hgh22ZuScQDkCtzQFQ=="
  "resolved" "https://registry.npmjs.org/rc-table/-/rc-table-7.30.3.tgz"
  "version" "7.30.3"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/context" "^1.3.0"
    "classnames" "^2.2.5"
    "rc-resize-observer" "^1.1.0"
    "rc-util" "^5.27.1"

"rc-tabs@~12.5.1", "rc-tabs@~12.5.10", "rc-tabs@~12.5.6":
  "integrity" "sha512-Ay0l0jtd4eXepFH9vWBvinBjqOpqzcsJTerBGwJy435P2S90Uu38q8U/mvc1sxUEVOXX5ZCFbxcWPnfG3dH+tQ=="
  "resolved" "https://registry.npmjs.org/rc-tabs/-/rc-tabs-12.5.10.tgz"
  "version" "12.5.10"
  dependencies:
    "@babel/runtime" "^7.11.2"
    "classnames" "2.x"
    "rc-dropdown" "~4.0.0"
    "rc-menu" "~9.8.0"
    "rc-motion" "^2.6.2"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^5.16.0"

"rc-textarea@^0.4.0", "rc-textarea@~0.4.5", "rc-textarea@~0.4.7":
  "integrity" "sha512-IQPd1CDI3mnMlkFyzt2O4gQ2lxUsnBAeJEoZGJnkkXgORNqyM9qovdrCj9NzcRfpHgLdzaEbU3AmobNFGUznwQ=="
  "resolved" "https://registry.npmjs.org/rc-textarea/-/rc-textarea-0.4.7.tgz"
  "version" "0.4.7"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.1"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^5.24.4"
    "shallowequal" "^1.1.0"

"rc-textarea@^1.0.0":
  "integrity" "sha512-846kjD/RYZx/th32FW4T80IrRTt2dT7+kxdToI7pwzJPlsfmZyo8e2F2m0FLcvriv6rtAUMSqQRH1HC3i+sAbw=="
  "resolved" "https://registry.npmjs.org/rc-textarea/-/rc-textarea-1.3.3.tgz"
  "version" "1.3.3"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.1"
    "rc-input" "~1.1.0"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^5.27.0"

"rc-textarea@~1.0.0":
  "integrity" "sha512-dtIm96apjJpCUcCeTtbnLGJaVlqbOqVgN0P9z+bqMSi7rcV5QVeUtBnG+jQTGk/uD183Z7jbhc8Dx7G3luDCwg=="
  "resolved" "https://registry.npmjs.org/rc-textarea/-/rc-textarea-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.1"
    "rc-input" "^0.2.1"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^5.27.0"

"rc-tooltip@^3.4.2":
  "integrity" "sha512-dE2ibukxxkrde7wH9W8ozHKUO4aQnPZ6qBHtrTH9LoO836PjDdiaWO73fgPB05VfJs9FbZdmGPVEbXCeOP99Ww=="
  "resolved" "https://registry.npmjs.org/rc-tooltip/-/rc-tooltip-3.7.3.tgz"
  "version" "3.7.3"
  dependencies:
    "babel-runtime" "6.x"
    "prop-types" "^15.5.8"
    "rc-trigger" "^2.2.2"

"rc-tooltip@~5.2.0":
  "integrity" "sha512-jtQzU/18S6EI3lhSGoDYhPqNpWajMtS5VV/ld1LwyfrDByQpYmw/LW6U7oFXXLukjfDHQ7Ju705A82PRNFWYhg=="
  "resolved" "https://registry.npmjs.org/rc-tooltip/-/rc-tooltip-5.2.2.tgz"
  "version" "5.2.2"
  dependencies:
    "@babel/runtime" "^7.11.2"
    "classnames" "^2.3.1"
    "rc-trigger" "^5.0.0"

"rc-tooltip@~5.2.2":
  "integrity" "sha512-jtQzU/18S6EI3lhSGoDYhPqNpWajMtS5VV/ld1LwyfrDByQpYmw/LW6U7oFXXLukjfDHQ7Ju705A82PRNFWYhg=="
  "resolved" "https://registry.npmjs.org/rc-tooltip/-/rc-tooltip-5.2.2.tgz"
  "version" "5.2.2"
  dependencies:
    "@babel/runtime" "^7.11.2"
    "classnames" "^2.3.1"
    "rc-trigger" "^5.0.0"

"rc-tooltip@~5.3.1":
  "integrity" "sha512-e6H0dMD38EPaSPD2XC8dRfct27VvT2TkPdoBSuNl3RRZ5tspiY/c5xYEmGC0IrABvMBgque4Mr2SMZuliCvoiQ=="
  "resolved" "https://registry.npmjs.org/rc-tooltip/-/rc-tooltip-5.3.1.tgz"
  "version" "5.3.1"
  dependencies:
    "@babel/runtime" "^7.11.2"
    "classnames" "^2.3.1"
    "rc-trigger" "^5.3.1"

"rc-tree-select@~5.5.0":
  "integrity" "sha512-k2av7jF6tW9bIO4mQhaVdV4kJ1c54oxV3/hHVU+oD251Gb5JN+m1RbJFTMf1o0rAFqkvto33rxMdpafaGKQRJw=="
  "resolved" "https://registry.npmjs.org/rc-tree-select/-/rc-tree-select-5.5.5.tgz"
  "version" "5.5.5"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-select" "~14.1.0"
    "rc-tree" "~5.7.0"
    "rc-util" "^5.16.1"

"rc-tree-select@~5.5.5":
  "integrity" "sha512-k2av7jF6tW9bIO4mQhaVdV4kJ1c54oxV3/hHVU+oD251Gb5JN+m1RbJFTMf1o0rAFqkvto33rxMdpafaGKQRJw=="
  "resolved" "https://registry.npmjs.org/rc-tree-select/-/rc-tree-select-5.5.5.tgz"
  "version" "5.5.5"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-select" "~14.1.0"
    "rc-tree" "~5.7.0"
    "rc-util" "^5.16.1"

"rc-tree-select@~5.6.0":
  "integrity" "sha512-XG6pu0a9l6+mzhQqUYfR2VIONbe/3LjVc3wKt28k6uBMZsI1j+SSxRyt/7jWRq8Kok8jHJBQASlDg6ehr9Sp0w=="
  "resolved" "https://registry.npmjs.org/rc-tree-select/-/rc-tree-select-5.6.0.tgz"
  "version" "5.6.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-select" "~14.2.0"
    "rc-tree" "~5.7.0"
    "rc-util" "^5.16.1"

"rc-tree@~5.7.0", "rc-tree@~5.7.12":
  "integrity" "sha512-LXA5nY2hG5koIAlHW5sgXgLpOMz+bFRbnZZ+cCg0tQs4Wv1AmY7EDi1SK7iFXhslYockbqUerQan82jljoaItg=="
  "resolved" "https://registry.npmjs.org/rc-tree/-/rc-tree-5.7.12.tgz"
  "version" "5.7.12"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-motion" "^2.0.1"
    "rc-util" "^5.16.1"
    "rc-virtual-list" "^3.5.1"

"rc-trigger@^2.2.2":
  "integrity" "sha512-m6Cts9hLeZWsTvWnuMm7oElhf+03GOjOLfTuU0QmdB9ZrW7jR2IpI5rpNM7i9MvAAlMAmTx5Zr7g3uu/aMvZAw=="
  "resolved" "https://registry.npmjs.org/rc-trigger/-/rc-trigger-2.6.5.tgz"
  "version" "2.6.5"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "^2.2.6"
    "prop-types" "15.x"
    "rc-align" "^2.4.0"
    "rc-animate" "2.x"
    "rc-util" "^4.4.0"
    "react-lifecycles-compat" "^3.0.4"

"rc-trigger@^5.0.0", "rc-trigger@^5.0.4", "rc-trigger@^5.1.2", "rc-trigger@^5.2.10", "rc-trigger@^5.3.1", "rc-trigger@^5.3.4":
  "integrity" "sha512-mQv+vas0TwKcjAO2izNPkqR4j86OemLRmvL2nOzdP9OWNWA1ivoTt5hzFqYNW9zACwmTezRiN8bttrC7cZzYSw=="
  "resolved" "https://registry.npmjs.org/rc-trigger/-/rc-trigger-5.3.4.tgz"
  "version" "5.3.4"
  dependencies:
    "@babel/runtime" "^7.18.3"
    "classnames" "^2.2.6"
    "rc-align" "^4.0.0"
    "rc-motion" "^2.0.0"
    "rc-util" "^5.19.2"

"rc-upload@~4.3.0", "rc-upload@~4.3.6":
  "integrity" "sha512-Bt7ESeG5tT3IY82fZcP+s0tQU2xmo1W6P3S8NboUUliquJLQYLkUcsaExi3IlBVr43GQMCjo30RA2o0i70+NjA=="
  "resolved" "https://registry.npmjs.org/rc-upload/-/rc-upload-4.3.6.tgz"
  "version" "4.3.6"
  dependencies:
    "@babel/runtime" "^7.18.3"
    "classnames" "^2.2.5"
    "rc-util" "^5.2.0"

"rc-util@^4.0.4", "rc-util@^4.4.0":
  "integrity" "sha512-Z+vlkSQVc1l8O2UjR3WQ+XdWlhj5q9BMQNLk2iOBch75CqPfrJyGtcWMcnhRlNuDu0Ndtt4kLVO8JI8BrABobg=="
  "resolved" "https://registry.npmjs.org/rc-util/-/rc-util-4.21.1.tgz"
  "version" "4.21.1"
  dependencies:
    "add-dom-event-listener" "^1.1.0"
    "prop-types" "^15.5.10"
    "react-is" "^16.12.0"
    "react-lifecycles-compat" "^3.0.4"
    "shallowequal" "^1.1.0"

"rc-util@^4.15.3":
  "integrity" "sha512-Z+vlkSQVc1l8O2UjR3WQ+XdWlhj5q9BMQNLk2iOBch75CqPfrJyGtcWMcnhRlNuDu0Ndtt4kLVO8JI8BrABobg=="
  "resolved" "https://registry.npmjs.org/rc-util/-/rc-util-4.21.1.tgz"
  "version" "4.21.1"
  dependencies:
    "add-dom-event-listener" "^1.1.0"
    "prop-types" "^15.5.10"
    "react-is" "^16.12.0"
    "react-lifecycles-compat" "^3.0.4"
    "shallowequal" "^1.1.0"

"rc-util@^4.19.0":
  "integrity" "sha512-Z+vlkSQVc1l8O2UjR3WQ+XdWlhj5q9BMQNLk2iOBch75CqPfrJyGtcWMcnhRlNuDu0Ndtt4kLVO8JI8BrABobg=="
  "resolved" "https://registry.npmjs.org/rc-util/-/rc-util-4.21.1.tgz"
  "version" "4.21.1"
  dependencies:
    "add-dom-event-listener" "^1.1.0"
    "prop-types" "^15.5.10"
    "react-is" "^16.12.0"
    "react-lifecycles-compat" "^3.0.4"
    "shallowequal" "^1.1.0"

"rc-util@^5.0.1", "rc-util@^5.0.6", "rc-util@^5.15.0", "rc-util@^5.16.0", "rc-util@^5.16.1", "rc-util@^5.17.0", "rc-util@^5.18.1", "rc-util@^5.19.2", "rc-util@^5.2.0", "rc-util@^5.2.1", "rc-util@^5.20.1", "rc-util@^5.21.0", "rc-util@^5.21.2", "rc-util@^5.22.5", "rc-util@^5.23.0", "rc-util@^5.24.4", "rc-util@^5.25.2", "rc-util@^5.26.0", "rc-util@^5.27.0", "rc-util@^5.27.1", "rc-util@^5.31.1", "rc-util@^5.32.2", "rc-util@^5.34.1", "rc-util@^5.37.0", "rc-util@^5.4.0", "rc-util@^5.43.0", "rc-util@^5.6.1", "rc-util@^5.8.0", "rc-util@^5.9.4":
  "integrity" "sha512-AzC7KKOXFqAdIBqdGWepL9Xn7cm3vnAmjlHqUnoQaTMZYhM4VlXGLkkHHxj/BZ7Td0+SOPKB4RGPboBVKT9htw=="
  "resolved" "https://registry.npmjs.org/rc-util/-/rc-util-5.43.0.tgz"
  "version" "5.43.0"
  dependencies:
    "@babel/runtime" "^7.18.3"
    "react-is" "^18.2.0"

"rc-util@4.x":
  "integrity" "sha512-Z+vlkSQVc1l8O2UjR3WQ+XdWlhj5q9BMQNLk2iOBch75CqPfrJyGtcWMcnhRlNuDu0Ndtt4kLVO8JI8BrABobg=="
  "resolved" "https://registry.npmjs.org/rc-util/-/rc-util-4.21.1.tgz"
  "version" "4.21.1"
  dependencies:
    "add-dom-event-listener" "^1.1.0"
    "prop-types" "^15.5.10"
    "react-is" "^16.12.0"
    "react-lifecycles-compat" "^3.0.4"
    "shallowequal" "^1.1.0"

"rc-virtual-list@^3.2.0", "rc-virtual-list@^3.4.13", "rc-virtual-list@^3.5.1":
  "integrity" "sha512-rG6IuD4EYM8K6oZ8Shu2BC/CmcTdqng4yBWkc/5fjWhB20bl6QwR2Upyt7+MxvfscoVm8zOQY+tcpEO5cu4GaQ=="
  "resolved" "https://registry.npmjs.org/rc-virtual-list/-/rc-virtual-list-3.5.3.tgz"
  "version" "3.5.3"
  dependencies:
    "@babel/runtime" "^7.20.0"
    "classnames" "^2.2.6"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^5.15.0"

"react-dom@*", "react-dom@^0.14.0 || ^15.0.0 || ^16.0.0-beta || ^16.0.0", "react-dom@^16.3.0 || ^17.0.0", "react-dom@>=16.0.0", "react-dom@>=16.11.0", "react-dom@>=16.9.0", "react-dom@15.x || ^16.0.0-0", "react-dom@16.x || 17.x", "react-dom@17.x":
  "integrity" "sha512-s4h96KtLDUQlsENhMn1ar8t2bEa+q/YAtj8pPPdIjPDGBDIVNsrD9aXNWqspUe6AzKCIG0C1HZZLqLV7qpOBGA=="
  "resolved" "https://registry.npmjs.org/react-dom/-/react-dom-17.0.2.tgz"
  "version" "17.0.2"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"
    "scheduler" "^0.20.2"

"react-dom@^16.8.4":
  "integrity" "sha512-1gCeQXDLoIqMgqD3IO2Ah9bnf0w9kzhwN5q4FGnHZ67hBm9yePzB5JJAIQCc8x3pFnNlwFq4RidZggNAAkzWWw=="
  "resolved" "https://registry.npmjs.org/react-dom/-/react-dom-16.14.0.tgz"
  "version" "16.14.0"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"
    "prop-types" "^15.6.2"
    "scheduler" "^0.19.1"

"react-dom@16.x":
  "integrity" "sha512-1gCeQXDLoIqMgqD3IO2Ah9bnf0w9kzhwN5q4FGnHZ67hBm9yePzB5JJAIQCc8x3pFnNlwFq4RidZggNAAkzWWw=="
  "resolved" "https://registry.npmjs.org/react-dom/-/react-dom-16.14.0.tgz"
  "version" "16.14.0"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"
    "prop-types" "^15.6.2"
    "scheduler" "^0.19.1"

"react-fast-compare@^3.1.1":
  "integrity" "sha512-nsO+KSNgo1SbJqJEYRE9ERzo7YtYbou/OqjSQKxV7jcKox7+usiUVZOAC+XnDOABXggQTno0Y1CpVnuWEc1boQ=="
  "resolved" "https://registry.npmjs.org/react-fast-compare/-/react-fast-compare-3.2.2.tgz"
  "version" "3.2.2"

"react-helmet@^6.1.0":
  "integrity" "sha512-4uMzEY9nlDlgxr61NL3XbKRy1hEkXmKNXhjbAIOVw5vcFrsdYbH2FEwcNyWvWinl103nXgzYNlns9ca+8kFiWw=="
  "resolved" "https://registry.npmjs.org/react-helmet/-/react-helmet-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "object-assign" "^4.1.1"
    "prop-types" "^15.7.2"
    "react-fast-compare" "^3.1.1"
    "react-side-effect" "^2.1.0"

"react-intl@3.12.1":
  "integrity" "sha512-cgumW29mwROIqyp8NXStYsoIm27+8FqnxykiLSawWjOxGIBeLuN/+p2srei5SRIumcJefOkOIHP+NDck05RgHg=="
  "resolved" "https://registry.npmjs.org/react-intl/-/react-intl-3.12.1.tgz"
  "version" "3.12.1"
  dependencies:
    "@formatjs/intl-displaynames" "^1.2.0"
    "@formatjs/intl-listformat" "^1.4.1"
    "@formatjs/intl-relativetimeformat" "^4.5.9"
    "@formatjs/intl-unified-numberformat" "^3.2.0"
    "@formatjs/intl-utils" "^2.2.0"
    "@types/hoist-non-react-statics" "^3.3.1"
    "@types/invariant" "^2.2.31"
    "hoist-non-react-statics" "^3.3.2"
    "intl-format-cache" "^4.2.21"
    "intl-messageformat" "^7.8.4"
    "intl-messageformat-parser" "^3.6.4"
    "shallow-equal" "^1.2.1"

"react-is@^16.12.0", "react-is@^16.13.1", "react-is@^16.6.0", "react-is@^16.7.0":
  "integrity" "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  "version" "16.13.1"

"react-is@^17.0.1":
  "integrity" "sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz"
  "version" "17.0.2"

"react-is@^17.0.2":
  "integrity" "sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz"
  "version" "17.0.2"

"react-is@^18.2.0":
  "integrity" "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz"
  "version" "18.3.1"

"react-lifecycles-compat@^3.0.0", "react-lifecycles-compat@^3.0.4":
  "integrity" "sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA=="
  "resolved" "https://registry.npmjs.org/react-lifecycles-compat/-/react-lifecycles-compat-3.0.4.tgz"
  "version" "3.0.4"

"react-native-swipeout@^2.2.2":
  "integrity" "sha512-t9suUCspzck4vp2pWggWe0frS/QOtX6yYCawHnEes75A7dZCEE74bxX2A1bQzGH9cUMjq6xsdfC94RbiDKIkJg=="
  "resolved" "https://registry.npmjs.org/react-native-swipeout/-/react-native-swipeout-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "create-react-class" "^15.6.0"
    "prop-types" "^15.5.10"
    "react-tween-state" "^0.1.5"

"react-redux@^5.0.5":
  "integrity" "sha512-Ns1G0XXc8hDyH/OcBHOxNgQx9ayH3SPxBnFCOidGKSle8pKihysQw2rG/PmciUQRoclhVBO8HMhiRmGXnDja9Q=="
  "resolved" "https://registry.npmjs.org/react-redux/-/react-redux-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "@babel/runtime" "^7.1.2"
    "hoist-non-react-statics" "^3.3.0"
    "invariant" "^2.2.4"
    "loose-envify" "^1.1.0"
    "prop-types" "^15.6.1"
    "react-is" "^16.6.0"
    "react-lifecycles-compat" "^3.0.0"

"react-redux@^6.0.0 || ^7.1.0", "react-redux@^7.1.0":
  "integrity" "sha512-Gx4L3uM182jEEayZfRbI/G11ZpYdNAnBs70lFVMNdHJI76XYtR+7m0MN+eAs7UHBPhWXcnFPaS+9owSCJQHNpQ=="
  "resolved" "https://registry.npmjs.org/react-redux/-/react-redux-7.2.9.tgz"
  "version" "7.2.9"
  dependencies:
    "@babel/runtime" "^7.15.4"
    "@types/react-redux" "^7.1.20"
    "hoist-non-react-statics" "^3.3.2"
    "loose-envify" "^1.4.0"
    "prop-types" "^15.7.2"
    "react-is" "^17.0.2"

"react-refresh@0.10.0":
  "integrity" "sha512-PgidR3wST3dDYKr6b4pJoqQFpPGNKDSCDx4cZoshjXipw3LzO7mG1My2pwEzz2JVkF+inx3xRpDeQLFQGH/hsQ=="
  "resolved" "https://registry.npmjs.org/react-refresh/-/react-refresh-0.10.0.tgz"
  "version" "0.10.0"

"react-router-config@5.1.1":
  "integrity" "sha512-DuanZjaD8mQp1ppHjgnnUnyOlqYXZVjnov/JzFhjLEwd3Z4dYjMSnqrEzzGThH47vpCOqPPwJM2FtthLeJ8Pbg=="
  "resolved" "https://registry.npmjs.org/react-router-config/-/react-router-config-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "@babel/runtime" "^7.1.2"

"react-router-dom@^4.1.2":
  "integrity" "sha512-c/MlywfxDdCp7EnB7YfPMOfMD3tOtIjrQlj/CKfNMBxdmpJP8xcz5P/UAFn3JbnQCNUxsHyVVqllF9LhgVyFCA=="
  "resolved" "https://registry.npmjs.org/react-router-dom/-/react-router-dom-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "history" "^4.7.2"
    "invariant" "^2.2.4"
    "loose-envify" "^1.3.1"
    "prop-types" "^15.6.1"
    "react-router" "^4.3.1"
    "warning" "^4.0.1"

"react-router-dom@^5.1.2", "react-router-dom@5.2.0":
  "integrity" "sha512-gxAmfylo2QUjcwxI63RhQ5G85Qqt4voZpUXSEqCwykV0baaOTQDR1f0PmY8AELqIyVc0NEZUj0Gov5lNGcXgsA=="
  "resolved" "https://registry.npmjs.org/react-router-dom/-/react-router-dom-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/runtime" "^7.1.2"
    "history" "^4.9.0"
    "loose-envify" "^1.3.1"
    "prop-types" "^15.6.2"
    "react-router" "5.2.0"
    "tiny-invariant" "^1.0.2"
    "tiny-warning" "^1.0.0"

"react-router-redux@5.0.0-alpha.9":
  "integrity" "sha512-euSgNIANnRXr4GydIuwA7RZCefrLQzIw5WdXspS8NPYbV+FxrKSS9MKG7U9vb6vsKHONnA4VxrVNWfnMUnUQAw=="
  "resolved" "https://registry.npmjs.org/react-router-redux/-/react-router-redux-5.0.0-alpha.9.tgz"
  "version" "5.0.0-alpha.9"
  dependencies:
    "history" "^4.7.2"
    "prop-types" "^15.6.0"
    "react-router" "^4.2.0"

"react-router@^4.2.0":
  "integrity" "sha512-yrvL8AogDh2X42Dt9iknk4wF4V8bWREPirFfS9gLU1huk6qK41sg7Z/1S81jjTrGHxa3B8R3J6xIkDAA6CVarg=="
  "resolved" "https://registry.npmjs.org/react-router/-/react-router-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "history" "^4.7.2"
    "hoist-non-react-statics" "^2.5.0"
    "invariant" "^2.2.4"
    "loose-envify" "^1.3.1"
    "path-to-regexp" "^1.7.0"
    "prop-types" "^15.6.1"
    "warning" "^4.0.1"

"react-router@^4.3.1 || ^5.0.0", "react-router@>=5", "react-router@5.2.0":
  "integrity" "sha512-smz1DUuFHRKdcJC0jobGo8cVbhO3x50tCL4icacOlcwDOEQPq4TMqwx3sY1TP+DvtTgz4nm3thuo7A+BK2U0Dw=="
  "resolved" "https://registry.npmjs.org/react-router/-/react-router-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/runtime" "^7.1.2"
    "history" "^4.9.0"
    "hoist-non-react-statics" "^3.1.0"
    "loose-envify" "^1.3.1"
    "mini-create-react-context" "^0.4.0"
    "path-to-regexp" "^1.7.0"
    "prop-types" "^15.6.2"
    "react-is" "^16.6.0"
    "tiny-invariant" "^1.0.2"
    "tiny-warning" "^1.0.0"

"react-router@^4.3.1":
  "integrity" "sha512-yrvL8AogDh2X42Dt9iknk4wF4V8bWREPirFfS9gLU1huk6qK41sg7Z/1S81jjTrGHxa3B8R3J6xIkDAA6CVarg=="
  "resolved" "https://registry.npmjs.org/react-router/-/react-router-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "history" "^4.7.2"
    "hoist-non-react-statics" "^2.5.0"
    "invariant" "^2.2.4"
    "loose-envify" "^1.3.1"
    "path-to-regexp" "^1.7.0"
    "prop-types" "^15.6.1"
    "warning" "^4.0.1"

"react-side-effect@^2.1.0":
  "integrity" "sha512-PVjOcvVOyIILrYoyGEpDN3vmYNLdy1CajSFNt4TDsVQC5KpTijDvWVoR+/7Rz2xT978D8/ZtFceXxzsPwZEDvw=="
  "resolved" "https://registry.npmjs.org/react-side-effect/-/react-side-effect-2.1.2.tgz"
  "version" "2.1.2"

"react-sortable-hoc@^2.0.0":
  "integrity" "sha512-JZUw7hBsAHXK7PTyErJyI7SopSBFRcFHDjWW5SWjcugY0i6iH7f+eJkY8cJmGMlZ1C9xz1J3Vjz0plFpavVeRg=="
  "resolved" "https://registry.npmjs.org/react-sortable-hoc/-/react-sortable-hoc-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@babel/runtime" "^7.2.0"
    "invariant" "^2.2.4"
    "prop-types" "^15.5.7"

"react-tween-state@^0.1.5":
  "integrity" "sha512-sJQpjsdn0wjlDIUpfpb7jQGnOG8hAEW2e8k0KPA+xmf5KFa6Xat2JldbmxBhaqP0S/uIXhVE5ymKyH/b9X8nYA=="
  "resolved" "https://registry.npmjs.org/react-tween-state/-/react-tween-state-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "raf" "^3.1.0"
    "tween-functions" "^1.0.1"

"react@*", "react@^0.14.0 || ^15.0.0 || ^16.0.0 || ^17.0.0", "react@^0.14.0 || ^15.0.0 || ^16.0.0-beta || ^16.0.0", "react@^0.14.0 || ^15.0.0-0 || ^16.0.0-0", "react@^16.11.0 || ^17.0.0 || ^18.0.0", "react@^16.3.0", "react@^16.3.0 || ^17.0.0", "react@^16.3.0 || ^17.0.0 || ^18.0.0", "react@^16.8.0 || ^17 || ^18", "react@^16.8.0 || ^17.0.0", "react@^16.8.0 || ^17.0.0 || ^18.0.0", "react@^16.8.3 || ^17 || ^18", "react@>=15", "react@>=16.0.0", "react@>=16.11.0", "react@>=16.12.0", "react@>=16.3.0", "react@>=16.9.0", "react@15.x || ^16.0.0-0", "react@16.x || 17.x", "react@17.0.2", "react@17.x":
  "integrity" "sha512-gnhPt75i/dq/z3/6q/0asP78D0u592D5L1pd7M8P+dck6Fu/jJeL6iVVK23fptSUZj8Vjf++7wXA8UNclGQcbA=="
  "resolved" "https://registry.npmjs.org/react/-/react-17.0.2.tgz"
  "version" "17.0.2"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"

"react@^16.14.0", "react@^16.4.0", "react@^16.8.4", "react@16.x":
  "integrity" "sha512-0X2CImDkJGApiAlcf0ODKIneSwBPhqJawOa5wCtKbu7ZECrmS26NvtSILynQ66cgkT/RJ4LidJOc3bUESwmU8g=="
  "resolved" "https://registry.npmjs.org/react/-/react-16.14.0.tgz"
  "version" "16.14.0"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"
    "prop-types" "^15.6.2"

"reactcss@^1.2.3":
  "integrity" "sha512-KiwVUcFu1RErkI97ywr8nvx8dNOpT03rbnma0SSalTYjkrPYaEajR4a/MRt6DZ46K6arDRbWMNHF+xH7G7n/8A=="
  "resolved" "https://registry.npmjs.org/reactcss/-/reactcss-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "lodash" "^4.0.1"

"read-pkg-up@^7.0.1":
  "integrity" "sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg=="
  "resolved" "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "find-up" "^4.1.0"
    "read-pkg" "^5.2.0"
    "type-fest" "^0.8.1"

"read-pkg@^5.2.0":
  "integrity" "sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg=="
  "resolved" "https://registry.npmjs.org/read-pkg/-/read-pkg-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    "normalize-package-data" "^2.5.0"
    "parse-json" "^5.0.0"
    "type-fest" "^0.6.0"

"readable-stream@^2.0.2", "readable-stream@^2.3.3", "readable-stream@^2.3.6":
  "integrity" "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz"
  "version" "2.3.8"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^3.6.0":
  "integrity" "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz"
  "version" "3.6.2"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readdirp@~3.5.0":
  "integrity" "sha512-cMhu7c/8rdhkHXWsY+osBhfSy0JikwpHK/5+imo+LpeasTF8ouErHrlYkwT0++njiyuDvc7OFY5T3ukvZ8qmFQ=="
  "resolved" "https://registry.npmjs.org/readdirp/-/readdirp-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "picomatch" "^2.2.1"

"redbox-react@1.x":
  "integrity" "sha512-mLjM5eYR41yOp5YKHpd3syFeGq6B4Wj5vZr64nbLvTZW5ZLff4LYk7VE4ITpVxkZpCY6OZuqh0HiP3A3uEaCpg=="
  "resolved" "https://registry.npmjs.org/redbox-react/-/redbox-react-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "error-stack-parser" "^1.3.6"
    "object-assign" "^4.0.1"
    "prop-types" "^15.5.4"
    "sourcemapped-stacktrace" "^1.1.6"

"redux-saga@^0.16.0":
  "integrity" "sha512-iIjKnRThI5sKPEASpUvySemjzwqwI13e3qP7oLub+FycCRDysLSAOwt958niZW6LhxfmS6Qm1BzbU70w/Koc4w=="
  "resolved" "https://registry.npmjs.org/redux-saga/-/redux-saga-0.16.2.tgz"
  "version" "0.16.2"

"redux@^2.0.0 || ^3.0.0 || ^4.0.0-0", "redux@^3.7.1", "redux@^3.7.2", "redux@>= 3.7.2":
  "integrity" "sha512-pNqnf9q1hI5HHZRBkj3bAngGZW/JMCmexDlOxw4XagXY2o1327nHH54LoTjiPJ0gizoqPDRqWyX/00g0hD6w+A=="
  "resolved" "https://registry.npmjs.org/redux/-/redux-3.7.2.tgz"
  "version" "3.7.2"
  dependencies:
    "lodash" "^4.2.1"
    "lodash-es" "^4.2.1"
    "loose-envify" "^1.1.0"
    "symbol-observable" "^1.0.3"

"redux@^3.6.0 || ^4.0.0", "redux@^4.0.1", "redux@4.x":
  "integrity" "sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w=="
  "resolved" "https://registry.npmjs.org/redux/-/redux-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "@babel/runtime" "^7.9.2"

"redux@^4.0.0":
  "integrity" "sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w=="
  "resolved" "https://registry.npmjs.org/redux/-/redux-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "@babel/runtime" "^7.9.2"

"regenerate-unicode-properties@10.0.1":
  "integrity" "sha512-vn5DU6yg6h8hP/2OkQo3K7uVILvY4iu0oI4t3HFa81UPkhGJwkRwM10JEc3upjdhHjs/k8GJY1sRBhk5sr69Bw=="
  "resolved" "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.0.1.tgz"
  "version" "10.0.1"
  dependencies:
    "regenerate" "^1.4.2"

"regenerate@^1.4.2", "regenerate@1.4.2":
  "integrity" "sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A=="
  "resolved" "https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz"
  "version" "1.4.2"

"regenerator-runtime@^0.11.0":
  "integrity" "sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz"
  "version" "0.11.1"

"regenerator-runtime@^0.13.4", "regenerator-runtime@^0.13.7":
  "integrity" "sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz"
  "version" "0.13.11"

"regenerator-runtime@^0.14.0":
  "integrity" "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz"
  "version" "0.14.1"

"regenerator-runtime@0.13.5":
  "integrity" "sha512-ZS5w8CpKFinUzOwW3c83oPeVXoNsrLsaCoLtJvAClH135j/R77RuymhiSErhm2lKcwSCIpmvIWSbDkIfAqKQlA=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.5.tgz"
  "version" "0.13.5"

"regex-not@^1.0.0", "regex-not@^1.0.2":
  "integrity" "sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A=="
  "resolved" "https://registry.npmjs.org/regex-not/-/regex-not-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "extend-shallow" "^3.0.2"
    "safe-regex" "^1.1.0"

"remove-trailing-separator@^1.0.1":
  "integrity" "sha512-/hS+Y0u3aOfIETiaiirUFwDBDzmXPvO+jAfKTitUngIPzdKc6Z0LoFjM/CK5PL4C+eKwHohlHAb6H0VFfmmUsw=="
  "resolved" "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz"
  "version" "1.1.0"

"repeat-element@^1.1.2":
  "integrity" "sha512-LFiNfRcSu7KK3evMyYOuCzv3L10TW7yC1G2/+StMjK8Y6Vqd2MG7r/Qjw4ghtuCOjFvlnms/iMmLqpvW/ES/WQ=="
  "resolved" "https://registry.npmjs.org/repeat-element/-/repeat-element-1.1.4.tgz"
  "version" "1.1.4"

"repeat-string@^1.6.1":
  "integrity" "sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w=="
  "resolved" "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz"
  "version" "1.6.1"

"require-directory@^2.1.1":
  "integrity" "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q=="
  "resolved" "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-main-filename@^2.0.0":
  "integrity" "sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg=="
  "resolved" "https://registry.npmjs.org/require-main-filename/-/require-main-filename-2.0.0.tgz"
  "version" "2.0.0"

"requires-port@^1.0.0":
  "integrity" "sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ=="
  "resolved" "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"resize-observer-polyfill@^1.5.1":
  "integrity" "sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg=="
  "resolved" "https://registry.npmjs.org/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  "version" "1.5.1"

"resolve-cwd@^3.0.0":
  "integrity" "sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg=="
  "resolved" "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "resolve-from" "^5.0.0"

"resolve-from@^3.0.0":
  "integrity" "sha512-GnlH6vxLymXJNMBo7XP1fJIzBFbdYt49CuTwmB/6N53t+kMPRMFKz783LlQ4tv28XoQfMWinAJX6WCGf2IlaIw=="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-3.0.0.tgz"
  "version" "3.0.0"

"resolve-from@^4.0.0":
  "integrity" "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve-from@^5.0.0":
  "integrity" "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw=="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz"
  "version" "5.0.0"

"resolve-pathname@^3.0.0":
  "integrity" "sha512-C7rARubxI8bXFNB/hqcp/4iUeIXJhJZvFPFPiSPRnhU5UPxzMFIl+2E6yY6c4k9giDJAhtV+enfA+G89N6Csng=="
  "resolved" "https://registry.npmjs.org/resolve-pathname/-/resolve-pathname-3.0.0.tgz"
  "version" "3.0.0"

"resolve-url@^0.2.1":
  "integrity" "sha512-ZuF55hVUQaaczgOIwqWzkEcEidmlD/xl44x1UZnhOXcYuFN2S6+rcxpG+C1N3So0wvNI3DmJICUFfu2SxhBmvg=="
  "resolved" "https://registry.npmjs.org/resolve-url/-/resolve-url-0.2.1.tgz"
  "version" "0.2.1"

"resolve@^1.10.0", "resolve@^1.18.1":
  "integrity" "sha512-Sb+mjNHOULsBv818T40qSPeRiuWLyaGMa5ewydRLFimneixmVy2zdivRl+AF6jaYPC8ERxGDmFSiqui6SfPd+g=="
  "resolved" "https://registry.npmjs.org/resolve/-/resolve-1.22.2.tgz"
  "version" "1.22.2"
  dependencies:
    "is-core-module" "^2.11.0"
    "path-parse" "^1.0.7"
    "supports-preserve-symlinks-flag" "^1.0.0"

"restore-cursor@^3.1.0":
  "integrity" "sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA=="
  "resolved" "https://registry.npmjs.org/restore-cursor/-/restore-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "onetime" "^5.1.0"
    "signal-exit" "^3.0.2"

"ret@~0.1.10":
  "integrity" "sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg=="
  "resolved" "https://registry.npmjs.org/ret/-/ret-0.1.15.tgz"
  "version" "0.1.15"

"rfdc@^1.3.0":
  "integrity" "sha512-V2hovdzFbOi77/WajaSMXk2OLm+xNIeQdMMuB7icj7bk6zi2F8GGAxigcnDFpJHbNyNcgyJDiP+8nOrY5cZGrA=="
  "resolved" "https://registry.npmjs.org/rfdc/-/rfdc-1.3.0.tgz"
  "version" "1.3.0"

"rimraf@^3.0.0":
  "integrity" "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA=="
  "resolved" "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"ripemd160@^2.0.0", "ripemd160@^2.0.1":
  "integrity" "sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA=="
  "resolved" "https://registry.npmjs.org/ripemd160/-/ripemd160-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"

"rmc-align@~1.0.0":
  "integrity" "sha512-3gEa5/+hqqoEVoeQ25KoRc8DOsXIdSaVpaBq1zQFaV941LR3xvZIRTlxTDT/IagYwoGM1KZea/jd7cNMYP34Rg=="
  "resolved" "https://registry.npmjs.org/rmc-align/-/rmc-align-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "babel-runtime" "6.x"
    "dom-align" "1.x"
    "rc-util" "4.x"

"rmc-calendar@^1.0.0":
  "integrity" "sha512-xxQZaPFDnpHt4IFO8mukYrXSgC1W8LcNVp+EoX4iyeOJFimungOKB/iP5/cy+st8yXq8lUgk9TXsHNtM6Xo6ZA=="
  "resolved" "https://registry.npmjs.org/rmc-calendar/-/rmc-calendar-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "babel-runtime" "^6.26.0"
    "rc-animate" "^2.4.4"
    "rmc-date-picker" "^6.0.8"

"rmc-cascader@~5.0.0":
  "integrity" "sha512-PxDhMjWViDdG4SMZqoXtAthGwgDyYnyxxZEE17IDDYsiCHpWtOhoIL8nsI+/hZ212UT/XF2LpqCsOlMoJiYk+w=="
  "resolved" "https://registry.npmjs.org/rmc-cascader/-/rmc-cascader-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "array-tree-filter" "2.1.x"
    "babel-runtime" "6.x"
    "rmc-picker" "~5.0.0"

"rmc-date-picker@^6.0.8":
  "integrity" "sha512-/9+I6lm3EDEl6M7862V6++zFuxwsM0UEq8wSHbotYIPPmyB/65gx1cviblghOv2QfB0O9+U2w3qEJlRP/WsMrA=="
  "resolved" "https://registry.npmjs.org/rmc-date-picker/-/rmc-date-picker-6.0.10.tgz"
  "version" "6.0.10"
  dependencies:
    "babel-runtime" "6.x"
    "rmc-picker" "~5.0.0"

"rmc-dialog@^1.0.1", "rmc-dialog@^1.1.1":
  "integrity" "sha512-28aJqtPTX6v13Z/aU1WBy1AFIXkE74PxZXde7JvtEIy9hQDTjH8fqOi822BpzAbXCyNE7jF9iFomy3H2ClsDJA=="
  "resolved" "https://registry.npmjs.org/rmc-dialog/-/rmc-dialog-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "babel-runtime" "6.x"
    "rc-animate" "2.x"

"rmc-drawer@^0.4.11":
  "integrity" "sha512-YfB9XEJ8iM0MMuLWAK4313uOxSM8NAljC8Cqun1KamXutglYTuRviUuTLNSOzV8HHPp5kNpsVduvPCGLWXvThw=="
  "resolved" "https://registry.npmjs.org/rmc-drawer/-/rmc-drawer-0.4.11.tgz"
  "version" "0.4.11"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "^2.2.4"
    "prop-types" "^15.5.10"

"rmc-feedback@^2.0.0":
  "integrity" "sha512-5PWOGOW7VXks/l3JzlOU9NIxRpuaSS8d9zA3UULUCuTKnpwBHNvv1jSJzxgbbCQeYzROWUpgKI4za3X4C/mKmQ=="
  "resolved" "https://registry.npmjs.org/rmc-feedback/-/rmc-feedback-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "^2.2.5"

"rmc-input-number@^1.0.0":
  "integrity" "sha512-prPkEtoOVde77GnEnEaBeWjBobMOPgGqU5bd0gxfp1kt1pUN740mMpVAcH7uxpJjVfmw+kuGWtiz4S7CueagSg=="
  "resolved" "https://registry.npmjs.org/rmc-input-number/-/rmc-input-number-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "^2.2.0"
    "rmc-feedback" "^2.0.0"

"rmc-list-view@^0.11.0":
  "integrity" "sha512-eMOC5394tLNawcdEEhF7boMpQgpjJGDdL5lS+LblAWdBec7Q4EYkUdnrKNbt+O9k5RGM6nSLAGZK5oB4FN85Lg=="
  "resolved" "https://registry.npmjs.org/rmc-list-view/-/rmc-list-view-0.11.5.tgz"
  "version" "0.11.5"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "^2.2.5"
    "fbjs" "^0.8.3"
    "prop-types" "^15.5.8"
    "warning" "^3.0.0"
    "zscroller" "~0.4.0"

"rmc-notification@~1.0.0":
  "integrity" "sha512-9sPxjltFvtRLt2v312Hu7OXwk53pHkBYgINRDmnJ3A5NF1qtJeCCcdN0Xr0fzJ6sbQvtGju822tWHdzYA9u7Vw=="
  "resolved" "https://registry.npmjs.org/rmc-notification/-/rmc-notification-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "2.x"
    "prop-types" "^15.5.8"
    "rc-animate" "2.x"
    "rc-util" "^4.0.4"

"rmc-nuka-carousel@~3.0.0":
  "integrity" "sha512-w2EPTERMUUZqcUSKFuejjin7xsMlhrLrtS0A/igTXpFJGq3kemDKcRi7q3pSYDuZBHYBl5iV4UqsLLkjdFtrYA=="
  "resolved" "https://registry.npmjs.org/rmc-nuka-carousel/-/rmc-nuka-carousel-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "exenv" "^1.2.0"
    "raf" "^3.3.2"

"rmc-picker@~5.0.0":
  "integrity" "sha512-KZ70+WjcaZHnG5GyCxWCPFWAZ12s6NqyrbW73LeqH0WEqaTMMs0sOrk2f4mQAZ/CGT0XcFN6VZLw7Ozoxfn7LA=="
  "resolved" "https://registry.npmjs.org/rmc-picker/-/rmc-picker-5.0.10.tgz"
  "version" "5.0.10"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "^2.2.6"
    "rmc-dialog" "^1.1.1"
    "rmc-feedback" "^2.0.0"

"rmc-pull-to-refresh@~1.0.1":
  "integrity" "sha512-iYLsURiR7G/sKmRA6p2kq6ZXicn7Hyeo6VQFljssV1eMW+fzDgihhaz0kv5mza0f88vphGJvjOihT9E6+xGb6Q=="
  "resolved" "https://registry.npmjs.org/rmc-pull-to-refresh/-/rmc-pull-to-refresh-1.0.13.tgz"
  "version" "1.0.13"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "^2.2.5"

"rmc-steps@~1.0.0":
  "integrity" "sha512-8ijtwp4D1CYTtI2yerXJYqCv+GQbiBc9T12nrFngd/vM0y+58CnznGphTAueF6IWf7qbxBwcjTrcFgg7bP2YGA=="
  "resolved" "https://registry.npmjs.org/rmc-steps/-/rmc-steps-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "babel-runtime" "^6.23.0"
    "classnames" "^2.2.3"

"rmc-tabs@~1.2.0":
  "integrity" "sha512-wiJS9WSJi9JH9GQO+FqncX+zaHP31qHa/S8nDW9UXUx0qbCX294QcJEnvfB+WmsfUws7rXjs6sOQp5EDiObnHg=="
  "resolved" "https://registry.npmjs.org/rmc-tabs/-/rmc-tabs-1.2.29.tgz"
  "version" "1.2.29"
  dependencies:
    "babel-runtime" "6.x"
    "rc-gesture" "~0.0.18"

"rmc-tooltip@~1.0.0":
  "integrity" "sha512-fSDArf2BlMVrHExmBiqb2TkCRJHshvXFJQ/7tMraLellwaJLNiwrxtWpW329k3S+zTtoVG8UxFS1TjBGEsMzRg=="
  "resolved" "https://registry.npmjs.org/rmc-tooltip/-/rmc-tooltip-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "babel-runtime" "6.x"
    "rmc-trigger" "1.x"

"rmc-trigger@1.x":
  "integrity" "sha512-AccQniX7PX7Pm8hBhHEsnf3JU6CA61Xc7fAt2WbO+oXrGaI/jqN8C3COhhOXG54S5iTOjLS26j858zshwAxR9A=="
  "resolved" "https://registry.npmjs.org/rmc-trigger/-/rmc-trigger-1.0.12.tgz"
  "version" "1.0.12"
  dependencies:
    "babel-runtime" "6.x"
    "rc-animate" "2.x"
    "rc-util" "4.x"
    "rmc-align" "~1.0.0"

"rsvp@^4.8.4":
  "integrity" "sha512-nfMOlASu9OnRJo1mbEk2cz0D56a1MBNrJ7orjRZQG10XDyuvwksKbuXNp6qa+kbn839HwjwhBzhFmdsaEAfauA=="
  "resolved" "https://registry.npmjs.org/rsvp/-/rsvp-4.8.5.tgz"
  "version" "4.8.5"

"rxjs@^7.5.1":
  "integrity" "sha512-AA3TVj+0A2iuIoQkWEK/tqFjBq2j+6PO6Y0zJcvzLAFhEFIO3HL0vls9hWLncZbAAbK0mar7oZ4V079I/qPMxg=="
  "resolved" "https://registry.npmjs.org/rxjs/-/rxjs-7.8.1.tgz"
  "version" "7.8.1"
  dependencies:
    "tslib" "^2.1.0"

"safe-buffer@^5.0.1", "safe-buffer@^5.1.0", "safe-buffer@^5.1.1", "safe-buffer@^5.1.2", "safe-buffer@^5.2.0", "safe-buffer@~5.2.0":
  "integrity" "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-buffer@~5.1.0", "safe-buffer@~5.1.1":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-regex@^1.1.0":
  "integrity" "sha512-aJXcif4xnaNUzvUuC5gcb46oTS7zvg4jpMTnuqtrEPlR3vFr4pxtdTwaF1Qs3Enjn9HK+ZlwQui+a7z0SywIzg=="
  "resolved" "https://registry.npmjs.org/safe-regex/-/safe-regex-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "ret" "~0.1.10"

"safer-buffer@^2.1.0", "safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0":
  "integrity" "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="
  "resolved" "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sane@^4.0.3":
  "integrity" "sha512-hhbzAgTIX8O7SHfp2c8/kREfEn4qO/9q8C9beyY6+tvZ87EpoZ3i1RIEvp27YBswnNbY9mWd6paKVmKbAgLfZA=="
  "resolved" "https://registry.npmjs.org/sane/-/sane-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "@cnakazawa/watch" "^1.0.3"
    "anymatch" "^2.0.0"
    "capture-exit" "^2.0.0"
    "exec-sh" "^0.3.2"
    "execa" "^1.0.0"
    "fb-watchman" "^2.0.0"
    "micromatch" "^3.1.4"
    "minimist" "^1.1.1"
    "walker" "~1.0.5"

"saxes@^5.0.1":
  "integrity" "sha512-5LBh1Tls8c9xgGjw3QrMwETmTMVk0oFgvrFSvWx62llR2hcEInrKNZ2GZCCuuy2lvWrdl5jhbpeqc5hRYKFOcw=="
  "resolved" "https://registry.npmjs.org/saxes/-/saxes-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "xmlchars" "^2.2.0"

"scheduler@^0.19.1":
  "integrity" "sha512-n/zwRWRYSUj0/3g/otKDRPMh6qv2SYMWNq85IEa8iZyAv8od9zDYpGSnpBEjNgcMNq6Scbu5KfIPxNF72R/2EA=="
  "resolved" "https://registry.npmjs.org/scheduler/-/scheduler-0.19.1.tgz"
  "version" "0.19.1"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"

"scheduler@^0.20.2":
  "integrity" "sha512-2eWfGgAqqWFGqtdMmcL5zCMK1U8KlXv8SQFGglL3CEtd0aDVDWgeF/YoCmvln55m5zSk3J/20hTaSBeSObsQDQ=="
  "resolved" "https://registry.npmjs.org/scheduler/-/scheduler-0.20.2.tgz"
  "version" "0.20.2"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"

"schema-utils@^1.0.0":
  "integrity" "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "ajv" "^6.1.0"
    "ajv-errors" "^1.0.0"
    "ajv-keywords" "^3.1.0"

"schema-utils@^3.0.0":
  "integrity" "sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "@types/json-schema" "^7.0.8"
    "ajv" "^6.12.5"
    "ajv-keywords" "^3.5.2"

"scroll-into-view-if-needed@^2.2.25":
  "integrity" "sha512-dGCXy99wZQivjmjIqihaBQNjryrz5rueJY7eHfTdyWEiR4ttYpsajb14rn9s5d4DY4EcY6+4+U/maARBXJedkA=="
  "resolved" "https://registry.npmjs.org/scroll-into-view-if-needed/-/scroll-into-view-if-needed-2.2.31.tgz"
  "version" "2.2.31"
  dependencies:
    "compute-scroll-into-view" "^1.0.20"

"scroll-into-view-if-needed@^3.0.3":
  "integrity" "sha512-s+/F50jwTOUt+u5oEIAzum9MN2lUQNvWBe/zfEsVQcbaERjGkKLq1s+2wCHkahMLC8nMLbzMVKivx9JhunXaZg=="
  "resolved" "https://registry.npmjs.org/scroll-into-view-if-needed/-/scroll-into-view-if-needed-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "compute-scroll-into-view" "^2.0.4"

"seamless-immutable@^7.1.3":
  "integrity" "sha512-XiUO1QP4ki4E2PHegiGAlu6r82o5A+6tRh7IkGGTVg/h+UoeX4nFBeCGPOhb4CYjvkqsfm/TUtvOMYC1xmV30A=="
  "resolved" "https://registry.npmjs.org/seamless-immutable/-/seamless-immutable-7.1.4.tgz"
  "version" "7.1.4"

"semver-compare@^1.0.0":
  "integrity" "sha512-YM3/ITh2MJ5MtzaM429anh+x2jiLVjqILF4m4oyQB18W7Ggea7BfqdH/wGMK7dDiMghv/6WG7znWMwUDzJiXow=="
  "resolved" "https://registry.npmjs.org/semver-compare/-/semver-compare-1.0.0.tgz"
  "version" "1.0.0"

"semver@^5.5.0":
  "integrity" "sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-5.7.2.tgz"
  "version" "5.7.2"

"semver@^6.3.0":
  "integrity" "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz"
  "version" "6.3.1"

"semver@^6.3.1":
  "integrity" "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz"
  "version" "6.3.1"

"semver@^7.3.2", "semver@^7.3.5", "semver@^7.5.3":
  "integrity" "sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-7.5.4.tgz"
  "version" "7.5.4"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@2 || 3 || 4 || 5":
  "integrity" "sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-5.7.2.tgz"
  "version" "5.7.2"

"set-blocking@^2.0.0":
  "integrity" "sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw=="
  "resolved" "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"set-function-length@^1.2.1":
  "integrity" "sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg=="
  "resolved" "https://registry.npmmirror.com/set-function-length/-/set-function-length-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "define-data-property" "^1.1.4"
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"
    "get-intrinsic" "^1.2.4"
    "gopd" "^1.0.1"
    "has-property-descriptors" "^1.0.2"

"set-value@^2.0.0", "set-value@^2.0.1":
  "integrity" "sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw=="
  "resolved" "https://registry.npmjs.org/set-value/-/set-value-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-extendable" "^0.1.1"
    "is-plain-object" "^2.0.3"
    "split-string" "^3.0.1"

"setimmediate@^1.0.4", "setimmediate@^1.0.5":
  "integrity" "sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA=="
  "resolved" "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz"
  "version" "1.0.5"

"sha.js@^2.4.0", "sha.js@^2.4.8":
  "integrity" "sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ=="
  "resolved" "https://registry.npmjs.org/sha.js/-/sha.js-2.4.11.tgz"
  "version" "2.4.11"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"shallow-equal@^1.2.1":
  "integrity" "sha512-S4vJDjHHMBaiZuT9NPb616CSmLf618jawtv3sufLl6ivK8WocjAo58cXwbRV1cgqxH0Qbv+iUt6m05eqEa2IRA=="
  "resolved" "https://registry.npmjs.org/shallow-equal/-/shallow-equal-1.2.1.tgz"
  "version" "1.2.1"

"shallowequal@^1.0.1", "shallowequal@^1.1.0":
  "integrity" "sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ=="
  "resolved" "https://registry.npmjs.org/shallowequal/-/shallowequal-1.1.0.tgz"
  "version" "1.1.0"

"shebang-command@^1.2.0":
  "integrity" "sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg=="
  "resolved" "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "shebang-regex" "^1.0.0"

"shebang-command@^2.0.0":
  "integrity" "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA=="
  "resolved" "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^1.0.0":
  "integrity" "sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ=="
  "resolved" "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz"
  "version" "1.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="
  "resolved" "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"shellwords@^0.1.1":
  "integrity" "sha512-vFwSUfQvqybiICwZY5+DAWIPLKsWO31Q91JSKl3UYv+K5c2QRPzn0qzec6QPu1Qc9eHYItiP3NdJqNVqetYAww=="
  "resolved" "https://registry.npmjs.org/shellwords/-/shellwords-0.1.1.tgz"
  "version" "0.1.1"

"side-channel@^1.0.6":
  "integrity" "sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA=="
  "resolved" "https://registry.npmmirror.com/side-channel/-/side-channel-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "call-bind" "^1.0.7"
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.4"
    "object-inspect" "^1.13.1"

"signal-exit@^3.0.0", "signal-exit@^3.0.2":
  "integrity" "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ=="
  "resolved" "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz"
  "version" "3.0.7"

"sisteransi@^1.0.5":
  "integrity" "sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg=="
  "resolved" "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz"
  "version" "1.0.5"

"slash@^3.0.0":
  "integrity" "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q=="
  "resolved" "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz"
  "version" "3.0.0"

"slice-ansi@^3.0.0":
  "integrity" "sha512-pSyv7bSTC7ig9Dcgbw9AuRNUb5k5V6oDudjZoMBSr13qpLBG7tB+zgCkARjq7xIUgdz5P1Qe8u+rSGdouOOIyQ=="
  "resolved" "https://registry.npmjs.org/slice-ansi/-/slice-ansi-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "astral-regex" "^2.0.0"
    "is-fullwidth-code-point" "^3.0.0"

"slice-ansi@^4.0.0":
  "integrity" "sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ=="
  "resolved" "https://registry.npmjs.org/slice-ansi/-/slice-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "astral-regex" "^2.0.0"
    "is-fullwidth-code-point" "^3.0.0"

"snapdragon-node@^2.0.1":
  "integrity" "sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw=="
  "resolved" "https://registry.npmjs.org/snapdragon-node/-/snapdragon-node-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "define-property" "^1.0.0"
    "isobject" "^3.0.0"
    "snapdragon-util" "^3.0.1"

"snapdragon-util@^3.0.1":
  "integrity" "sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ=="
  "resolved" "https://registry.npmjs.org/snapdragon-util/-/snapdragon-util-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^3.2.0"

"snapdragon@^0.8.1":
  "integrity" "sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg=="
  "resolved" "https://registry.npmjs.org/snapdragon/-/snapdragon-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "base" "^0.11.1"
    "debug" "^2.2.0"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "map-cache" "^0.2.2"
    "source-map" "^0.5.6"
    "source-map-resolve" "^0.5.0"
    "use" "^3.1.0"

"sort-keys@^1.0.0":
  "integrity" "sha512-vzn8aSqKgytVik0iwdBEi+zevbTYZogewTUM6dtpmGwEcdzbub/TX4bCzRhebDCRC3QzXgJsLRKB2V/Oof7HXg=="
  "resolved" "https://registry.npmjs.org/sort-keys/-/sort-keys-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "is-plain-obj" "^1.0.0"

"source-map-resolve@^0.5.0":
  "integrity" "sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw=="
  "resolved" "https://registry.npmjs.org/source-map-resolve/-/source-map-resolve-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "atob" "^2.1.2"
    "decode-uri-component" "^0.2.0"
    "resolve-url" "^0.2.1"
    "source-map-url" "^0.4.0"
    "urix" "^0.1.0"

"source-map-support@^0.5.6", "source-map-support@~0.5.20":
  "integrity" "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w=="
  "resolved" "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz"
  "version" "0.5.21"
  dependencies:
    "buffer-from" "^1.0.0"
    "source-map" "^0.6.0"

"source-map-url@^0.4.0":
  "integrity" "sha512-cPiFOTLUKvJFIg4SKVScy4ilPPW6rFgMgfuZJPNoDuMs3nC1HbMUycBoJw77xFIp6z1UJQJOfx6C9GMH80DiTw=="
  "resolved" "https://registry.npmjs.org/source-map-url/-/source-map-url-0.4.1.tgz"
  "version" "0.4.1"

"source-map@^0.5.6":
  "integrity" "sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.6.0", "source-map@^0.6.1", "source-map@~0.6.1":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.7.3":
  "integrity" "sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.7.4.tgz"
  "version" "0.7.4"

"source-map@0.5.6":
  "integrity" "sha512-MjZkVp0NHr5+TPihLcadqnlVoGIoWo4IBHptutGh9wI3ttUYvCG26HkSuDi+K6lsZ25syXJXcctwgyVCt//xqA=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.5.6.tgz"
  "version" "0.5.6"

"sourcemapped-stacktrace@^1.1.6":
  "integrity" "sha512-O0pcWjJqzQFVsisPlPXuNawJHHg9N9UgpJ/aDmvi9+vnS3x1C0NhwkVFzzZ1VN0Xo+bekyweoqYvBw5ZBKiNnQ=="
  "resolved" "https://registry.npmjs.org/sourcemapped-stacktrace/-/sourcemapped-stacktrace-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "source-map" "0.5.6"

"spdx-correct@^3.0.0":
  "integrity" "sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA=="
  "resolved" "https://registry.npmjs.org/spdx-correct/-/spdx-correct-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "spdx-expression-parse" "^3.0.0"
    "spdx-license-ids" "^3.0.0"

"spdx-exceptions@^2.1.0":
  "integrity" "sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A=="
  "resolved" "https://registry.npmjs.org/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz"
  "version" "2.3.0"

"spdx-expression-parse@^3.0.0":
  "integrity" "sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q=="
  "resolved" "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "spdx-exceptions" "^2.1.0"
    "spdx-license-ids" "^3.0.0"

"spdx-license-ids@^3.0.0":
  "integrity" "sha512-XkD+zwiqXHikFZm4AX/7JSCXA98U5Db4AFd5XUg/+9UNtnH75+Z9KxtpYiJZx36mUDVOwH83pl7yvCer6ewM3w=="
  "resolved" "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-3.0.13.tgz"
  "version" "3.0.13"

"split-on-first@^1.0.0":
  "integrity" "sha512-43ZssAJaMusuKWL8sKUBQXHWOpq8d6CfN/u1p4gUzfJkM05C8rxTmYrkIPTXapZpORA6LkkzcUulJ8FqA7Uudw=="
  "resolved" "https://registry.npmjs.org/split-on-first/-/split-on-first-1.1.0.tgz"
  "version" "1.1.0"

"split-string@^3.0.1", "split-string@^3.0.2":
  "integrity" "sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw=="
  "resolved" "https://registry.npmjs.org/split-string/-/split-string-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "extend-shallow" "^3.0.0"

"sprintf-js@~1.0.2":
  "integrity" "sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g=="
  "resolved" "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"ssf@~0.11.2":
  "integrity" "sha512-+idbmIXoYET47hH+d7dfm2epdOMUDjqcB4648sTZ+t2JwoyBFL/insLfB/racrDmsKB3diwsDA696pZMieAC5g=="
  "resolved" "https://registry.npmmirror.com/ssf/-/ssf-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "frac" "~1.1.2"

"stack-utils@^2.0.2":
  "integrity" "sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ=="
  "resolved" "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "escape-string-regexp" "^2.0.0"

"stackframe@^0.3.1":
  "integrity" "sha512-XmoiF4T5nuWEp2x2w92WdGjdHGY/cZa6LIbRsDRQR/Xlk4uW0PAUlH1zJYVffocwKpCdwyuypIp25xsSXEtZHw=="
  "resolved" "https://registry.npmjs.org/stackframe/-/stackframe-0.3.1.tgz"
  "version" "0.3.1"

"stackframe@^1.3.4":
  "integrity" "sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw=="
  "resolved" "https://registry.npmjs.org/stackframe/-/stackframe-1.3.4.tgz"
  "version" "1.3.4"

"static-extend@^0.1.1":
  "integrity" "sha512-72E9+uLc27Mt718pMHt9VMNiAL4LMsmDbBva8mxWUCkT07fSzEGMYUCk0XWY6lp0j6RBAG4cJ3mWuZv2OE3s0g=="
  "resolved" "https://registry.npmjs.org/static-extend/-/static-extend-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "define-property" "^0.2.5"
    "object-copy" "^0.1.0"

"stream-browserify@^2.0.1":
  "integrity" "sha512-nX6hmklHs/gr2FuxYDltq8fJA1GDlxKQCz8O/IM4atRqBH8OORmBNgfvW5gG10GT/qQ9u0CzIvr2X5Pkt6ntqg=="
  "resolved" "https://registry.npmjs.org/stream-browserify/-/stream-browserify-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "inherits" "~2.0.1"
    "readable-stream" "^2.0.2"

"stream-http@^2.7.2":
  "integrity" "sha512-+TSkfINHDo4J+ZobQLWiMouQYB+UVYFttRA94FpEzzJ7ZdqcL4uUUQ7WkdkI4DSozGmgBUE/a47L+38PenXhUw=="
  "resolved" "https://registry.npmjs.org/stream-http/-/stream-http-2.8.3.tgz"
  "version" "2.8.3"
  dependencies:
    "builtin-status-codes" "^3.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.3.6"
    "to-arraybuffer" "^1.0.0"
    "xtend" "^4.0.0"

"strict-uri-encode@^1.0.0":
  "integrity" "sha512-R3f198pcvnB+5IpnBlRkphuE9n46WyVl8I39W/ZUTZLz4nqSP/oLYUrcnJrw462Ds8he4YKMov2efsTIw1BDGQ=="
  "resolved" "https://registry.npmjs.org/strict-uri-encode/-/strict-uri-encode-1.1.0.tgz"
  "version" "1.1.0"

"strict-uri-encode@^2.0.0":
  "integrity" "sha512-QwiXZgpRcKkhTj2Scnn++4PKtWsH0kpzZ62L2R6c/LUVYv7hVnZqcg2+sMuT6R7Jusu1vviK/MFsu6kNJfWlEQ=="
  "resolved" "https://registry.npmjs.org/strict-uri-encode/-/strict-uri-encode-2.0.0.tgz"
  "version" "2.0.0"

"string_decoder@^1.0.0", "string_decoder@^1.1.1":
  "integrity" "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA=="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "safe-buffer" "~5.2.0"

"string_decoder@~1.1.1":
  "integrity" "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg=="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-argv@0.3.1":
  "integrity" "sha512-a1uQGz7IyVy9YwhqjZIZu1c8JO8dNIe20xBmSS6qu9kv++k3JGzCVmprbNN5Kn+BgzD5E7YYwg1CcjuJMRNsvg=="
  "resolved" "https://registry.npmjs.org/string-argv/-/string-argv-0.3.1.tgz"
  "version" "0.3.1"

"string-convert@^0.2.0":
  "integrity" "sha512-u/1tdPl4yQnPBjnVrmdLo9gtuLvELKsAoRapekWggdiQNvvvum+jYF329d84NAa660KQw7pB2n36KrIKVoXa3A=="
  "resolved" "https://registry.npmjs.org/string-convert/-/string-convert-0.2.1.tgz"
  "version" "0.2.1"

"string-length@^4.0.1":
  "integrity" "sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ=="
  "resolved" "https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "char-regex" "^1.0.2"
    "strip-ansi" "^6.0.0"

"string-width@^4.1.0", "string-width@^4.2.0":
  "integrity" "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"stringify-object@^3.3.0":
  "integrity" "sha512-rHqiFh1elqCQ9WPLIC8I0Q/g/wj5J1eMkyoiD6eoQApWHP0FtlK7rqnhmabL5VUY9JQCcqwwvlOaSuutekgyrw=="
  "resolved" "https://registry.npmjs.org/stringify-object/-/stringify-object-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "get-own-enumerable-property-symbols" "^3.0.0"
    "is-obj" "^1.0.1"
    "is-regexp" "^1.0.0"

"strip-ansi@^6.0.0", "strip-ansi@^6.0.1":
  "integrity" "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-bom@^4.0.0":
  "integrity" "sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w=="
  "resolved" "https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz"
  "version" "4.0.0"

"strip-eof@^1.0.0":
  "integrity" "sha512-7FCwGGmx8mD5xQd3RPUvnSpUXHM3BWuzjtpD4TXsfcZ9EL4azvVVUscFYwD9nx8Kh+uCBC00XBtAykoMHwTh8Q=="
  "resolved" "https://registry.npmjs.org/strip-eof/-/strip-eof-1.0.0.tgz"
  "version" "1.0.0"

"strip-final-newline@^2.0.0":
  "integrity" "sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA=="
  "resolved" "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  "version" "2.0.0"

"strip-indent@^2.0.0":
  "integrity" "sha512-RsSNPLpq6YUL7QYy44RnPVTn/lcVZtb48Uof3X5JLbF4zD/Gs7ZFDv2HWol+leoQN2mT86LAzSshGfkTlSOpsA=="
  "resolved" "https://registry.npmjs.org/strip-indent/-/strip-indent-2.0.0.tgz"
  "version" "2.0.0"

"stylis@^4.0.13":
  "integrity" "sha512-E87pIogpwUsUwXw7dNyU4QDjdgVMy52m+XEOPEKUn161cCzWjjhPSQhByfd1CcNvrOLnXQ6OnnZDwnJrz/Z4YQ=="
  "resolved" "https://registry.npmjs.org/stylis/-/stylis-4.3.0.tgz"
  "version" "4.3.0"

"supports-color@^5.3.0":
  "integrity" "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^6.1.0":
  "integrity" "sha512-qe1jfm1Mg7Nq/NSh6XE24gPXROEVsWHxC1LIx//XNlD9iw7YZQGjZNjYN7xGaEG6iKdA8EtNFW6R0gjnVXp+wQ=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^7.0.0", "supports-color@^7.1.0":
  "integrity" "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"supports-hyperlinks@^2.0.0":
  "integrity" "sha512-RpsAZlpWcDwOPQA22aCH4J0t7L8JmAvsCxfOSEwm7cQs3LshN36QaTkwd70DnBOXDWGssw2eUoc8CaRWT0XunA=="
  "resolved" "https://registry.npmjs.org/supports-hyperlinks/-/supports-hyperlinks-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "has-flag" "^4.0.0"
    "supports-color" "^7.0.0"

"supports-preserve-symlinks-flag@^1.0.0":
  "integrity" "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="
  "resolved" "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  "version" "1.0.0"

"swr@^1.2.0":
  "integrity" "sha512-dkghQrOl2ORX9HYrMDtPa7LTVHJjCTeZoB1dqTbnnEDlSvN8JEKpYIYurDfvbQFUUS8Cg8PceFVZNkW0KNNYPw=="
  "resolved" "https://registry.npmjs.org/swr/-/swr-1.3.0.tgz"
  "version" "1.3.0"

"swr@^2.0.0":
  "integrity" "sha512-AjqHOv2lAhkuUdIiBu9xbuettzAzWXmCEcLONNKJRba87WAefz8Ca9d6ds/SzrPc235n1IxWYdhJ2zF3MNUaoQ=="
  "resolved" "https://registry.npmjs.org/swr/-/swr-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "use-sync-external-store" "^1.2.0"

"symbol-observable@^1.0.3":
  "integrity" "sha512-e900nM8RRtGhlV36KGEU9k65K3mPb1WV70OdjfxlG2EAuM1noi/E/BaW/uMhL7bPEssK8QV57vN3esixjUvcXQ=="
  "resolved" "https://registry.npmjs.org/symbol-observable/-/symbol-observable-1.2.0.tgz"
  "version" "1.2.0"

"symbol-tree@^3.2.4":
  "integrity" "sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw=="
  "resolved" "https://registry.npmjs.org/symbol-tree/-/symbol-tree-3.2.4.tgz"
  "version" "3.2.4"

"terminal-link@^2.0.0":
  "integrity" "sha512-un0FmiRUQNr5PJqy9kP7c40F5BOfpGlYTrxonDChEZB7pzZxRNp/bt+ymiy9/npwXya9KH99nJ/GXFIiUkYGFQ=="
  "resolved" "https://registry.npmjs.org/terminal-link/-/terminal-link-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "ansi-escapes" "^4.2.1"
    "supports-hyperlinks" "^2.0.0"

"terser@5.14.2":
  "integrity" "sha512-oL0rGeM/WFQCUd0y2QrWxYnq7tfSuKBiqTjRPWrRgB46WD/kiwHwF8T23z78H6Q6kGCuuHcPB+KULHRdxvVGQA=="
  "resolved" "https://registry.npmjs.org/terser/-/terser-5.14.2.tgz"
  "version" "5.14.2"
  dependencies:
    "@jridgewell/source-map" "^0.3.2"
    "acorn" "^8.5.0"
    "commander" "^2.20.0"
    "source-map-support" "~0.5.20"

"test-exclude@^6.0.0":
  "integrity" "sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w=="
  "resolved" "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    "glob" "^7.1.4"
    "minimatch" "^3.0.4"

"throat@^5.0.0":
  "integrity" "sha512-fcwX4mndzpLQKBS1DVYhGAcYaYt7vsHNIvQV+WXMvnow5cgjPphq5CaayLaGsjRdSCKZFNGt7/GYAuXaNOiYCA=="
  "resolved" "https://registry.npmjs.org/throat/-/throat-5.0.0.tgz"
  "version" "5.0.0"

"throttle-debounce@^5.0.0":
  "integrity" "sha512-2iQTSgkkc1Zyk0MeVrt/3BvuOXYPl/R8Z0U2xxo9rjwNciaHDG3R+Lm6dh4EeUci49DanvBnuqI6jshoQQRGEg=="
  "resolved" "https://registry.npmjs.org/throttle-debounce/-/throttle-debounce-5.0.0.tgz"
  "version" "5.0.0"

"through@^2.3.8":
  "integrity" "sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg=="
  "resolved" "https://registry.npmjs.org/through/-/through-2.3.8.tgz"
  "version" "2.3.8"

"timers-browserify@^2.0.4":
  "integrity" "sha512-9phl76Cqm6FhSX9Xe1ZUAMLtm1BLkKj2Qd5ApyWkXzsMRaA7dgr81kf4wJmQf/hAvg8EEyJxDo3du/0KlhPiKQ=="
  "resolved" "https://registry.npmjs.org/timers-browserify/-/timers-browserify-2.0.12.tgz"
  "version" "2.0.12"
  dependencies:
    "setimmediate" "^1.0.4"

"tiny-invariant@^1.0.2":
  "integrity" "sha512-AD5ih2NlSssTCwsMznbvwMZpJ1cbhkGd2uueNxzv2jDlEeZdU04JQfRnggJQ8DrcVBGjAsCKwFBbDlVNtEMlzw=="
  "resolved" "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.3.1.tgz"
  "version" "1.3.1"

"tiny-warning@^1.0.0", "tiny-warning@^1.0.3":
  "integrity" "sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA=="
  "resolved" "https://registry.npmjs.org/tiny-warning/-/tiny-warning-1.0.3.tgz"
  "version" "1.0.3"

"tinycolor2@^1.4.2":
  "integrity" "sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw=="
  "resolved" "https://registry.npmjs.org/tinycolor2/-/tinycolor2-1.6.0.tgz"
  "version" "1.6.0"

"tmpl@1.0.5":
  "integrity" "sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw=="
  "resolved" "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz"
  "version" "1.0.5"

"to-arraybuffer@^1.0.0":
  "integrity" "sha512-okFlQcoGTi4LQBG/PgSYblw9VOyptsz2KJZqc6qtgGdes8VktzUQkj4BI2blit072iS8VODNcMA+tvnS9dnuMA=="
  "resolved" "https://registry.npmjs.org/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz"
  "version" "1.0.1"

"to-fast-properties@^2.0.0":
  "integrity" "sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog=="
  "resolved" "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  "version" "2.0.0"

"to-object-path@^0.3.0":
  "integrity" "sha512-9mWHdnGRuh3onocaHzukyvCZhzvr6tiflAy/JRFXcJX0TjgfWA9pk9t8CMbzmBE4Jfw58pXbkngtBtqYxzNEyg=="
  "resolved" "https://registry.npmjs.org/to-object-path/-/to-object-path-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "kind-of" "^3.0.2"

"to-regex-range@^2.1.0":
  "integrity" "sha512-ZZWNfCjUokXXDGXFpZehJIkZqq91BcULFq/Pi7M5i4JnxXdhMKAK682z8bCW3o8Hj1wuuzoKcW3DfVzaP6VuNg=="
  "resolved" "https://registry.npmjs.org/to-regex-range/-/to-regex-range-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"

"to-regex-range@^5.0.1":
  "integrity" "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="
  "resolved" "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"to-regex@^3.0.1", "to-regex@^3.0.2":
  "integrity" "sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw=="
  "resolved" "https://registry.npmjs.org/to-regex/-/to-regex-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "regex-not" "^1.0.2"
    "safe-regex" "^1.1.0"

"toggle-selection@^1.0.6":
  "integrity" "sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ=="
  "resolved" "https://registry.npmjs.org/toggle-selection/-/toggle-selection-1.0.6.tgz"
  "version" "1.0.6"

"tough-cookie@^4.0.0":
  "integrity" "sha512-aX/y5pVRkfRnfmuX+OdbSdXvPe6ieKX/G2s7e98f4poJHnqH3281gDPm/metm6E/WRamfx7WC4HUqkWHfQHprw=="
  "resolved" "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.3.tgz"
  "version" "4.1.3"
  dependencies:
    "psl" "^1.1.33"
    "punycode" "^2.1.1"
    "universalify" "^0.2.0"
    "url-parse" "^1.5.3"

"tr46@^2.1.0":
  "integrity" "sha512-15Ih7phfcdP5YxqiB+iDtLoaTz4Nd35+IiAv0kQ5FNKHzXgdWqPoTIqEDDJmXceQt4JZk6lVPT8lnDlPpGDppw=="
  "resolved" "https://registry.npmjs.org/tr46/-/tr46-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "punycode" "^2.1.1"

"tslib@^2.1.0":
  "integrity" "sha512-t0hLfiEKfMUoqhG+U1oid7Pva4bbDPHYfJNiB7BiIjRkj1pyC++4N3huJfqY6aRH6VTB0rvtzQwjM4K6qpfOig=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-2.6.1.tgz"
  "version" "2.6.1"

"tty-browserify@0.0.0":
  "integrity" "sha512-JVa5ijo+j/sOoHGjw0sxw734b1LhBkQ3bvUGNdxnVXDCX81Yx7TFgnZygxrIIWn23hbfTaMYLwRmAxFyDuFmIw=="
  "resolved" "https://registry.npmjs.org/tty-browserify/-/tty-browserify-0.0.0.tgz"
  "version" "0.0.0"

"tween-functions@^1.0.1":
  "integrity" "sha512-PZBtLYcCLtEcjL14Fzb1gSxPBeL7nWvGhO5ZFPGqziCcr8uvHp0NDmdjBchp6KHL+tExcg0m3NISmKxhU394dA=="
  "resolved" "https://registry.npmjs.org/tween-functions/-/tween-functions-1.2.0.tgz"
  "version" "1.2.0"

"type-detect@4.0.8":
  "integrity" "sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g=="
  "resolved" "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz"
  "version" "4.0.8"

"type-fest@^0.21.3":
  "integrity" "sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz"
  "version" "0.21.3"

"type-fest@^0.6.0":
  "integrity" "sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-0.6.0.tgz"
  "version" "0.6.0"

"type-fest@^0.8.1":
  "integrity" "sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-0.8.1.tgz"
  "version" "0.8.1"

"typedarray-to-buffer@^3.1.5":
  "integrity" "sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q=="
  "resolved" "https://registry.npmjs.org/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz"
  "version" "3.1.5"
  dependencies:
    "is-typedarray" "^1.0.0"

"typescript@^4.1.2":
  "integrity" "sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g=="
  "resolved" "https://registry.npmjs.org/typescript/-/typescript-4.9.5.tgz"
  "version" "4.9.5"

"ua-parser-js@^0.7.30":
  "integrity" "sha512-veRf7dawaj9xaWEu9HoTVn5Pggtc/qj+kqTOFvNiN1l0YdxwC1kvel57UCjThjGa3BHBihE8/UJAHI+uQHmd/g=="
  "resolved" "https://registry.npmjs.org/ua-parser-js/-/ua-parser-js-0.7.35.tgz"
  "version" "0.7.35"

"umi-request@^1.2.14":
  "integrity" "sha512-OknwtQZddZHi0Ggi+Vr/olJ7HNMx4AzlywyK0W3NZBT7B0stjeZ9lcztA85dBgdAj3KVk8uPJPZSnGaDjELhrA=="
  "resolved" "https://registry.npmjs.org/umi-request/-/umi-request-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "isomorphic-fetch" "^2.2.1"
    "qs" "^6.9.1"

"umi@^3.5.36", "umi@3.x":
  "integrity" "sha512-3bhnxwEAo7Ejg8kxVjocCuZuoFFR21fC06qzGjNfr4GX0E3S878i6Mu+PAuYioSXNxs4kVreyXu4x/q88a28jQ=="
  "resolved" "https://registry.npmjs.org/umi/-/umi-3.5.36.tgz"
  "version" "3.5.36"
  dependencies:
    "@umijs/bundler-webpack" "3.5.36"
    "@umijs/core" "3.5.36"
    "@umijs/deps" "3.5.36"
    "@umijs/preset-built-in" "3.5.36"
    "@umijs/runtime" "3.5.36"
    "@umijs/types" "3.5.36"
    "@umijs/utils" "3.5.36"
    "react" "16.x"
    "react-dom" "16.x"
    "v8-compile-cache" "2.3.0"

"union-value@^1.0.0":
  "integrity" "sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg=="
  "resolved" "https://registry.npmjs.org/union-value/-/union-value-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "arr-union" "^3.1.0"
    "get-value" "^2.0.6"
    "is-extendable" "^0.1.1"
    "set-value" "^2.0.1"

"uniq@^1.0.1":
  "integrity" "sha512-Gw+zz50YNKPDKXs+9d+aKAjVwpjNwqzvNpLigIruT4HA9lMZNdMqs9x07kKHB/L9WRzqp4+DlTU5s4wG2esdoA=="
  "resolved" "https://registry.npmjs.org/uniq/-/uniq-1.0.1.tgz"
  "version" "1.0.1"

"universalify@^0.2.0":
  "integrity" "sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg=="
  "resolved" "https://registry.npmjs.org/universalify/-/universalify-0.2.0.tgz"
  "version" "0.2.0"

"unset-value@^1.0.0":
  "integrity" "sha512-PcA2tsuGSF9cnySLHTLSh2qrQiJ70mn+r+Glzxv2TWZblxsxCC52BDlZoPCsz7STd9pN7EZetkWZBAvk4cgZdQ=="
  "resolved" "https://registry.npmjs.org/unset-value/-/unset-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-value" "^0.3.1"
    "isobject" "^3.0.0"

"unstated-next@^1.1.0":
  "integrity" "sha512-AAn47ZncPvgBGOvMcn8tSRxsrqwf2VdAPxLASTuLJvZt4rhKfDvUkmYZLGfclImSfTVMv7tF4ynaVxin0JjDCA=="
  "resolved" "https://registry.npmjs.org/unstated-next/-/unstated-next-1.1.0.tgz"
  "version" "1.1.0"

"update-browserslist-db@^1.0.11":
  "integrity" "sha512-dCwEFf0/oT85M1fHBg4F0jtLwJrutGoHSQXCh7u4o2t1drG+c0a9Flnqww6XUKSfQMPpJBRjU8d4RXB09qtvaA=="
  "resolved" "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.11.tgz"
  "version" "1.0.11"
  dependencies:
    "escalade" "^3.1.1"
    "picocolors" "^1.0.0"

"uri-js@^4.2.2":
  "integrity" "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg=="
  "resolved" "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"urix@^0.1.0":
  "integrity" "sha512-Am1ousAhSLBeB9cG/7k7r2R0zj50uDRlZHPGbazid5s9rlF1F/QKYObEKSIunSjIOkJZqwRRLpvewjEkM7pSqg=="
  "resolved" "https://registry.npmjs.org/urix/-/urix-0.1.0.tgz"
  "version" "0.1.0"

"url-parse@^1.5.3":
  "integrity" "sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ=="
  "resolved" "https://registry.npmjs.org/url-parse/-/url-parse-1.5.10.tgz"
  "version" "1.5.10"
  dependencies:
    "querystringify" "^2.1.1"
    "requires-port" "^1.0.0"

"url@^0.11.0":
  "integrity" "sha512-rWS3H04/+mzzJkv0eZ7vEDGiQbgquI1fGfOad6zKvgYQi1SzMmhl7c/DdRGxhaWrVH6z0qWITo8rpnxK/RfEhA=="
  "resolved" "https://registry.npmjs.org/url/-/url-0.11.1.tgz"
  "version" "0.11.1"
  dependencies:
    "punycode" "^1.4.1"
    "qs" "^6.11.0"

"use-json-comparison@^1.0.3", "use-json-comparison@^1.0.5":
  "integrity" "sha512-xPadt5yMRbEmVfOSGFSMqjjICrq7nLbfSH3rYIXsrtcuFX7PmbYDN/ku8ObBn3v8o/yZelO1OxUS5+5TI3+fUw=="
  "resolved" "https://registry.npmjs.org/use-json-comparison/-/use-json-comparison-1.0.6.tgz"
  "version" "1.0.6"

"use-media-antd-query@^1.1.0":
  "integrity" "sha512-B6kKZwNV4R+l4Rl11sWO7HqOay9alzs1Vp1b4YJqjz33YxbltBCZtt/yxXxkXN9rc1S7OeEL/GbwC30Wmqhw6Q=="
  "resolved" "https://registry.npmjs.org/use-media-antd-query/-/use-media-antd-query-1.1.0.tgz"
  "version" "1.1.0"

"use-subscription@1.5.1":
  "integrity" "sha512-Xv2a1P/yReAjAbhylMfFplFKj9GssgTwN7RlcTxBujFQcloStWNDQdc4g4NRWH9xS4i/FDk04vQBptAXoF3VcA=="
  "resolved" "https://registry.npmjs.org/use-subscription/-/use-subscription-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "object-assign" "^4.1.1"

"use-sync-external-store@^1.2.0":
  "integrity" "sha512-eEgnFxGQ1Ife9bzYs6VLi8/4X6CObHMw9Qr9tPY43iKwsPw8xE8+EFsf/2cFZ5S3esXgpWgtSCtLNS41F+sKPA=="
  "resolved" "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.2.0.tgz"
  "version" "1.2.0"

"use@^3.1.0":
  "integrity" "sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ=="
  "resolved" "https://registry.npmjs.org/use/-/use-3.1.1.tgz"
  "version" "3.1.1"

"util-deprecate@^1.0.1", "util-deprecate@^1.0.2", "util-deprecate@~1.0.1":
  "integrity" "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="
  "resolved" "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"util@^0.11.0":
  "integrity" "sha512-HShAsny+zS2TZfaXxD9tYj4HQGlBezXZMZuM/S5PKLLoZkShZiGk9o5CzukI1LVHZvjdvZ2Sj1aW/Ndn2NB/HQ=="
  "resolved" "https://registry.npmjs.org/util/-/util-0.11.1.tgz"
  "version" "0.11.1"
  dependencies:
    "inherits" "2.0.3"

"util@0.10.3":
  "integrity" "sha512-5KiHfsmkqacuKjkRkdV7SsfDJ2EGiPsK92s2MhNSY0craxjTdKTtqKsJaCWp4LW33ZZ0OPUv1WO/TFvNQRiQxQ=="
  "resolved" "https://registry.npmjs.org/util/-/util-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "inherits" "2.0.1"

"uuid@^8.3.0":
  "integrity" "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg=="
  "resolved" "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz"
  "version" "8.3.2"

"v8-compile-cache@2.3.0":
  "integrity" "sha512-l8lCEmLcLYZh4nbunNZvQCJc5pv7+RCwa8q/LdUx8u7lsWvPDKmpodJAJNwkAhJC//dFY48KuIEmjtd4RViDrA=="
  "resolved" "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz"
  "version" "2.3.0"

"v8-to-istanbul@^7.0.0":
  "integrity" "sha512-TxNb7YEUwkLXCQYeudi6lgQ/SZrzNO4kMdlqVxaZPUIUjCv6iSSypUQX70kNBSERpQ8fk48+d61FXk+tgqcWow=="
  "resolved" "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-7.1.2.tgz"
  "version" "7.1.2"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.1"
    "convert-source-map" "^1.6.0"
    "source-map" "^0.7.3"

"validate-npm-package-license@^3.0.1":
  "integrity" "sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew=="
  "resolved" "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"value-equal@^1.0.1":
  "integrity" "sha512-NOJ6JZCAWr0zlxZt+xqCHNTEKOsrks2HQd4MqhP1qy4z1SkbEP467eNx6TgDKXMvUOb+OENfJCZwM+16n7fRfw=="
  "resolved" "https://registry.npmjs.org/value-equal/-/value-equal-1.0.1.tgz"
  "version" "1.0.1"

"vm-browserify@^1.0.1":
  "integrity" "sha512-2ham8XPWTONajOR0ohOKOHXkm3+gaBmGut3SRuu75xLd/RRaY6vqgh8NBYYk7+RW3u5AtzPQZG8F10LHkl0lAQ=="
  "resolved" "https://registry.npmjs.org/vm-browserify/-/vm-browserify-1.1.2.tgz"
  "version" "1.1.2"

"w3c-hr-time@^1.0.2":
  "integrity" "sha512-z8P5DvDNjKDoFIHK7q8r8lackT6l+jo/Ye3HOle7l9nICP9lf1Ci25fy9vHd0JOWewkIFzXIEig3TdKT7JQ5fQ=="
  "resolved" "https://registry.npmjs.org/w3c-hr-time/-/w3c-hr-time-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "browser-process-hrtime" "^1.0.0"

"w3c-xmlserializer@^2.0.0":
  "integrity" "sha512-4tzD0mF8iSiMiNs30BiLO3EpfGLZUT2MSX/G+o7ZywDzliWQ3OPtTZ0PTC3B3ca1UAf4cJMHB+2Bf56EriJuRA=="
  "resolved" "https://registry.npmjs.org/w3c-xmlserializer/-/w3c-xmlserializer-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "xml-name-validator" "^3.0.0"

"walker@^1.0.7", "walker@~1.0.5":
  "integrity" "sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ=="
  "resolved" "https://registry.npmjs.org/walker/-/walker-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "makeerror" "1.0.12"

"warning@^3.0.0":
  "integrity" "sha512-jMBt6pUrKn5I+OGgtQ4YZLdhIeJmObddh6CsibPxyQ5yPZm1XExSyzC1LCNX7BzhxWgiHmizBWJTHJIjMjTQYQ=="
  "resolved" "https://registry.npmjs.org/warning/-/warning-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "loose-envify" "^1.0.0"

"warning@^4.0.1":
  "integrity" "sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w=="
  "resolved" "https://registry.npmjs.org/warning/-/warning-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "loose-envify" "^1.0.0"

"warning@^4.0.3":
  "integrity" "sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w=="
  "resolved" "https://registry.npmjs.org/warning/-/warning-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "loose-envify" "^1.0.0"

"webidl-conversions@^5.0.0":
  "integrity" "sha512-VlZwKPCkYKxQgeSbH5EyngOmRp7Ww7I9rQLERETtf5ofd9pGeswWiOtogpEO850jziPRarreGxn5QIiTqpb2wA=="
  "resolved" "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-5.0.0.tgz"
  "version" "5.0.0"

"webidl-conversions@^6.1.0":
  "integrity" "sha512-qBIvFLGiBpLjfwmYAaHPXsn+ho5xZnGvyGvsarywGNc8VyQJUMHJ8OBKGGrPER0okBeMDaan4mNBlgBROxuI8w=="
  "resolved" "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-6.1.0.tgz"
  "version" "6.1.0"

"webpack-chain@6.5.1":
  "integrity" "sha512-7doO/SRtLu8q5WM0s7vPKPWX580qhi0/yBHkOxNkv50f6qB76Zy9o2wRTrrPULqYTvQlVHuvbA8v+G5ayuUDsA=="
  "resolved" "https://registry.npmjs.org/webpack-chain/-/webpack-chain-6.5.1.tgz"
  "version" "6.5.1"
  dependencies:
    "deepmerge" "^1.5.2"
    "javascript-stringify" "^2.0.1"

"whatwg-encoding@^1.0.5":
  "integrity" "sha512-b5lim54JOPN9HtzvK9HFXvBma/rnfFeqsic0hSpjtDbVxR3dJKLc+KB4V6GgiGOvl7CY/KNh8rxSo9DKQrnUEw=="
  "resolved" "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "iconv-lite" "0.4.24"

"whatwg-fetch@^3.5.0", "whatwg-fetch@>=0.10.0":
  "integrity" "sha512-c4ghIvG6th0eudYwKZY5keb81wtFz9/WeAHAoy8+r18kcWlitUIrmGFQ2rWEl4UCKUilD3zCLHOIPheHx5ypRQ=="
  "resolved" "https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.6.17.tgz"
  "version" "3.6.17"

"whatwg-mimetype@^2.3.0":
  "integrity" "sha512-M4yMwr6mAnQz76TbJm914+gPpB/nCwvZbJU28cUD6dR004SAxDLOOSUaB1JDRqLtaOV/vi0IC5lEAGFgrjGv/g=="
  "resolved" "https://registry.npmjs.org/whatwg-mimetype/-/whatwg-mimetype-2.3.0.tgz"
  "version" "2.3.0"

"whatwg-url@^8.0.0", "whatwg-url@^8.5.0":
  "integrity" "sha512-gAojqb/m9Q8a5IV96E3fHJM70AzCkgt4uXYX2O7EmuyOnLrViCQlsEBmF9UQIu3/aeAIp2U17rtbpZWNntQqdg=="
  "resolved" "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.7.0.tgz"
  "version" "8.7.0"
  dependencies:
    "lodash" "^4.7.0"
    "tr46" "^2.1.0"
    "webidl-conversions" "^6.1.0"

"which-module@^2.0.0":
  "integrity" "sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ=="
  "resolved" "https://registry.npmjs.org/which-module/-/which-module-2.0.1.tgz"
  "version" "2.0.1"

"which@^1.2.9":
  "integrity" "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ=="
  "resolved" "https://registry.npmjs.org/which/-/which-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.1":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.2":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"wmf@~1.0.1":
  "integrity" "sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw=="
  "resolved" "https://registry.npmmirror.com/wmf/-/wmf-1.0.2.tgz"
  "version" "1.0.2"

"word@~0.3.0":
  "integrity" "sha512-OELeY0Q61OXpdUfTp+oweA/vtLVg5VDOXh+3he3PNzLGG/y0oylSOC1xRVj0+l4vQ3tj/bB1HVHv1ocXkQceFA=="
  "resolved" "https://registry.npmmirror.com/word/-/word-0.3.0.tgz"
  "version" "0.3.0"

"wrap-ansi@^6.2.0":
  "integrity" "sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA=="
  "resolved" "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrap-ansi@^7.0.0":
  "integrity" "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q=="
  "resolved" "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrappy@1":
  "integrity" "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ=="
  "resolved" "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"write-file-atomic@^3.0.0":
  "integrity" "sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q=="
  "resolved" "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "imurmurhash" "^0.1.4"
    "is-typedarray" "^1.0.0"
    "signal-exit" "^3.0.2"
    "typedarray-to-buffer" "^3.1.5"

"ws@^7.4.6":
  "integrity" "sha512-F+P9Jil7UiSKSkppIiD94dN07AwvFixvLIj1Og1Rl9GGMuNipJnV9JzjD6XuqmAeiswGvUmNLjr5cFuXwNS77Q=="
  "resolved" "https://registry.npmjs.org/ws/-/ws-7.5.9.tgz"
  "version" "7.5.9"

"xlsx@0.18.0":
  "integrity" "sha512-zQluErfRAr7ga2me77sIlDoljSrPCXnrNaiKo2+YFLtGkd0aW0Z9zfARVgNn9nytYBhsEjf6A+H5TogTeddscg=="
  "resolved" "https://registry.npmmirror.com/xlsx/-/xlsx-0.18.0.tgz"
  "version" "0.18.0"
  dependencies:
    "adler-32" "~1.3.0"
    "cfb" "^1.1.4"
    "codepage" "~1.15.0"
    "crc-32" "~1.2.1"
    "ssf" "~0.11.2"
    "wmf" "~1.0.1"
    "word" "~0.3.0"

"xml-name-validator@^3.0.0":
  "integrity" "sha512-A5CUptxDsvxKJEU3yO6DuWBSJz/qizqzJKOMIfUJHETbBw/sFaDxgd6fxm1ewUaM0jZ444Fc5vC5ROYurg/4Pw=="
  "resolved" "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-3.0.0.tgz"
  "version" "3.0.0"

"xmlchars@^2.2.0":
  "integrity" "sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw=="
  "resolved" "https://registry.npmjs.org/xmlchars/-/xmlchars-2.2.0.tgz"
  "version" "2.2.0"

"xtend@^4.0.0":
  "integrity" "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ=="
  "resolved" "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz"
  "version" "4.0.2"

"y18n@^4.0.0":
  "integrity" "sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ=="
  "resolved" "https://registry.npmjs.org/y18n/-/y18n-4.0.3.tgz"
  "version" "4.0.3"

"yallist@^2.1.2":
  "integrity" "sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A=="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz"
  "version" "2.1.2"

"yallist@^3.0.2":
  "integrity" "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g=="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz"
  "version" "3.1.1"

"yallist@^4.0.0":
  "integrity" "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
  "version" "4.0.0"

"yaml@^1.10.0":
  "integrity" "sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg=="
  "resolved" "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz"
  "version" "1.10.2"

"yargs-parser@^18.1.2":
  "integrity" "sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ=="
  "resolved" "https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.1.3.tgz"
  "version" "18.1.3"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs@^15.4.1":
  "integrity" "sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A=="
  "resolved" "https://registry.npmjs.org/yargs/-/yargs-15.4.1.tgz"
  "version" "15.4.1"
  dependencies:
    "cliui" "^6.0.0"
    "decamelize" "^1.2.0"
    "find-up" "^4.1.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^4.2.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^18.1.2"

"yorkie@^2.0.0":
  "integrity" "sha512-jcKpkthap6x63MB4TxwCyuIGkV0oYP/YRyuQU5UO0Yz/E/ZAu+653/uov+phdmO54n6BcvFRyyt0RRrWdN2mpw=="
  "resolved" "https://registry.npmjs.org/yorkie/-/yorkie-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "execa" "^0.8.0"
    "is-ci" "^1.0.10"
    "normalize-path" "^1.0.0"
    "strip-indent" "^2.0.0"

"zscroller@~0.4.0":
  "integrity" "sha512-G5NiNLKx2+QhhvZi2yV1jjVXY50otktxkseX2hG2N/eixohOUk0AY8ZpbAxNqS9oJS/NxItCsowupy2tsXxAMw=="
  "resolved" "https://registry.npmjs.org/zscroller/-/zscroller-0.4.8.tgz"
  "version" "0.4.8"
  dependencies:
    "babel-runtime" "6.x"
