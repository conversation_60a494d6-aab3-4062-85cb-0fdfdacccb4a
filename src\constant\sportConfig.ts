export const SportTypeCodeEnum: { [key: string]: number } = {
  football: 0,
  basketball: 1,
  tennis: 2,
  tabletennis: 3,
  baseball: 4,
  amfootball: 5,
  badminton: 6,
  icehockey: 7,
  volleyball: 8,
  handball: 9,
  snooker: 10,
  cricket: 11,
  waterpolo: 12,
  esports: 20,
};

export const SportTypeNameEnum: any = {
  0: 'Football',
  1: 'Basketball',
  2: 'Tennis',
  3: 'Table Tennis',
  4: 'Baseball',
  6: 'Badminton',
  5: 'AM. Football',
  7: 'Hockey',
  8: 'Volleyball',
  9: 'Handball',
  10: 'Snooker',
  11: 'Cricket',
  12: 'Waterpolo',
  20: 'Esports',
};

export const SportTypeList = [
  {
    value: 0,
    label: SportTypeNameEnum['0'],
    color: 'blue',
  },
  {
    value: 1,
    label: SportTypeNameEnum['1'],
    color: 'magenta',
  },
  {
    value: 2,
    label: SportTypeNameEnum['2'],
    color: 'grey',
  },
  {
    value: 3,
    label: SportTypeNameEnum['3'],
    color: 'green',
  },
  {
    value: 4,
    label: SportTypeNameEnum['4'],
    color: 'red',
  },
  {
    value: 5,
    label: SportTypeNameEnum['5'],
    color: 'volcano',
  },
  {
    value: 6,
    label: SportTypeNameEnum['6'],
    color: 'orange',
  },
  {
    value: 7,
    label: SportTypeNameEnum['7'],
    color: 'gold',
  },
  {
    value: 8,
    label: SportTypeNameEnum['8'],
    color: 'lime',
  },
  {
    value: 9,
    label: SportTypeNameEnum['9'],
    color: 'geekblue',
  },
  {
    value: 10,
    label: SportTypeNameEnum['10'],
    color: 'purple',
  },
  {
    value: 11,
    label: SportTypeNameEnum['11'],
    color: 'cyan',
  },
  {
    value: 12,
    label: SportTypeNameEnum['12'],
    color: 'volcano',
  },
  {
    value: 20,
    label: SportTypeNameEnum['20'],
    color: '',
  },
];

// sport `LIVE` status filtering
export const SportTypeLiveStatusEnum = {
  0: [2, 3, 4, 5, 6, 7],
  1: [2, 3, 4, 5, 6, 7, 8, 9],
  2: [51, 52, 53, 54, 55],
  3: [51, 52, 53, 54, 55, 472, 473, 331, 332, 333, 334, 335, 336],
  4: [
    432, 452, 433, 453, 434, 454, 435, 455, 436, 456, 437, 457, 438, 458, 439, 459, 440, 460, 411, 461, 412, 462, 413, 463, 414,
    464, 415, 465, 416, 466, 417, 467, 418, 468, 419, 469,
  ],
  5: [44, 331, 45, 332, 46, 333, 47, 6, 10, 105],
  6: [51, 52, 53, 54, 55, 331, 332, 333, 334],
  7: [30, 331, 31, 32, 332, 6, 10, 8, 13],
  8: [432, 434, 436, 438, 440],
  9: [35, 5, 36, 6, 7, 11, 12, 105, 8, 13, 110],
  10: [3, 4, 476],
  11: [532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543],
  12: [44, 45, 46, 47, 6, 10, 105, 8, 13, 110],
  20: [
    2, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 432, 433, 434, 435, 436, 437, 438, 439, 440, 452, 453, 454, 455,
    456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470,
  ],
};

// sport category filtering (currently only handle Football)
export const SportTypeCategoryEnum: { [key: number]: any } = {
  0: { competition: 'Competition', team: 'Team', match: 'Match', player: 'Player' },
};
