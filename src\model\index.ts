interface AdListDataType {
  key: string;
  name: string;
  id: number;
  client: string;
  width: number;
  length: number;
  image: string;
  commonAd: boolean;
  status: 0 | 1;
  imageUrl: string;
  jumpUrl: string;
  country: string[];
  position: string;
  platform: string;
  description: string;
  createdBy: string;
  modifiedBy: string;
  startTime: number;
  endTime: number;
}

interface ResponseData<T> {
  code: string;
  message: string;
  result: T;
  server_time: number;
}

interface FeaturedSectionDataType {
  id: number;
  sportType: number;
  dataId: string;
  category: string;
  sorting: number;
  status: number;
  extraDescription: string;
  description: string;
  siteUrl: string;
  modifiedBy: string;
  createdBy: string;
  createdAt: number;
  modifiedAt: number;
  startTime: number;
  endTime: number;
}

interface PopularCompetitionDataType {
  id: number;
  sportType: number;
  dataId: string;
  sorting: number;
  status: number;
  extraDescription: string;
  description: string;
  siteUrl: string;
  modifiedBy: string;
  createdBy: string;
  createdAt: number;
  modifiedAt: number;
}

interface DisplayConfigDataType {
  id: number;
  country: string;
  platform: string;
  configName: string;
  status: number;
  modifiedBy: string;
  createdBy: string;
  createdAt: number;
  modifiedAt: number;
}

interface MobileAppDetailDataType {
  id: number;
  country: string;
  platform: string;
  detailName: string;
  detailValue: any;
  status: number;
  modifiedBy: string;
  createdBy: string;
  createdAt: number;
  modifiedAt: number;
}

interface MobileAppDetailDataType {
  id: number;
  country: string;
  platform: string;
  detailName: string;
  detailValue: any;
  status: number;
  modifiedBy: string;
  createdBy: string;
  createdAt: number;
  modifiedAt: number;
}

interface CountryNameMapInterface {
  [key: string]: string;
}

interface AdminAuditLogSearchRequest {
  startTime: number;
  endTime: number;
  triggeredBy: string;
  requestPath: string;
  ipAddress: string;
}

export {
  AdListDataType,
  ResponseData,
  FeaturedSectionDataType,
  PopularCompetitionDataType,
  DisplayConfigDataType,
  MobileAppDetailDataType,
  CountryNameMapInterface,
  AdminAuditLogSearchRequest,
};
