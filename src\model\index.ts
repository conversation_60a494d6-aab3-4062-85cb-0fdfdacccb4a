interface AdListDataType {
  key: string;
  name: string;
  id: number;
  client: string;
  width: number;
  length: number;
  image: string;
  commonAd: boolean;
  status: 0 | 1;
  imageUrl: string;
  jumpUrl: string;
  country: string[];
  position: string;
  platform: string;
  description: string;
  createdBy: string;
  modifiedBy: string;
  startTime: number;
  endTime: number;
}

interface ResponseData<T> {
  code: string;
  message: string;
  result: T;
  server_time: number;
}

interface FeaturedSectionDataType {
  id: number;
  sportType: number;
  dataId: string;
  category: string;
  sorting: number;
  status: number;
  extraDescription: string;
  description: string;
  siteUrl: string;
  modifiedBy: string;
  createdBy: string;
  createdAt: number;
  modifiedAt: number;
  startTime: number;
  endTime: number;
}

interface PopularCompetitionDataType {
  id: number;
  sportType: number;
  dataId: string;
  sorting: number;
  status: number;
  extraDescription: string;
  description: string;
  siteUrl: string;
  modifiedBy: string;
  createdBy: string;
  createdAt: number;
  modifiedAt: number;
}

interface DisplayConfigDataType {
  id: number;
  country: string;
  platform: string;
  configName: string;
  status: number;
  modifiedBy: string;
  createdBy: string;
  createdAt: number;
  modifiedAt: number;
}

interface MobileAppDetailDataType {
  id: number;
  country: string;
  platform: string;
  detailName: string;
  detailValue: any;
  status: number;
  modifiedBy: string;
  createdBy: string;
  createdAt: number;
  modifiedAt: number;
}

interface MobileAppDetailDataType {
  id: number;
  country: string;
  platform: string;
  detailName: string;
  detailValue: any;
  status: number;
  modifiedBy: string;
  createdBy: string;
  createdAt: number;
  modifiedAt: number;
}

interface CountryNameMapInterface {
  [key: string]: string;
}

interface AdminAuditLogSearchRequest {
  startTime: number | null;
  endTime: number | null;
  triggeredBy: string | null;
  requestPath: string | null;
  ipAddress: string | null;
}

interface AdminAuditLog {
  id: number;
  ipAddress: string;
  requestPath: string;
  action: string;
  httpMethod: string;
  requestPayload: string;
  responseStatus: number;
  executionTimeMs: number;
  userAgent: string;
  sessionId: string;
  triggeredAt: number;
  triggeredBy: string;
}

interface Authority {
  authority: string;
}

interface UserData {
  username: string;
  authorities: Authority[];
  accountNonExpired: boolean;
  accountNonLocked: boolean;
  credentialsNonExpired: boolean;
  enabled: boolean;
}

interface AuthContextType {
  user: UserData | null;
  isAuthenticated: boolean;
  hasRole: (role: string) => boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  loading: boolean;
}

interface UseRoleAccessOptions {
  requiredRole: string;
  redirectPath?: string;
  showMessage?: boolean;
}

interface MenuRoute {
  path: string;
  name: string;
  requiredRole?: string;
  routes?: MenuRoute[];
}


export {
  AdListDataType,
  ResponseData,
  FeaturedSectionDataType,
  PopularCompetitionDataType,
  DisplayConfigDataType,
  MobileAppDetailDataType,
  CountryNameMapInterface,
  AdminAuditLogSearchRequest,
  AdminAuditLog,
  Authority,
  UserData,
  AuthContextType,
  UseRoleAccessOptions,
  MenuRoute,
};
