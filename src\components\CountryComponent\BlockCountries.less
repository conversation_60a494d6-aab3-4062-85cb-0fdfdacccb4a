.group-box {
  border-radius: 8px;
  border: 1px solid #eee;
  padding: 10px;
  height: 520px;
  width: 32%;

  .country-list {
    overflow-y: scroll;
    height: 80%;

    .country-list-checkbox {
      display: flex;
      flex-direction: column;
    }
  }

  .country-list-top-section {
    margin-bottom: 10px;

    .ant-divider {
      margin: 0;
      margin-top: 10px;
    }
  }

  .section-header {
    text-align: 'center';

    .ant-divider {
      margin: 0;
      margin-top: 10px;
    }
  }

  .unblocked-country-list {
    overflow-y: scroll;
    height: 90%;
    margin-top: 10px;
  }

  .ant-checkbox-wrapper + .ant-checkbox-wrapper {
    margin-inline-start: 0 !important;
  }
}

.search-country-input {
  margin-bottom: 12px;

  .ant-input-clear-icon {
    font-size: initial;
  }
}

.pop-up-style{
  // width: 200% !important;
  // height: 200% !important;
  top: 20px;
}
