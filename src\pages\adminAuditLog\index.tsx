import { CountryList } from '@/constant/country';
import React, { Key, useEffect, useMemo, useState } from 'react';
import {
  Space,
  Table,
  Modal,
  Button,
  notification,
  Tag,
  Image,
  message,
  Typography,
  Switch,
  Tooltip,
  Input,
  Col,
  Select,
  DatePicker,
  Checkbox,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import {
  RedoOutlined,
  PlusOutlined,
  DeleteOutlined,
  FormOutlined,
  InfoCircleOutlined,
  StopOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import { PlatformEnum, PlatformList, statusFilterOption } from '@/constant';
import { debounce } from '@/utils/help';
import { history } from 'umi';
import dayjs from 'dayjs';

import './index.less';
import { dateTimeTagComponent, ResponseStatusTagComponent, HttpMethodTagComponent } from '@/components/Common/CommonComponent';
import AdModal from '@/components/adListComponent/AdModal';
import { AdminAuditLog, AdminAuditLogSearchRequest } from '@/model';
import { AdminAuditLogHooks } from '@/hooks';

const { confirm } = Modal;
const { RangePicker } = DatePicker;
const { Text } = Typography;
const pageDisplayLabel = 'AD';

const format = 'YYYY-MM-DD';
const defaultEndTime = dayjs();
const defaultStartTime = defaultEndTime.clone().subtract(1, 'days');
const { getListHook, handleTimeChangeHook, fuzzySearchHook } = AdminAuditLogHooks;

const AdList: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const [allRecord, setAllRecord] = useState<AdminAuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalVisible, setModalVisible] = useState(false);
  const [recordId, setRecordId] = useState<number | null>(null); // Store the id of record to be edited
  const [dateRange, setDateRange] = useState([defaultStartTime, defaultEndTime]);
  const [nameFilter, setNameFilter] = useState('');
  const [pageNum, setPageNum] = useState(0);
  const [pageSize, setPageSize] = useState(15);
  const [total, setTotal] = useState(0);

  const getList = getListHook({
    dateRange,
    setAllRecord,
    setLoading,
    messageApi,
  });

  // initial load
  useEffect(() => {
    setLoading(true);
    getList();
  }, [dateRange]);

  const resetModal = () => {
    setModalVisible(false);
    setRecordId(null);
  };

  const onPageChange = (nextPageNumber: number, nextPageSize: number) => {
    if (pageSize !== nextPageSize) {
      setPageSize(nextPageSize);
      setPageNum(0);
    } else {
      setPageNum(nextPageNumber - 1);
    }
  };

  /**
   * lower threshold represents more `exact` search
   * total record is set through this hook
   * */
  const searchedData = fuzzySearchHook({
    allRecord,
    nameFilter,
    threshold: 0.1,
    keys: ['action'],
    setLoading,
    setTotal,
  });

  const columns: ColumnsType<AdminAuditLog> = useMemo(
    () => [
      {
        title: 'User',
        dataIndex: 'triggeredBy',
        key: 'triggeredBy',
      },
      {
        title: 'IP Address',
        dataIndex: 'ipAddress',
        key: 'ipAddress',
        align: 'center',
        render: (value) => (
          <Tag>
            <Text strong>{value}</Text>
          </Tag>
        ),
      },
      {
        title: (
          <Col>
            Action
            <Input
              placeholder="Search..."
              onChange={debounce((e: any) => {
                setLoading(true);
                setNameFilter(e.target.value);
              }, 500)}
              className="search-input"
            />
          </Col>
        ),
        dataIndex: 'action',
        key: 'action',
      },
      {
        title: 'Request Path',
        dataIndex: 'requestPath',
        key: 'requestPath',
        // align: 'center',
        // sorter: (a: any, b: any) => {
        //   return a.startTime - b.startTime;
        // },
        // render: (value) => {
        //   if (!value) {
        //     return <Tag>-</Tag>;
        //   }
        //   return dateTimeTagComponent(value, 'green');
        // },
        // width: '10%',
      },
      {
        title: 'HTTP Method',
        dataIndex: 'httpMethod',
        key: 'httpMethod',
        align: 'center',
        render: (value: string) => HttpMethodTagComponent(value),
        // sorter: (a: any, b: any) => {
        //   return a.endTime - b.endTime;
        // },
        // render: (value) => {
        //   if (!value) {
        //     return <Tag>-</Tag>;
        //   }
        //   return dateTimeTagComponent(value, 'red');
        // },
        // width: '10%',
      },
      {
        title: 'Response Status',
        dataIndex: 'responseStatus',
        key: 'responseStatus',
        align: 'center',
        render: (value: number) => ResponseStatusTagComponent(value),
        // width: '15%',
      },
      {
        title: 'Time',
        dataIndex: 'triggeredAt',
        key: 'triggeredAt',
        align: 'center',
        sorter: (a: any, b: any) => {
          return a.triggeredAt - b.triggeredAt;
        },
        render: (value) => dateTimeTagComponent(value, ''),
      },
    ],
    [allRecord],
  );

  const handleChangeDate = (v1: any) => {
    setDateRange(v1);
  };

  console.log('🚀 ~ dateRange:', dateRange);
  return (
    <div>
      {contextHolder}
      <div className="page-styling">
        <Space wrap style={{ marginBottom: 16 }}>
          <RangePicker format={format} defaultValue={dateRange} onChange={handleChangeDate} clearIcon={false} />
          <Button type="primary" icon={<RedoOutlined />} onClick={getList}>
            Refresh
          </Button>
        </Space>
      </div>
      <Table
        rowKey="id"
        loading={loading}
        columns={columns}
        dataSource={searchedData}
        // rowSelection={{
        //   selectedRowKeys,
        //   onChange: (newSelectedRowKeys: any) => setSelectedRowKeys(newSelectedRowKeys),
        // }}
        pagination={{
          total,
          pageSize,
          defaultPageSize: 15,
          pageSizeOptions: [15, 30, 60],
          showSizeChanger: true,
          current: pageNum + 1,
          showQuickJumper: true,
          onChange: onPageChange,
        }}
      />
      {/* <AdModal
        isModalVisible={isModalVisible}
        onCancel={resetModal}
        resetModal={() => {
          resetModal();
          getList();
        }}
        actionType={actionType}
        recordId={recordId}
        allRecord={allRecord}
      /> */}
    </div>
  );
};

export default AdList;
