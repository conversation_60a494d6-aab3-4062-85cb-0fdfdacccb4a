import React, { Key, useEffect, useMemo, useState } from 'react';
import {
  Space,
  Table,
  Modal,
  Button,
  notification,
  Tag,
  Image,
  message,
  Typography,
  Switch,
  Tooltip,
  Input,
  Col,
  Select,
  DatePicker,
  Checkbox,
  Row,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { DownloadOutlined, RedoOutlined } from '@ant-design/icons';
import { debounce } from '@/utils/help';
import { history } from 'umi';
import dayjs, { Dayjs } from 'dayjs';

import './index.less';
import { dateTimeTagComponent, ResponseStatusTagComponent, HttpMethodTagComponent } from '@/components/Common/CommonComponent';
import { AdminAuditLog } from '@/model';
import { AdminAuditLogHooks } from '@/hooks';

const { RangePicker } = DatePicker;
const { Text } = Typography;

const { getListHook, fuzzySearchHook, downloadCSVHook } = AdminAuditLogHooks;

const AdList: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const [allRecord, setAllRecord] = useState<AdminAuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>([dayjs().startOf('day'), dayjs().endOf('day')]);
  const [nameFilter, setNameFilter] = useState('');
  const [pageNum, setPageNum] = useState(0);
  const [pageSize, setPageSize] = useState(15);
  const [total, setTotal] = useState(0);

  const getList = getListHook({
    dateRange,
    setAllRecord,
    setLoading,
    messageApi,
  });

  const downloadCSV = downloadCSVHook({
    dateRange,
    messageApi,
  });

  // initial load
  useEffect(() => {
    setLoading(true);
    getList();
  }, [dateRange]);

  const onPageChange = (nextPageNumber: number, nextPageSize: number) => {
    if (pageSize !== nextPageSize) {
      setPageSize(nextPageSize);
      setPageNum(0);
    } else {
      setPageNum(nextPageNumber - 1);
    }
  };

  /**
   * lower threshold represents more `exact` search
   * total record is set through this hook
   * */
  const searchedData = fuzzySearchHook({
    allRecord,
    nameFilter,
    threshold: 0.1,
    keys: ['action'],
    setLoading,
    setTotal,
  });

  const columns: ColumnsType<AdminAuditLog> = useMemo(
    () => [
      {
        title: 'User',
        dataIndex: 'triggeredBy',
        key: 'triggeredBy',
      },
      {
        title: 'IP Address',
        dataIndex: 'ipAddress',
        key: 'ipAddress',
        align: 'center',
        render: (value) => (
          <Tag>
            <Text strong>{value}</Text>
          </Tag>
        ),
      },
      {
        title: (
          <Col>
            Action
            <Input
              placeholder="Search..."
              onChange={debounce((e: any) => {
                setLoading(true);
                setNameFilter(e.target.value);
              }, 500)}
              className="search-input"
            />
          </Col>
        ),
        dataIndex: 'action',
        key: 'action',
        width: '30%',
      },
      {
        title: 'Request Path',
        dataIndex: 'requestPath',
        key: 'requestPath',
      },
      {
        title: 'HTTP Method',
        dataIndex: 'httpMethod',
        key: 'httpMethod',
        align: 'center',
        render: (value: string) => HttpMethodTagComponent(value),
      },
      {
        title: 'Response Status',
        dataIndex: 'responseStatus',
        key: 'responseStatus',
        align: 'center',
        render: (value: number) => ResponseStatusTagComponent(value),
      },
      {
        title: 'Time (GMT +8)',
        dataIndex: 'triggeredAt',
        key: 'triggeredAt',
        align: 'center',
        sorter: (a: any, b: any) => {
          return a.triggeredAt - b.triggeredAt;
        },
        render: (value) => dateTimeTagComponent(value, ''),
      },
    ],
    [allRecord],
  );

  return (
    <div>
      {contextHolder}
      <div className="page-styling">
        <Space wrap style={{ marginBottom: 16 }}>
          <div>
            <Text strong>Time Filter: </Text>
            <RangePicker
              format="YYYY-MM-DD HH:mm"
              value={dateRange}
              showTime={true}
              onChange={(dates) => {
                if (dates) {
                  setDateRange(dates as [Dayjs, Dayjs]);
                }
              }}
              clearIcon={false}
              allowClear={false}
              placeholder={['Start Date Filter', 'End Date Filter']}
            />
          </div>

          <Button type="primary" icon={<RedoOutlined />} onClick={getList}>
            Refresh
          </Button>
        </Space>
        <Button
          type="primary"
          size="small"
          icon={<DownloadOutlined />}
          onClick={downloadCSV}
          style={{ marginBottom: '10px', height: '30px', width: '150px' }}
        >
          Download CSV
        </Button>
      </div>
      <Table
        rowKey="id"
        loading={loading}
        columns={columns}
        dataSource={searchedData}
        pagination={{
          total,
          pageSize,
          defaultPageSize: 15,
          pageSizeOptions: [15, 30, 60],
          showSizeChanger: true,
          current: pageNum + 1,
          showQuickJumper: true,
          onChange: onPageChange,
        }}
      />
    </div>
  );
};

export default AdList;
