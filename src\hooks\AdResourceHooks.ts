import { Key, useCallback, useEffect, useMemo, useState } from 'react';
import Fuse from 'fuse.js';

import { AdResourceApi } from '@/api';
import { debounce } from '@/utils';
import { AdSettingInfoMaps } from '@/constant';
import { AdListDataType } from '@/model';
import { BaseImageURL } from '@/api/instance';

const { getData, getSingleData, deleteData, updateStatus } = AdResourceApi;

export const getListHook = (props: any) => {
  const { platform, position, country, status, commonAd, startTime, setAllRecord, setTotal, setLoading, messageApi } = props;
  return debounce(async () => {
    setLoading(true);
    try {
      const result = await getData({ platform, position, country, status, commonAd, startTime }).then((res: any) => {
        return res;
      });
      setAllRecord(result.result.list);
      setTotal(result.result.total);
    } catch (error) {
      messageApi.error('Failed to Retrieve Data!');
    } finally {
      setLoading(false);
    }
  }, 100);
};

export const handleDeleteHook = (props: any) => {
  const { confirm, id, selectedRowKeys, pageDisplayLabel, getList, messageApi } = props;
  confirm({
    title: id > 0 ? `Remove Selected ${pageDisplayLabel} (ID: ${id})?` : `Remove All Selected ${pageDisplayLabel}(s)?`,
    onOk: async () => {
      try {
        await deleteData(id > 0 ? [id] : selectedRowKeys);
        messageApi.open({
          type: 'success',
          content: `${pageDisplayLabel}(s) Delete Success!`,
        });
        getList();
      } catch (e) {
        messageApi.open({
          type: 'error',
          content: `${pageDisplayLabel}(s) Delete Failed!`,
        });
      }
    },
    onCancel: () => {},
    maskClosable: true,
  });
};

// hook to handle single/bulk status update
export const handleUpdateStatusHook = (props: any) => {
  const { confirm, dataToUpdate, pageDisplayLabel, setStatusUpdateLoad, getList, messageApi } = props;

  // handle loading state of status switch
  const loadStatus: { [key: number]: boolean } = dataToUpdate.ids.reduce((acc: any, id: number) => {
    acc[id] = true;
    return acc;
  }, {});
  setStatusUpdateLoad((prev: { [key: number]: boolean }) => ({ ...prev, ...loadStatus }));

  confirm({
    title:
      dataToUpdate.ids.length <= 1
        ? `Update Status ${pageDisplayLabel} (ID: ${dataToUpdate.ids[0]})?`
        : `Update All Selected ${pageDisplayLabel}(s)?`,
    onOk: async () => {
      try {
        await updateStatus(dataToUpdate);
        messageApi.open({
          type: 'success',
          content: `${pageDisplayLabel}(s) Status Update Success!`,
        });
        getList();
      } catch (e) {
        messageApi.open({
          type: 'error',
          content: `${pageDisplayLabel}(s) Status Update Failed!`,
        });
      } finally {
        Object.keys(loadStatus).forEach((value: any, key: any) => {
          loadStatus[value] = false;
        });
        setStatusUpdateLoad((prev: { [key: number]: boolean }) => ({ ...prev, ...loadStatus }));
      }
    },
    onCancel: () => {
      Object.keys(loadStatus).forEach((value: any, key: any) => {
        loadStatus[value] = false;
      });
      setStatusUpdateLoad((prev: { [key: number]: boolean }) => ({ ...prev, ...loadStatus }));
    },
    maskClosable: true,
  });
};

export const fuzzySearchHook = <T>(props: any) => {
  const { allRecord, nameFilter, threshold, keys, setLoading, setTotal } = props;

  return useMemo(() => {
    const fuseInstance = new Fuse(allRecord, { keys, threshold, ignoreLocation: true });

    if (!nameFilter) {
      setLoading(false);
      return allRecord;
    }
    setLoading(true);
    const result = fuseInstance.search(nameFilter).map((item) => item.item);
    console.log("🚀 ~ returnuseMemo ~ result.length:", result.length)
    setTotal(result.length);
    setLoading(false);
    return result;
  }, [allRecord, nameFilter]);
};

export const handleTimeChangeHook = (props: any) => {
  const { form, dateTime } = props;
  if (dateTime) {
    const start = dateTime[0].startOf('day').unix();
    const end = dateTime[1].endOf('day').unix();
    form.setFieldsValue({
      startTime: start,
      endTime: end,
      displayPeriod: dateTime, // Make sure this is set
    });
  } else {
    // Handle null case when clearing the picker
    form.setFieldsValue({
      startTime: null,
      endTime: null,
      displayPeriod: null,
    });
  }
};

export const handleStartTimeChangeHook = (props: any) => {
  const { date, setStartTime } = props;
  setStartTime(!date ? null : date.unix());
};

/**
 * Hooks to reset the state of AdModal component when modal is visible/invisible
 * @param props:
 */
export const modalResetHook = (props: any) => {
  const {
    isModalVisible,
    form,
    recordId,
    setRecordData,
    setImageUrl,
    setCountry,
    setNewCountryList,
    setIsCommonAd,
    setPlatform,
    setPosition,
    messageApi,
  } = props;
  useEffect(() => {
    if (!isModalVisible) {
      form.resetFields();
      setRecordData(null);
      setImageUrl('');
      setCountry([]);
      setNewCountryList([]);
      setIsCommonAd(false);
      setPlatform('');
      setPosition('');
    }
    if (recordId) {
      getSingleData(recordId)
        .then((res: any) => {
          const selectedRecord: AdListDataType = res.result;
          setRecordData(selectedRecord);
        })
        .catch((error) => {
          messageApi.open({ type: 'error', content: 'Error Updating Ad!' });
        });
    }
  }, [isModalVisible]);
};

export const positionListHook = (props: any) => {
  const { platformFilter } = props;
  return useMemo(() => {
    if (!platformFilter || platformFilter === '') return [];
    return Object.keys(AdSettingInfoMaps[platformFilter]).map((value) => ({ value, text: value }));
  }, [platformFilter]);
};

export const adSettingHook = (props: any) => {
  const { platform, position } = props;
  return useMemo(() => {
    if (!platform || !position) return [];
    return AdSettingInfoMaps[platform][position];
  }, [platform, position]);
};

export const handleFileUploadHook = (props: any) => {
  const { info, setImageUrl, form, setLoading } = props;
  return useCallback((info: any) => {
    if (info.file.status === 'uploading') {
      setLoading(true);
      return;
    }
    if (info.file.status === 'error') {
      setLoading(false);
    }
    if (info.file.status === 'done') {
      const newImageUrl = `${BaseImageURL}${info.file.name}`;
      setImageUrl(newImageUrl);
      form.setFieldsValue({ imageUrl: newImageUrl });
      setLoading(false);
    }
  }, []);
};
