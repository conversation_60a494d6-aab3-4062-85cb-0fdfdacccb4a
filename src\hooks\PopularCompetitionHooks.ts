import { useCallback, useEffect, useMemo, useState } from 'react';
import Fuse from 'fuse.js';

import { PopularCompetitionApi } from '@/api';
import { getDataFromSiteUrl, getSportDataFromSiteData, debounce } from '@/utils';
import { SportTypeNameEnum } from '@/constant';
import { PopularCompetitionDataType } from '@/model';

const { getDescription, getData, getSingleData, deleteData, generateSortingNumber } = PopularCompetitionApi;

export const getListHook = (props: any) => {
  const { sportType, startTime, pageNum, pageSize, setAllRecord, setTotal, setLoading, messageApi } = props;
  return debounce(async () => {
    setLoading(true);
    try {
      const result = await getData({ sportType, startTime, pageNum, pageSize }).then((res: any) => {
        return res;
      });
      setAllRecord(result.result.list);
      setTotal(result.result.total);
    } catch (error) {
      messageApi.error('Failed to Retrieve Data!');
    } finally {
      setLoading(false);
    }
  }, 100);
};

export const fuzzySearchHook = (props: any) => {
  const { allRecord, searchQuery, threshold, keys, setLoading } = props;
  return useMemo(() => {
    const fuseInstance = new Fuse(allRecord, { keys, threshold });

    if (!searchQuery) {
      setLoading(false);
      return allRecord;
    }
    setLoading(true);
    const result = fuseInstance.search(searchQuery).map((item) => item.item);
    setLoading(false);
    return result;
  }, [allRecord, searchQuery, threshold]);
};

export const populateDataHook = (props: any) => {
  const { form, siteUrl, setSportType, setDataId, recordData } = props;

  const handleSiteUrlChange = useCallback(
    debounce(() => {
      if (recordData?.siteUrl !== siteUrl) {
        if (siteUrl) {
          const siteData = getDataFromSiteUrl(siteUrl);
          const sportData = getSportDataFromSiteData(siteData[1]);

          form.setFieldValue('sportType', sportData.sport);
          form.setFieldValue('dataId', siteData[2]);

          if (sportData.sport !== null && siteData[2] !== null) {
            getDescription({ sportType: sportData.sport, dataId: siteData[2] }).then((res: any) => {
              if (res.code === 'A00000') {
                form.setFieldValue('description', res.result[0]);
                form.setFieldValue('extraDescription', res.result[1]);
              }
            });
          } else {
            form.setFieldValue('description', '');
            form.setFieldValue('extraDescription', '');
          }
          setSportType(sportData.sport);
          setDataId(siteData[2]);
        } else {
          form.resetFields();
        }
      }
    }, 300),
    [siteUrl],
  );

  useEffect(() => {
    handleSiteUrlChange();
  }, [handleSiteUrlChange]);
};

export const handleDeleteHook = (props: any) => {
  const { confirm, id, selectedRowKeys, pageDisplayLabel, getList, messageApi } = props;
  confirm({
    title: id > 0 ? `Remove Selected ${pageDisplayLabel} (ID: ${id})?` : `Remove All Selected ${pageDisplayLabel}(s)?`,
    onOk: async () => {
      try {
        await deleteData(id > 0 ? [id] : selectedRowKeys);
        messageApi.open({
          type: 'success',
          content: `${pageDisplayLabel}(s) Delete Success!`,
        });
        getList();
      } catch (e) {
        messageApi.open({
          type: 'error',
          content: `${pageDisplayLabel}(s) Delete Failed!`,
        });
      }
    },
    onCancel: () => {},
    maskClosable: true,
  });
};

export const modalResetHook = (props: any) => {
  const { isModalVisible, form, recordId, setRecordData, setSiteUrl, setSorting, setSportType, setDataId, messageApi } = props;
  useEffect(() => {
    if (!isModalVisible) {
      form.resetFields();
      setRecordData(null);
      setSiteUrl('');
      setSportType(null);
      setDataId('');
      setSorting(null);
    }
    if (recordId) {
      getSingleData(recordId)
        .then((res: any) => {
          const selectedRecord: PopularCompetitionDataType = res.result;
          setRecordData(selectedRecord);
        })
        .catch((error) => {
          messageApi.open({ type: 'error', content: 'Error Updating Popular Competition!' });
        });
    }
  }, [isModalVisible]);
};

export const sortingSettingHook = (props: any) => {
  const { setSorting, form, sorting, sportType } = props;
  useEffect(() => {
    if (!sorting && sportType !== null) {
      generateSortingNumber(sportType).then((res: any) => {
        const incrementalSorting = res.result;
        form.setFieldValue('sorting', incrementalSorting);
        setSorting(incrementalSorting);
      });
    }
  }, [sorting, sportType]);
};

export const sportTypeListHook = () => {
  return useMemo(
    () => Object.keys(SportTypeNameEnum).map((value) => ({ value: Number(value), label: SportTypeNameEnum[value] })),
    [SportTypeNameEnum],
  );
};
