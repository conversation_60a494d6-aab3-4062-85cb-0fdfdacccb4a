export default [
  {
    exact: false,
    path: '/',
    component: '@/layouts/index',
    routes: [
      { exact: true, name: 'home', path: '/', component: '@/pages/home' },
      { exact: true, name: 'login', path: 'login', component: '@/pages/login' },

      // Admin Audit Log (ROLE_MASTER only)
      { exact: true, name: 'adminAuditLog', path: 'adminLog/list', component: '@/pages/adminAuditLog' },

      // Ad Banner
      { exact: true, name: 'adList', path: 'ad/list', component: '@/pages/adList' },

      // Video Manage
      { exact: true, name: 'video', path: 'video/list', component: '@/pages/video' },
      { exact: true, name: 'blacklist', path: 'video/blacklist', component: '@/pages/blacklist' },
      { exact: true, name: 'countrySetting', path: 'video/countrysetting', component: '@/pages/countrySetting' },
      
      // Crawler Manager
      { exact: true, name: 'matching', path: 'matching/rules', component: '@/pages/matching/rules' },

      // Site Settings
      { exact: true, name: 'displayConfig', path: 'setting/displayConfig', component: '@/pages/setting/displayConfig' },
      { exact: true, name: 'featured', path: 'setting/featured', component: '@/pages/setting/featured' },
      { exact: true, name: 'popularCompetition', path: 'setting/popularcompetition', component: '@/pages/setting/popularCompetition' },
      { exact: true, name: 'mobileAppDetail', path: 'setting/mobileappdetail', component: '@/pages/setting/mobileAppDetail' },
    ],
  },
];
