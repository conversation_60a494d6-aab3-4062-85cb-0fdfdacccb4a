import { Tag, Modal, Space } from 'antd';
import React, { memo, useState } from 'react';

import { CountryNameMap } from '@/constant/country';

import './BlockedCountryName.less';

const ModalMaxHeight = window.innerHeight * 0.7;

const NameModal = memo((props: any) => {
  const { setOpen, countryCodes, modalTitle="Blocked in Following Countries {0}" } = props;
  return (
    <Modal
      bodyStyle={{ maxHeight: ModalMaxHeight, overflow: 'auto' }}
      title={modalTitle.replace('{0}', countryCodes.length.toString())}
      open={true}
      onCancel={() => setOpen(false)}
      footer={[]}
    >
      <Space size={[0, 8]} wrap>
        {countryCodes.map((item: string) => {
          return <Tag key={item}>{CountryNameMap[item]}</Tag>;
        })}
      </Space>
    </Modal>
  );
});

const BlockedCountryName: React.FC<any> = (props: any) => {
  const { countryCodes = [], itemDisplayLimit = 5, modalTitle } = props;
  const [open, setOpen] = useState(false);

  if (!countryCodes || countryCodes.length === 0) {
    return <span>-</span>;
  }

  return (
    <>
      <div style={{ cursor: 'pointer' }} onClick={() => setOpen(true)}>
        {countryCodes.slice(0, itemDisplayLimit).map((item: string) => {
          return <Tag key={item}>{CountryNameMap[item]}</Tag>;
        })}
        {countryCodes.length > itemDisplayLimit && <span> ... </span>}
      </div>
      {open && <NameModal countryCodes={countryCodes} setOpen={setOpen} modalTitle={modalTitle}/>}
    </>
  );
};

export default memo(BlockedCountryName);
