.container {
  display: flex;
  flex-direction: column;
  margin-bottom: 40px;
}

.sub-title-styling {
  margin-top: 0;
  margin-bottom: 0.5em;
  color: rgba(0, 0, 0, 0.88);
  font-weight: 600;
  line-height: 1.5;
  font-size: 18px;
}

.switch-container {
  display: flex;
  gap: 30px; /* Spacing between switch units */
}

.switch-unit {
  display: flex;
  align-items: flex-end;
  gap: 10px;
  margin-right: 40px;
}
