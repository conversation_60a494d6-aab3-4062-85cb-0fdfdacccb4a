.form-outline {
  width: 100%;
  height: 100%;
  font-size: 24px;
  cursor: pointer;
  color: #1677ff;
}

.enable-button {
  color: green;
  border-color: green;
}

.search-input {
  margin-top: 2;
}

.page-styling {
  display: flex;
  flex-direction: column;
}

.additional-note {
  display: flex;
  align-items: center;
  margin-block: 12px;
  font-size: 16px;
  font-weight: bold;

  .label {
    margin-right: 4px;
    color: #1890ff;
  }

  .text {
    font-size: inherit;
  }
}

.show-common-ad-switch {
  width: 150px
}

.table-filter-dropdown {
  padding: 8px;
  width: 350px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  .search-input {
    margin-bottom: 10px;
  }

  .selected-filters {
    margin-bottom: 10px;

    .display-frame {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .ant-tag {
        margin-right: 0 !important;
      }
    }
  }

  .table-filter-options {
    max-height: 250px;
    overflow-y: auto;
    border-top: 1px solid #f0f0f0;
    border-bottom: 1px solid #f0f0f0;

    .table-filter-item {
      padding: 1px 4px;
      cursor: pointer;
      background-color: transparent;
      display: flex;
      align-items: center;

      &:hover {
        background-color: #f5f5f5;
      }
    }

    .filter-not-found {
      padding: 8px;
      color: rgba(0, 0, 0, 0.35);
      font-weight: bold;
    }
  }

  .action-buttons {
    display: flex;
    justify-content: space-evenly;
    margin-top: 8px;

    .button-styling {
      width: 30%;
    }
  }
}

// Override width for status filter dropdown specifically
.table-filter-dropdown.status-filter-dropdown {
  width: 150px !important;
}
