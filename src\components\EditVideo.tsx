import { InfoCircleFilled } from '@ant-design/icons';
import { Form, Input, Modal, Popover } from 'antd';
import videoLinkDemo from '../../public/img/videolinkdemo.png';
import { useState } from 'react';

export default function EditVideo(props: any) {
  const { item, onConfirm, onCancel } = props;

  const { matchId, videoId, sportType } = item;

  const [videoLink, setVideoLink] = useState(videoId);

  const ok = () => {
    const createdBy = localStorage.getItem('username');
    onConfirm({
      matchId,
      sportType,
      videoId,
      createdBy,
    });
  };

  return (
    <Modal
      title="Add Live stream"
      style={{ top: '100px' }}
      open={true}
      onOk={ok}
      okButtonProps={{
        disabled: !videoLink,
      }}
      onCancel={onCancel}
    >
      <Form name="basic" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ maxWidth: 600 }} autoComplete="off">
        <Form.Item
          label="Igscore Match Link"
          name="game"
          rules={[{ required: true, message: 'Please input Igscore Match Link!' }]}
        >
          <Input placeholder="Please input Igscore Match Link!" defaultValue={matchId} disabled />
        </Form.Item>

        <Form.Item label="Live Link or ID" name="video" rules={[{ required: true, message: 'Please input Live Link or ID!' }]}>
          <div className="videoDemo">
            <Input
              value={videoLink}
              defaultValue={videoLink}
              placeholder="Please input Live stream Link or ID!"
              onChange={(e) => setVideoLink(e.target.value)}
            />
            <Popover
              content={<img src={videoLinkDemo} style={{ width: '600px', height: 'auto' }} />}
              title=""
              placement="bottomLeft"
              arrow={false}
            >
              <InfoCircleFilled style={{ background: '#fff', marginLeft: '10px' }} />
            </Popover>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
}
