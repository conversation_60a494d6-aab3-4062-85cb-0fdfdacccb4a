import igsRequest from './instance';
import { localStorageKey } from '@/constant/config';

const urlPath = 'user/config/displayconfig';

export const getData = async () => {
  return igsRequest.post(`${urlPath}/search`);
};

export const updateData = async (params: any) => {
  return igsRequest.post(
    `${urlPath}`,
    Object.assign(params, {
      modifiedBy: localStorage.getItem(localStorageKey.USERNAME),
    }),
  );
};
