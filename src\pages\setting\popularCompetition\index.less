.form-outline {
  width: 100%;
  height: 100%;
  font-size: 24px;
  cursor: pointer;
  color: #1677ff;
}

.enable-button {
  color: green;
  border-color: green;
}

.search-input {
  margin-top: 2;
}

.category-tag {
  transform: scale(1.3);
}

.additional-note {
  display: flex;
  align-items: center;
  margin-block: 12px;
  font-size: 16px;
  font-weight: bold;

  .label {
    margin-right: 4px;
    color: #1890ff;
  }

  .text {
    font-size: inherit;
  }
}

.page-styling {
  display: flex;
  flex-direction: column;
}

.sorting-col {
  display: flex;
  align-items: center;
  justify-content: center;

  .col-item {
    display: inherit;
    align-items: inherit;
  }

  .label-style {
    margin-right: 10px;
    white-space: nowrap;
  }
}
