import { goLogout } from '@/api';
import { <PERSON><PERSON><PERSON><PERSON>, ProLayout, DefaultFooter } from '@ant-design/pro-components';
import { App, <PERSON><PERSON>, ConfigProvider } from 'antd';
import { useCallback } from 'react';
import { history } from 'umi';
import enUS from 'antd/locale/en_US';
import Menu from './menu';
import { Provider } from 'mobx-react';
import store from '@/store';

import logo from '../../public/img/logo.png';
import icon from '../../public/img/logo_icon.png';
import styles from './index.less';

import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import localeData from 'dayjs/plugin/localeData';
import weekday from 'dayjs/plugin/weekday';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import weekYear from 'dayjs/plugin/weekYear';

dayjs.extend(customParseFormat);
dayjs.extend(advancedFormat);
dayjs.extend(weekday);
dayjs.extend(localeData);
dayjs.extend(weekOfYear);
dayjs.extend(weekYear);

export default (props: any) => {
  const isLoginPage = history.location.pathname.indexOf('login') > -1;
  const logout = useCallback(() => {
    goLogout().finally(() => {
      localStorage.clear();
      history.replace('/login');
    });
  }, []);
  const {
    UserStore: { username },
  }: { UserStore: any } = store;

  return (
    <Provider store={store}>
      <ConfigProvider locale={enUS}>
        {isLoginPage ? (
          <PageContainer style={{ background: '#fff' }}>{props.children}</PageContainer>
        ) : (
          <App>
            <div className={styles.container}>
              {/* @ts-ignore */}
              <ProLayout
                locale="en-US"
                title="Admin Web"
                loading={false}
                logo={
                  <div className={styles.header}>
                    <img className={styles.logo} src={logo} alt="logo" onClick={() => history.push('/')} />
                  </div>
                }
                menu={{
                  defaultOpenAll: true,
                  hideMenuWhenCollapsed: true,
                  ignoreFlatMenu: true,
                }}
                avatarProps={{
                  src: icon,
                  size: 'small',
                  title: localStorage.getItem('username'),
                }}
                route={Menu}
                menuItemRender={(item: any, dom) => <a onClick={() => history.push(item.path)}>{dom}</a>}
                menuFooterRender={() => <Button onClick={logout}>Log out</Button>}
                footerRender={() => <DefaultFooter copyright="IGScore" />}
              >
                <PageContainer style={{ backgroundColor: '#ffffff', borderRadius: 10, minHeight: 600 }}>
                  {props.children}
                </PageContainer>
              </ProLayout>
            </div>
          </App>
        )}
      </ConfigProvider>
    </Provider>
  );
};
