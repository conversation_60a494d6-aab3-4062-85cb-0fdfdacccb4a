# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: STG CI

on:
  push:
    branches: ['staging']

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [14.18.1] # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: Cache Yarn Dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/yarn
            node_modules
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install Dependencies & Build
        run: |
          echo "Installing packages..."
          yarn
          echo "Building for Staging..."
          yarn build:staging

      - name: Archive Old Artifact
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.STG_SERVER_IP }}
          username: ${{ secrets.STG_SSH_USERNAME }}
          key: ${{ secrets.STG_SSH_TOKEN }}
          port: ${{ secrets.STG_SSH_PORT }}
          script: |
            rm -rf /home/<USER>/admin-old
            cp -r /home/<USER>/admin /home/<USER>/admin-old

      - name: Deploy Artifact to Server
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.STG_SERVER_IP }}
          username: ${{ secrets.STG_SSH_USERNAME }}
          key: ${{ secrets.STG_SSH_TOKEN }}
          port: ${{ secrets.STG_SSH_PORT }}
          timeout: 60s
          rm: true
          source: 'dist/*'
          strip_components: 1
          target: '/home/<USER>/admin'

      # - name: Prepare SSH key
      #   run: |
      #     echo "${{ secrets.STG_SSH_TOKEN }}" > key.pem
      #     chmod 600 key.pem

      # - name: Deploy only changed files via rsync
      #   run: |
      #     rsync -avz --delete \
      #       -e "ssh -i key.pem -p ${{ secrets.STG_SSH_PORT }} -o StrictHostKeyChecking=no" \
      #       ./dist/ \
      #       ${{ secrets.STG_SSH_USERNAME }}@${{ secrets.STG_SERVER_IP }}:/home/<USER>/admin/

      # - name: Cleanup SSH key
      #   run: rm -f key.pem

    # # Optional: Restart nginx or any server process
    # - name: Restart Nginx (optional)
    #   if: success()  # only run if previous steps succeeded
    #   uses: appleboy/ssh-action@v1.0.0
    #   with:
    #     host: ${{ secrets.STG_SERVER_IP }}
    #     username: ${{ secrets.STG_SSH_USERNAME }}
    #     key: ${{ secrets.STG_SSH_TOKEN }}
    #     port: ${{ secrets.STG_SSH_PORT }}
    #     script: |
    #       sudo systemctl restart nginx
