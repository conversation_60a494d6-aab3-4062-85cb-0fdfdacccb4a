import { CountryNameMapInterface } from '@/model';

const countries = require('i18n-iso-countries');

// import english language country
countries.registerLocale(require('i18n-iso-countries/langs/en.json'));

const alpha2CodeCountryName = countries.getNames('en');
export const CountryList = Object.keys(alpha2CodeCountryName).map((key) => {
  return {
    value: key,
    label: alpha2CodeCountryName[key],
  };
});

export const CountryNameMap: CountryNameMapInterface = {};
for (let v of CountryList) {
  CountryNameMap[v.value] = v.label;
}
