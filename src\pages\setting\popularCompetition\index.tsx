import React, { useEffect, useMemo, useState } from 'react';
import { Space, Table, Modal, Button, notification, Tag, message, Typography, Switch, Tooltip, Input, Col, Select } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { RedoOutlined, PlusOutlined, DeleteOutlined, FormOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { PopularCompetitionApi } from '@/api';
import { SportTypeList, statusFilterOption } from '@/constant';
import { debounce } from '@/utils/help';

import './index.less';
import { dateTimeTagComponent, UpdateSingleFieldModal } from '@/components/Common/CommonComponent';
import PopularCompetitionModal from '@/components/PopularCompetitionComponent/Modal';
import { PopularCompetitionDataType } from '@/model';
import { PopularCompetitionHooks } from '@/hooks';

const { confirm } = Modal;
const { Text } = Typography;
const { fuzzySearchHook, getListHook, handleDeleteHook } = PopularCompetitionHooks;
const { updateData, generateSortingNumber } = PopularCompetitionApi;
const pageDisplayLabel = 'Popular Competition';

const PopularCompetitionList: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const [allRecord, setAllRecord] = useState<PopularCompetitionDataType[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [statusUpdateLoad, setStatusUpdateLoad] = useState<{ [key: number]: boolean }>({});
  const [isModalVisible, setModalVisible] = useState(false);
  const [recordId, setRecordId] = useState<number | null>(null);
  const [actionType, setActionType] = useState('');
  const [sportTypeFilter, setSportTypeFilter] = useState(-1);
  const [total, setTotal] = useState(0);
  const [pageNum, setPageNum] = useState(0);
  const [pageSize, setPageSize] = useState(15);
  const [descriptionFilter, setDescriptionFilter] = useState('');

  // const [isUpdateSingleFieldVisible, setUpdateSingleFieldVisible] = useState(false);
  // const [oldData, setOldData] = useState<number | null>(null);
  // const [fieldNameToUpdate, setFieldNameToUpdate] = useState('');
  // const [selectedRecord, setSelectedRecord] = useState<PopularCompetitionDataType | null>(null);

  const sportsArray = SportTypeList.map((item) => ({ label: item.label, value: item.value }));
  sportsArray.unshift({
    value: -1,
    label: 'ALL',
  });

  const hasSelected = useMemo(() => selectedRowKeys.length > 0, [selectedRowKeys]);
  const getList = getListHook({
    sportType: sportTypeFilter,
    pageNum,
    pageSize,
    setAllRecord,
    setTotal,
    setLoading,
    messageApi,
  });

  //initial load
  useEffect(() => {
    setLoading(true);
    getList();
  }, [sportTypeFilter, pageNum, pageSize]);

  const resetModal = () => {
    setModalVisible(false);
    // setUpdateSingleFieldVisible(false);
    setRecordId(null);
  };

  const onPageChange = (nextPageNumber: number, nextPageSize: number) => {
    if (pageSize !== nextPageSize) {
      setPageSize(nextPageSize);
      setPageNum(0);
    } else {
      setPageNum(nextPageNumber - 1);
    }
  };

  // temporarily disable search function
  const filteredData = fuzzySearchHook({
    allRecord,
    descriptionFilter,
    threshold: 0.5,
    keys: ['description'],
    setLoading,
  });

  const handleDelete = (id: number) =>
    handleDeleteHook({
      confirm,
      id,
      selectedRowKeys,
      pageDisplayLabel,
      getList,
      messageApi,
    });

  const updateStatus = (data: PopularCompetitionDataType) => {
    const newStatus: number = data.status === 1 ? 0 : 1;
    //update status field with new status after confirm update action
    updateData(Object.assign(data, { status: newStatus }))
      .then(() => {
        notification.success({
          key: 'success',
          message: 'Success!',
          description: (
            <Text>
              Status Update Success! {pageDisplayLabel} ID: <b> {data.id || ''}</b>
            </Text>
          ),
        });
        getList();
      })
      .catch(() => {
        notification.error({
          key: 'error',
          message: 'Error!',
          description: (
            <Text>
              Status Update Failed! {pageDisplayLabel} ID: <b> {data.id || ''}</b>
            </Text>
          ),
        });
      })
      .finally(() => {
        setStatusUpdateLoad((prev) => ({ ...prev, [data.id]: false }));
      });
  };

  const updateStatusConfirm = (data: PopularCompetitionDataType) => {
    setStatusUpdateLoad((prev) => ({ ...prev, [data.id]: true }));
    confirm({
      title: `Confirmation`,
      content: (
        <Text>
          {data.status === 1 ? 'Disable' : 'Enable'} {pageDisplayLabel} <b>(ID: {data.id || ''})</b>?
        </Text>
      ),
      onOk() {
        updateStatus(data);
      },
      onCancel() {
        setStatusUpdateLoad((prev) => ({ ...prev, [data.id]: false }));
      },
      maskClosable: true,
    });
  };

  // const sortingDataChecking = (selectedRecord: PopularCompetitionDataType, sorting: number) => {
  //   if (sorting !== null && selectedRecord !== null) {
  //     return generateSortingNumber(selectedRecord.sportType)
  //       .then((res: any) => {
  //         if (sorting > res.result - 1) {
  //           return false;
  //         }
  //         return true;
  //       })
  //       .catch(() => {
  //         return false;
  //       });
  //   }
  //   return Promise.resolve(false);
  // };

  const columns: ColumnsType<PopularCompetitionDataType> = useMemo(
    () => [
      {
        title: 'Id',
        dataIndex: 'id',
        key: 'id',
      },
      {
        title: 'Sports',
        dataIndex: 'sportType',
        key: 'sportType',
        render: (value) => {
          const tagColor = SportTypeList.filter((item) => item.value === value)[0].color;
          return (
            <Tag color={tagColor} className="category-tag">
              {SportTypeList.filter((item) => item.value === value)[0].label}
            </Tag>
          );
        },
      },
      {
        title: 'Sorting',
        dataIndex: 'sorting',
        key: 'sorting',
        sorter: (a, b) => a.sorting - b.sorting,
        render: (value, record) => (
          <Text strong className="label-style">
            {value}
          </Text>
          // <div className="sorting-col">
          //   <div className="col-item">
          //     <Text strong className="label-style">
          //       {value}
          //     </Text>
          //     <Tooltip title="EDIT">
          //       <FormOutlined
          //         className="form-outline"
          //         onClick={() => {
          //           setOldData(value);
          //           setFieldNameToUpdate('sorting');
          //           setSelectedRecord(record);
          //           setActionType('update');
          //           setUpdateSingleFieldVisible(true);
          //         }}
          //       />
          //     </Tooltip>
          //   </div>
          // </div>
        ),
        align: 'center',
      },
      {
        title: (
          <Col>
            Description
            <Input
              placeholder="Search..."
              onChange={debounce((e: any) => {
                setLoading(true);
                setDescriptionFilter(e.target.value);
              }, 300)}
              className="search-input"
            />
          </Col>
        ),
        dataIndex: 'description',
        key: 'description',
        width: '15%',
      },
      {
        title: 'Extra Description',
        dataIndex: 'extraDescription',
        key: 'extraDescription',
        width: '10%',
      },
      {
        title: 'Modified At',
        dataIndex: 'modifiedAt',
        key: 'modifiedAt',
        align: 'center',
        sorter: (a: any, b: any) => {
          return a.modifiedAt - b.modifiedAt;
        },
        render: (value) => dateTimeTagComponent(value, ''),
      },
      {
        title: 'Modified By',
        dataIndex: 'modifiedBy',
        key: 'modifiedBy',
      },
      {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        align: 'center',
        filters: statusFilterOption,
        onFilter: (value, record) => value === record?.status,
        render: (value, record) => {
          const checkStatus: boolean = value === 1 ? true : false;
          return (
            <Switch
              checkedChildren="Enable"
              unCheckedChildren="Disable"
              loading={statusUpdateLoad[record.id]}
              checked={checkStatus}
              onClick={() => {
                updateStatusConfirm(record);
              }}
            />
          );
        },
      },
      {
        title: 'Options',
        key: 'action',
        align: 'center',
        render: (_, record) => (
          <Space size="middle">
            <Tooltip title="EDIT">
              <FormOutlined
                className="form-outline"
                onClick={() => {
                  setActionType('update');
                  setRecordId(record.id);
                  setModalVisible(true);
                }}
              />
            </Tooltip>
            <Tooltip title="DELETE">
              <DeleteOutlined
                className="form-outline"
                onClick={() => {
                  handleDelete(record.id);
                }}
              />
            </Tooltip>
          </Space>
        ),
      },
    ],
    [allRecord, statusUpdateLoad],
  );

  return (
    <div className="page-styling">
      {contextHolder}
      <Space wrap>
        <Select
          value={sportTypeFilter}
          style={{ width: 150 }}
          onChange={(value) => {
            setSportTypeFilter(value);
          }}
          options={sportsArray}
        />
        <Button
          type="primary"
          onClick={() => {
            setActionType('create');
            setModalVisible(true);
          }}
        >
          <PlusOutlined />
          Create
        </Button>
        <Button type="primary" onClick={getList}>
          <RedoOutlined />
          Refresh
        </Button>
        <Button
          type="primary"
          disabled={!hasSelected}
          danger
          onClick={() => {
            handleDelete(-1);
            setSelectedRowKeys([]);
          }}
        >
          <DeleteOutlined />
          Delete
        </Button>
      </Space>
      <div className="additional-note">
        <InfoCircleOutlined className="label" />
        <Text className="text">Competition with same sorting setting will be sorted according to `Modified At`</Text>
      </div>
      <Table
        rowKey="id"
        loading={loading}
        columns={columns}
        dataSource={filteredData}
        rowSelection={{
          selectedRowKeys,
          onChange: (newSelectedRowKeys: any) => setSelectedRowKeys(newSelectedRowKeys),
        }}
        pagination={{
          total,
          pageSize,
          defaultPageSize: 50,
          pageSizeOptions: [20, 50, 100],
          showSizeChanger: true,
          current: pageNum + 1,
          showQuickJumper: true,
          onChange: onPageChange,
        }}
      />
      <PopularCompetitionModal
        isModalVisible={isModalVisible}
        onCancel={resetModal}
        resetModal={() => {
          resetModal();
          getList();
        }}
        actionType={actionType}
        recordId={recordId}
      />
      {/* <UpdateSingleFieldModal
        updateApi={updateData}
        isModalVisible={isUpdateSingleFieldVisible}
        onCancel={resetModal}
        resetModal={() => {
          resetModal();
          getList();
        }}
        fieldDataType={typeof oldData}
        selectedRecord={selectedRecord}
        fieldName={fieldNameToUpdate}
        pageOrigin={pageDisplayLabel}
        oldData={oldData}
        dataChecking={sortingDataChecking}
      /> */}
    </div>
  );
};

export default PopularCompetitionList;
