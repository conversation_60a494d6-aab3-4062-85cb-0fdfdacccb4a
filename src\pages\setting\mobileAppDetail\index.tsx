import React, { useEffect, useReducer, useState } from 'react';
import { Space, Modal, message, Typography, Switch, Spin, Input, Button } from 'antd';

import { MobileAppDetailApi } from '@/api';
import { MobileAppDetailConstant } from '@/constant';
import { MobileAppDetailHooks } from '@/hooks';
import { MobileAppDetailDataType } from '@/model';

import './index.less';

const { confirm } = Modal;
const { Text } = Typography;
const { MobileAppDetailLabel } = MobileAppDetailConstant;
const { getMobileAppDetailDataHook } = MobileAppDetailHooks;
const { updateData } = MobileAppDetailApi;

const MobileAppDetail: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<any>({});
  const [isModalVisible, setModalVisible] = useState(false);
  const [newValue, setNewValue] = useState();
  const [modalTitle, setModalTitle] = useState<string>('');

  const getMobileAppDetailData = () =>
    getMobileAppDetailDataHook({
      setData,
      setLoading,
      messageApi,
    });

  useEffect(() => {
    getMobileAppDetailData();
  }, []);

  const changeConfigConfirm = (params: any) => {
    confirm({
      title: `Confirmation`,
      content: (
        <Text>
          Update <b>{params.label}</b> to <b>{newValue}</b>?
        </Text>
      ),
      onOk() {
        updateMobileAppDetail(params);
        setModalVisible(false);
      },
      onCancel() {},
      maskClosable: true,
      width: 400,
    });
  };

  const updateMobileAppDetail = (params: any) => {
    //update status field with new status after confirm update action
    updateData({ detailName: 'appVersion', detailValue: newValue })
      .then(() => {
        messageApi.open({
          type: 'success',
          content: (
            <Text>
              <b>{params.label}</b> Updated Success!
            </Text>
          ),
        });
      })
      .catch(() => {
        messageApi.open({
          type: 'error',
          content: (
            <Text>
              <b>{params.label}</b> Update Failed!
            </Text>
          ),
        });
      })
      .finally(() => {
        getMobileAppDetailData();
      });
  };

  const handleCancel = () => {
    setModalVisible(false);
  };

  const showModal = (label: any) => {
    setNewValue(data.appVersion);
    setModalTitle(label);
    setModalVisible(true);
  };
  // style={{ fontSize: '14px', width: '200px' }}
  return (
    <Spin size="large" spinning={loading}>
      {contextHolder}
      <div className="container">
        <Text className="sub-title-styling">{MobileAppDetailLabel.appVersion.label}</Text>
        <div className="textbox-container">
          <Input value={data.appVersion} disabled className="display-data-label" />
          <Button type="primary" onClick={() => showModal(MobileAppDetailLabel.appVersion.label)}>
            Update
          </Button>
        </div>
        <Modal
          title={'Update ' + modalTitle}
          open={isModalVisible}
          onOk={()=> changeConfigConfirm({ label: modalTitle })}
          onCancel={handleCancel}
          width={500}
        >
          <Input value={newValue} onChange={(e: any) => setNewValue(e.target.value)} />
        </Modal>
      </div>
    </Spin>
  );
};

export default MobileAppDetail;
