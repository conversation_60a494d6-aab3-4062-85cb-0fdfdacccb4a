// @ts-nocheck
import React, { useEffect, useState } from 'react';
import { Space, Table, Modal, Button, Select, Input, message } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { PlusOutlined, StopOutlined, SearchOutlined, DeleteOutlined, EditOutlined, RedoOutlined } from '@ant-design/icons';
import { banCompetitionVideo, searchBanCompetitions } from '@/api';
import { SportTypeNameEnum } from '@/constant/sportConfig';
import { debounce, getCompIdFromUrl, getGameType } from '@/utils/help';

import UploadFile from '@/components/UploadFile';
import BlockCountries from '@/components/CountryComponent/BlockCountries';
import BlockedCountryName from '@/components/CountryComponent/BlockedCountryName';
import BlockCompetitionSteps from '@/components/BlockCompetitionSteps';
import { dateTimeTagComponent } from '@/components/Common/CommonComponent';

const { error, confirm } = Modal;
const CompGameList = {
  football: 'FOOTBALL',
  basketball: 'BASKETBALL',
};

const sportsArray = Object.keys(CompGameList).map((key) => {
  return {
    value: key,
    // @ts-ignore
    label: CompGameList[key],
  };
});
sportsArray.unshift({
  value: '',
  label: 'Sport Type',
});

interface DataType {
  awayTeamId: string;
  awayTeamName: string;
  competitionId: string;
  competitionName: string;
  countryCodes: string[];
  homeTeamId: string;
  homeTeamName: string;
  id: any;
  matchTime: number;
  sportType: number;
  statusId: number;
  videoId: string;
}

const debounceList = debounce((cb) => {
  if (cb) cb();
}, 300);

const App: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();

  const [competitionId, setCompetitionId] = useState<string>('');
  const [list, setList] = useState<DataType[]>([]);
  const [blockCompList, setBlockCompList] = useState<DataType[]>([]);
  const [defaultCheckedList, setDefaultCheckedList] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [sportType, setSportType] = useState('');
  const [pageNum, setPageNum] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [isBlockCountriesStepVisible, setIsBlockCountriesStepVisible] = useState(false);
  const [isBlockCountriesVisible, setIsBlockCountriesVisible] = useState(false);
  const [total, setTotal] = useState(0);

  const onPageChange = (nextPageNumber, nextPageSize) => {
    if (pageSize !== nextPageSize) {
      setPageSize(nextPageSize);
      setPageNum(0);
    } else {
      setPageNum(nextPageNumber - 1);
    }
  };

  const getList = () => {
    setLoading(true);
    searchBanCompetitions({
      sportType,
      competitionId: sportType ? competitionId : '',
      pageNum,
      pageSize,
    })
      .then((d: any) => {
        setList(d.result.competitionBans);
        setTotal(d.result.total);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    debounceList(getList);
  }, [sportType, competitionId, pageSize, pageNum]);

  const block = (item: DataType) => {
    const { countryCodes = [] } = item;
    setDefaultCheckedList(countryCodes || []);
    setIsBlockCountriesVisible(true);
    setBlockCompList([item]);
  };

  const blockList = (idList) => {
    const selectedList = list.filter((item) => idList.includes(item.competitionId));
    setDefaultCheckedList([]);
    setIsBlockCountriesVisible(true);
    setBlockCompList(selectedList);
  };

  const blockAll = () => {
    blockList(selectedRowKeys);
  };

  const deleteAll = () => {
    confirm({
      title: `Delete ALL Selected Competition Blacklist?`,
      type: 'warn',
      onOk: () => {
        const selectedList = list
          .filter((item) => selectedRowKeys.includes(item.competitionId))
          .map((item) => {
            return {
              ...item,
              countryCodes: null,
            };
          });
        banCompetitionVideoList(selectedList);
      },
    });
  };

  const onCloseBlockModal = () => {
    setDefaultCheckedList([]);
    setIsBlockCountriesVisible(false);
    setBlockCompList([]);
  };

  const banCompetitionVideoList = (videoCompetitionBanList) => {
    banCompetitionVideo(videoCompetitionBanList, false)
      .then((d) => {
        if (d.code === 'A00000') {
          messageApi.open({
            type: 'success',
            content: 'Update Competition Blacklist Success!',
          });
          getList();
        } else {
          messageApi.open({
            type: 'error',
            content: d.message,
          });
        }
      })
      .catch(() => {
        messageApi.open({
          type: 'error',
          content: 'Failed to update live stream blacklist for competition/league!',
        });
      });
  };

  const onBlockCoutriesConfirm = (countryCodes: string[]) => {
    confirm({
      title: `Confirmation`,
      content: 'Confirm Update Country Blacklist?',
      onOk() {
        onCloseBlockModal();
        const videoCompetitionBanList = blockCompList.map((item) => {
          return {
            competitionId: item.competitionId,
            countryCodes,
            sportType: item.sportType,
          };
        });
        banCompetitionVideoList(videoCompetitionBanList);
      },
      onCancel() {
        onCloseBlockModal();
      },
      maskClosable: true,
    });
  };

  const onSelectChange = (newSelectedRowKeys: []) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const handleChangeSport = (v: string) => {
    setSportType(v);
  };

  const getInputCompId = (v: string) => {
    let id: string;
    try {
      id = getCompIdFromUrl(v);
    } catch (e) {
      id = v;
    }
    setCompetitionId(id);
  };

  const importExcel = (list) => {
    let compIdList = [];
    let count = 0;
    for (let v of list) {
      try {
        const competitionId = getCompIdFromUrl(v.competitionlink);
        const sportType = getGameType(v.competitionlink);
        compIdList.push({
          competitionId,
          sportType,
        });
      } catch (e) {
        count++;
      }
    }
    if (count > 0) {
      messageApi.open({
        type: 'error',
        content: `${count} items data valid`,
      });
    }

    setDefaultCheckedList([]);
    setIsBlockCountriesVisible(true);
    setBlockCompList(compIdList);
  };

  const deleteItem = (item) => {
    confirm({
      title: `Delete Selected Competition Blacklist?`,
      type: 'warn',
      onOk: () => {
        banCompetitionVideoList([
          {
            ...item,
            countryCodes: null,
          },
        ]);
      },
      maskClosable: true,
    });
  };

  const columns: ColumnsType<DataType> = [
    {
      title: 'Sport',
      dataIndex: 'sportType',
      key: 'sportType',
      render: (sportType) => <span>{SportTypeNameEnum[sportType]}</span>,
    },
    {
      title: 'Competition',
      dataIndex: 'competitionName',
      key: 'competitionName',
    },
    {
      title: 'Blacklist',
      dataIndex: 'countryCodes',
      key: 'countryCodes',
      render: (countryCodes) => {
        return <BlockedCountryName countryCodes={countryCodes} />;
      },
    },
    {
      title: 'Modified By',
      dataIndex: 'modifiedBy',
      key: 'modifiedBy',
    },
    {
      title: 'Modified At',
      dataIndex: 'modifiedAt',
      key: 'modifiedAt',
      sorter: (a: any, b: any) => {
        return a.modifiedAt - b.modifiedAt;
      },
      render: (value) => dateTimeTagComponent(value, ''),
    },
    {
      title: 'Options',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button size="small" onClick={() => block(record)}>
            <EditOutlined /> Edit
          </Button>
          <Button size="small" danger onClick={() => deleteItem(record)}>
            <DeleteOutlined /> Delete
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {contextHolder}
      <Space wrap style={{ marginBottom: 16 }}>
        <span style={{ marginLeft: 8 }}>Selected {selectedRowKeys.length} items</span>
        <Select defaultValue={sportType} style={{ width: 150 }} onChange={handleChangeSport} options={sportsArray} />
        <Input
          onChange={(e) => getInputCompId(e.target.value)}
          size="medium"
          placeholder="Filter by competition id or link"
          disabled={!sportType}
          style={{ width: 300 }}
          prefix={<SearchOutlined />}
        />
        <Button type="primary" onClick={() => setIsBlockCountriesStepVisible(true)}>
          <PlusOutlined />
          Create
        </Button>
        <UploadFile columns={['competitionLink']} onConfirm={importExcel} />
        <Button type="primary" onClick={() => getList()}>
          <RedoOutlined />
          Refresh
        </Button>
        <Button disabled={selectedRowKeys.length === 0} onClick={blockAll}>
          <StopOutlined />
          Update (Country Blocked): {selectedRowKeys.length} Selected
        </Button>
        <Button danger disabled={selectedRowKeys.length === 0} onClick={deleteAll}>
          <DeleteOutlined />
          Delete: {selectedRowKeys.length} Selected
        </Button>
      </Space>
      <Table
        rowSelection={rowSelection}
        loading={loading}
        columns={columns}
        dataSource={list}
        rowKey="competitionId"
        pagination={{
          total,
          pageSize,
          showSizeChanger: true,
          current: pageNum + 1,
          showQuickJumper: true,
          onChange: onPageChange,
          showTotal: (total, range) => (
            <Text>
              Showing <b>{range[0]}-{range[1]}</b> of <b>{total}</b>
            </Text>
          ),
        }}
      />
      <BlockCompetitionSteps
        isBlockCountriesStepVisible={isBlockCountriesStepVisible}
        onSuccess={getList}
        onCancel={() => setIsBlockCountriesStepVisible(false)}
      />
      <BlockCountries
        isBlockCountriesVisible={isBlockCountriesVisible}
        defaultCheckedList={defaultCheckedList}
        onBlockCoutriesConfirm={onBlockCoutriesConfirm}
        onClose={onCloseBlockModal}
        title="Update: Country Setting for Competition Live Stream"
      />
    </div>
  );
};

export default App;
