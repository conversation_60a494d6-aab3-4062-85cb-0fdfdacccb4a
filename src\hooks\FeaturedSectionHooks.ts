import { useCallback, useEffect, useMemo, useState } from 'react';
import Fuse from 'fuse.js';

import { FeaturedSectionApi } from '@/api';
import { getDataFromSiteUrl, getSportDataFromSiteData, debounce } from '@/utils';
import { SportTypeNameEnum, SportTypeCategoryEnum } from '@/constant';
import { FeaturedSectionDataType } from '@/model';

const { getDescription, getData, getSingleData, deleteData } = FeaturedSectionApi;

export const getListHook = (props: any) => {
  const { sportType, category, startTime, pageNum, pageSize, setAllRecord, setTotal, setLoading, messageApi } = props;
  return debounce(async () => {
    setLoading(true);
    try {
      const result = await getData({ sportType, category, startTime, pageNum, pageSize }).then((res: any) => {
        return res;
      });
      setAllRecord(result.result.list);
      setTotal(result.result.total);
    } catch (error) {
      messageApi.error('Failed to Retrieve Data!');
    } finally {
      setLoading(false);
    }
  }, 100);
};

export const handleDeleteHook = (props: any) => {
  const { confirm, id, selectedRowKeys, pageDisplayLabel, getList, messageApi } = props;
  confirm({
    title: id > 0 ? `Remove Selected ${pageDisplayLabel} (ID: ${id})?` : `Remove All Selected ${pageDisplayLabel}(s)?`,
    onOk: async () => {
      try {
        await deleteData(id > 0 ? [id] : selectedRowKeys);
        messageApi.open({
          type: 'success',
          content: `${pageDisplayLabel}(s) Delete Success!`,
        });
        getList();
      } catch (e) {
        messageApi.open({
          type: 'error',
          content: `${pageDisplayLabel}(s) Delete Failed!`,
        });
      }
    },
    onCancel: () => {},
    maskClosable: true,
  });
};

export const fuzzySearchHook = <T>(allRecord: T[], searchQuery: string, threshold = 0.3, keys: string[], setLoading: any) => {
  return useMemo(() => {
    const fuseInstance = new Fuse(allRecord, { keys, threshold });

    if (!searchQuery) {
      setLoading(false);
      return allRecord;
    }
    setLoading(true);
    const result = fuseInstance.search(searchQuery).map((item) => item.item);
    setLoading(false);
    return result;
  }, [allRecord, searchQuery, threshold]);
};

export const populateDataHook = (props: any) => {
  const { form, siteUrl, setSportType, setCategory, setDataId, recordData } = props;

  const handleSiteUrlChange = useCallback(
    debounce(() => {
      if (recordData?.siteUrl !== siteUrl) {
        if (siteUrl) {
          const siteData = getDataFromSiteUrl(siteUrl);
          const sportData = getSportDataFromSiteData(siteData[1]);

          form.setFieldValue('sportType', sportData.sport);
          form.setFieldValue('category', sportData.category);
          form.setFieldValue('dataId', siteData[2]);

          if (sportData.sport !== null && sportData.category !== null && siteData[2] !== null) {
            getDescription({ sportType: sportData.sport, category: sportData.category, dataId: siteData[2] }).then((res: any) => {
              if (res.code === 'A00000') {
                form.setFieldValue('description', res.result[0]);
                form.setFieldValue('extraDescription', res.result[1]);
              }
            });
          } else {
            form.setFieldValue('description', '');
            form.setFieldValue('extraDescription', '');
          }

          setSportType(sportData.sport);
          setCategory(sportData.category);
          setDataId(siteData[2]);
        } else {
          form.resetFields();
        }
      }
    }, 300),
    [siteUrl],
  );

  useEffect(() => {
    handleSiteUrlChange();
  }, [handleSiteUrlChange]);
};

export const handleTimeChangeHook = (props: any) => {
  const { form, dateTime } = props;
  if (dateTime) {
    const start = dateTime[0].startOf('day').unix();
    const end = dateTime[1].endOf('day').unix();
    form.setFieldsValue({
      startTime: start,
      endTime: end,
    });
  }
};

export const handleStartTimeChangeHook = (props: any) => {
  const { date, setStartTime } = props;
  setStartTime(!date ? null : date.unix());
};

export const modalResetHook = (props: any) => {
  const { isModalVisible, form, recordId, setRecordData, setSiteUrl, setSportType, setCategory, setDataId, messageApi } = props;
  useEffect(() => {
    if (!isModalVisible) {
      form.resetFields();
      setRecordData(null);
      setSiteUrl('');
      setSportType(null);
      setCategory('');
      setDataId('');
    }
    if (recordId) {
      getSingleData(recordId)
        .then((res: any) => {
          const selectedRecord: FeaturedSectionDataType = res.result;
          setRecordData(selectedRecord);
        })
        .catch((error) => {
          messageApi.open({ type: 'error', content: 'Error Updating Featured Section!' });
        });
    }
  }, [isModalVisible]);
};

export const sportTypeListHook = () => {
  return useMemo(
    () => Object.keys(SportTypeNameEnum).map((value) => ({ value: Number(value), label: SportTypeNameEnum[value] })),
    [SportTypeNameEnum],
  );
};

export const categoryListHook = (props: any) => {
  const { sportType } = props;
  return useMemo(() => {
    // temporarily hardcode to only handle football (Lock Jun Lin, 05/12/2024)
    const categoryData = sportType !== null && sportType === 0 ? SportTypeCategoryEnum[sportType] : {};
    return Object.keys(categoryData).map((value: string) => ({ value, label: categoryData[value] }));
  }, [SportTypeNameEnum, sportType]);
};
