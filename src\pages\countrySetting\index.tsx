import { FormOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { Card, Button, Space, Tag, Tooltip, Result, Alert, Typography, message, Modal, Transfer, TransferProps } from 'antd';
import React, { useRef, useCallback, useEffect, useMemo, useState } from 'react';

import { CountryList } from '@/constant/country';
import { getWhiteBlackList, updateWhiteBlackList, WhiteBlackListData } from '@/api';

const { Paragraph } = Typography;

interface RecordType {
  key: string;
  title: string;
}

const dataSource = CountryList.map(({ value, label }) => ({
  key: value,
  title: label,
}));

const CountrySetting = React.memo(() => {
  const [messageApi, contextHolder] = message.useMessage();
  const [listMaps, setListMaps] = useState<WhiteBlackListData>({ whitelist: [], blacklist: [] });
  const [listLoading, setListLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const [editType, setEditType] = useState<'whitelist' | 'blacklist' | ''>('');
  const [targetKeys, setTargetKeys] = useState<string[]>([]);
  const initialTargetKeys = useRef<string[]>([]);

  const whiteList = useMemo(
    () => CountryList.filter(({ value }) => listMaps.whitelist.includes(value)).map(({ label }) => label),
    [listMaps.whitelist],
  );
  const blacklist = useMemo(
    () => CountryList.filter(({ value }) => listMaps.blacklist.includes(value)).map(({ label }) => label),
    [listMaps.blacklist],
  );

  const okBtnDisabled = useMemo(() => {
    if (!targetKeys.length && !initialTargetKeys.current.length) {
      return true;
    } else if (targetKeys.length !== initialTargetKeys.current.length) {
      return false;
    }
    return targetKeys.every((key) => initialTargetKeys.current.includes(key));
  }, [targetKeys]);

  const onEdit = useCallback(
    (type: 'whitelist' | 'blacklist') => {
      setEditType(type);
      initialTargetKeys.current = [...listMaps[type]];
      setTargetKeys([...listMaps[type]]);
      setOpen(true);
    },
    [listMaps],
  );

  const filterOption = (inputValue: string, option: RecordType) => {
    return option.title.toLowerCase().includes(inputValue.toLowerCase());
  };

  const handleChange: TransferProps<RecordType>['onChange'] = useCallback((newTargetKeys: string[]) => {
    setTargetKeys(newTargetKeys);
  }, []);

  const getList = useCallback(async () => {
    try {
      const res = await getWhiteBlackList();
      if (res.result) {
        const { whitelist, blacklist } = res.result;
        setListMaps({
          whitelist: Array.isArray(whitelist) ? whitelist : [],
          blacklist: Array.isArray(blacklist) ? blacklist : [],
        });
      }
    } catch (error) {
      messageApi.error('Failed to get the white and black list, please try again later.');
    }
  }, []);

  const handleUpdateList = useCallback(async () => {
    setConfirmLoading(true);
    try {
      const data: WhiteBlackListData = {
        whitelist: editType === 'whitelist' ? targetKeys : listMaps.whitelist,
        blacklist: editType === 'blacklist' ? targetKeys : listMaps.blacklist,
      };
      const res = await updateWhiteBlackList(data);
      if (res.code === 'A00000') {
        messageApi.success('Update success');
        setOpen(false);
        getList();
      }
    } catch (e) {}
    setConfirmLoading(false);
  }, [listMaps, editType, targetKeys]);

  const renderTag = useCallback((tag: string) => {
    const isLongTag = tag.length > 20;
    const tagElem = (
      <Tag key={tag} style={{ userSelect: 'none', backgroundColor: '#f5f5f5' }}>
        {isLongTag ? `${tag.slice(0, 20)}...` : tag}
      </Tag>
    );
    return isLongTag ? (
      <Tooltip title={tag} key={tag}>
        {tagElem}
      </Tooltip>
    ) : (
      tagElem
    );
  }, []);

  useEffect(() => {
    setListLoading(true);
    getList().finally(() => {
      setListLoading(false);
    });
  }, []);

  const renderFooter: TransferProps<RecordType>['footer'] = (_, info) => {
    if (info?.direction === 'left') {
      return null;
    }
    return (
      <Button
        size="small"
        style={{ float: 'right', margin: 5 }}
        onClick={() => {
          if (editType) {
            setTargetKeys([...listMaps[editType]]);
          }
        }}
      >
        reload {editType}
      </Button>
    );
  };

  return (
    <>
      {contextHolder}
      <ProCard style={{ minHeight: '100%' }} split="vertical" bordered headerBordered>
        <ProCard
          colSpan="50%"
          title="White List"
          subTitle=""
          extra={
            <Button icon={<FormOutlined />} type="text" onClick={() => onEdit('whitelist')}>
              Edit
            </Button>
          }
        >
          <Card loading={listLoading} style={{ width: '100%', minHeight: 360, marginBottom: 20, backgroundColor: '#f6ffed' }}>
            {whiteList.length ? (
              <Space wrap>{whiteList.map<React.ReactNode>(renderTag)}</Space>
            ) : (
              <Result
                title="No White List"
                subTitle="You can add white list to control the countries that can watch the video."
                extra={
                  <Button type="primary" onClick={() => onEdit('whitelist')}>
                    Create Now
                  </Button>
                }
              />
            )}
          </Card>
          <Alert
            type="info"
            message="Instructions for use"
            description={
              <>
                <Paragraph style={{ marginBottom: 5 }}>
                  1. If not set the whitelist, all countries except the blacklist can watch the video.
                </Paragraph>
                <Paragraph style={{ marginBottom: 5 }}>
                  2. If only set the whitelist, only the countries in the whitelist can watch the video.
                </Paragraph>
                <Paragraph style={{ marginBottom: 0 }}>
                  3. If set the whitelist and blacklist, the countries in the whitelist and not in the blacklist can watch the
                  video.
                </Paragraph>
              </>
            }
            showIcon
            closable
          />
        </ProCard>
        <ProCard
          title="Black List"
          extra={
            <Button icon={<FormOutlined />} type="text" onClick={() => onEdit('blacklist')}>
              Edit
            </Button>
          }
        >
          <Card loading={listLoading} style={{ width: '100%', minHeight: 360, marginBottom: 20, backgroundColor: '#fff1f0' }}>
            {blacklist.length ? (
              <Space wrap>{blacklist.map<React.ReactNode>(renderTag)}</Space>
            ) : (
              <Result
                title="No Black List"
                subTitle="You can add black list to control the countries that cannot watch the video."
                extra={
                  <Button type="primary" onClick={() => onEdit('blacklist')}>
                    Create Now
                  </Button>
                }
              />
            )}
          </Card>
          <Alert
            type="info"
            message="Instructions for use"
            description={
              <>
                <Paragraph style={{ marginBottom: 5 }}>
                  1. If not set the blacklist, all countries in the whitelist can watch the video.
                </Paragraph>
                <Paragraph style={{ marginBottom: 5 }}>
                  2. If only set the blacklist, all countries except the countries in the blacklist can watch the video.
                </Paragraph>
                <Paragraph style={{ marginBottom: 0 }}>
                  3. If set the whitelist and blacklist, the countries in the whitelist and not in the blacklist can watch the
                  video.
                </Paragraph>
              </>
            }
            showIcon
            closable
          />
        </ProCard>
      </ProCard>
      <Modal
        title={`Edit ${editType}`}
        width={860}
        forceRender
        open={open}
        okText="Save"
        okButtonProps={{ disabled: okBtnDisabled }}
        onOk={handleUpdateList}
        onCancel={() => {
          setOpen(false);
          setTimeout(() => {
            initialTargetKeys.current = [];
            setTargetKeys([]);
            setEditType('');
          }, 200);
        }}
        confirmLoading={confirmLoading}
      >
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Transfer<RecordType>
            dataSource={dataSource}
            showSearch
            listStyle={{
              width: 320,
              height: 450,
            }}
            operations={[`to ${editType}`, `removed from list`]}
            rowKey={(record) => record.key}
            render={(item) => item.title}
            targetKeys={targetKeys}
            filterOption={filterOption}
            onChange={handleChange}
            footer={renderFooter}
          />
        </div>
      </Modal>
    </>
  );
});

export default CountrySetting;
