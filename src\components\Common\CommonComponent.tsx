import { InputNumber, Modal, Tag, Typography, notification } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import moment from 'moment';
import { useCallback, useEffect, useMemo, useState } from 'react';

const { Text } = Typography;
const { confirm } = Modal;

export const dateTimeTagComponent = (value: number, color: string = '') => (
  <Tag color={color}>
    <Text strong>{moment.unix(value).format('YYYY-MM-DD HH:mm')}</Text>
  </Tag>
);

export const ResponseStatusTagComponent = (value: number) => {
  if (value === 200) {
    return (
      <Tag color="green">
        <Text strong>{value}</Text>
      </Tag>
    );
  }
  return (
    <Tag color="red">
      <Text strong>{value}</Text>
    </Tag>
  );
};

export const UpdateSingleFieldModal = (props: any) => {
  const {
    resetModal,
    updateApi,
    isModalVisible,
    onCancel,
    fieldName,
    pageOrigin,
    selectedRecord,
    oldData,
    fieldDataType,
    dataChecking,
  } = props;

  const [newData, setNewData] = useState(oldData);
  const [inputStatus, setInputStatus] = useState<'' | 'error' | 'warning' | undefined>('');

  useEffect(() => {
    setNewData(oldData);
  }, [oldData]);

  const actionConfirm = (data: any) => {
    confirm({
      title: `Confirmation`,
      content: (
        <Text>
          Confirm Update <b>{fieldName}</b> Data of Selected {pageOrigin}?
        </Text>
      ),
      onOk() {
        actionResult(data);
      },
      onCancel() {},
      maskClosable: true,
    });
  };

  const actionResult = (data: any) => {
    try {
      let result = updateApi(data);
      notification.success({
        key: 'success',
        message: 'Success!',
        description: (
          <Text>
            Update Success! ${pageOrigin} ID: <b> {selectedRecord?.id || ''}</b>
          </Text>
        ),
      });
    } catch (error) {
      notification.error({
        key: 'error',
        message: 'Error!',
        description: (
          <Text>
            Update Failed! {pageOrigin} ID: <b> {selectedRecord?.id || ''}</b>
          </Text>
        ),
      });
    } finally {
      resetModal();
      setNewData(null);
    }
  };

  const handleOk = () => {
    dataChecking(selectedRecord, newData).then((res: any) => {
      if (!res) {
        setInputStatus('error');
        notification.error({
          key: 'validation-error',
          message: 'Validation Error',
          description: (
            <Text>
              Invalid input for <b>{fieldName}</b>. Please check your data.
            </Text>
          ),
        });
        return;
      }
      actionConfirm({ ...selectedRecord, [fieldName]: newData });
    });
  };

  return (
    <Modal
      title={`Update ${pageOrigin} ID: ${selectedRecord?.id}`}
      open={isModalVisible}
      onCancel={() => {
        onCancel();
        setNewData(null);
        setInputStatus('');
      }}
      // onOk={() => actionConfirm({ ...selectedRecord, [fieldName]: newData })}
      onOk={handleOk}
      destroyOnClose={true}
    >
      <p>
        Updating <b>{fieldName}</b> Data
      </p>

      {fieldDataType === 'number' ? (
        <InputNumber placeholder="New Data..." value={newData} onChange={(value) => setNewData(value)} status={inputStatus} />
      ) : (
        <TextArea rows={4} placeholder="New Data...." value={newData} allowClear onChange={(value) => setNewData(value)} />
      )}
    </Modal>
  );
};
