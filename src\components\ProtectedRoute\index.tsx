import React, { ReactNode } from 'react';
import { Result, But<PERSON>, Spin } from 'antd';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { useAuth } from '@/contexts/AuthContext';
import { history } from 'umi';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: string;
  fallbackPath?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredRole,
  fallbackPath = '/login' 
}) => {
  const { isAuthenticated, hasRole, loading, user } = useAuth();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" tip="Verifying access..." />
      </div>
    );
  }

  // Not authenticated
  if (!isAuthenticated) {
    return (
      <Result
        status="403"
        title="Authentication Required"
        subTitle="You need to be logged in to access this page."
        icon={<UserOutlined style={{ color: '#1890ff' }} />}
        extra={
          <Button type="primary" onClick={() => history.push(fallbackPath)}>
            Go to Login
          </Button>
        }
      />
    );
  }

  // Authenticated but insufficient role
  if (requiredRole && !hasRole(requiredRole)) {
    return (
      <Result
        status="403"
        title="Access Denied"
        subTitle={`You don't have sufficient permissions to access this page. Required role: ${requiredRole}`}
        icon={<LockOutlined style={{ color: '#ff4d4f' }} />}
        extra={
          <div>
            <p style={{ marginBottom: '16px', color: '#666' }}>
              Current user: <strong>{user?.username}</strong>
            </p>
            <p style={{ marginBottom: '16px', color: '#666' }}>
              Your roles: {user?.authorities?.map(auth => auth.authority).join(', ') || 'None'}
            </p>
            <Button type="primary" onClick={() => history.push('/')}>
              Go to Dashboard
            </Button>
          </div>
        }
      />
    );
  }

  // All checks passed - render the protected content
  return <>{children}</>;
};

export default ProtectedRoute;
