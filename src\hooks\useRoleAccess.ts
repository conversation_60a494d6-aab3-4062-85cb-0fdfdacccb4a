import { useAuth, ROLES } from '@/contexts/AuthContext';
import { useEffect } from 'react';
import { history } from 'umi';
import { message } from 'antd';
import { UseRoleAccessOptions } from '@/model';

export const useRoleAccess = (options: UseRoleAccessOptions) => {
  const { requiredRole, redirectPath = '/', showMessage = true } = options;
  const { isAuthenticated, hasRole, loading } = useAuth();

  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated) {
        if (showMessage) {
          message.error('Please login to access this page');
        }
        history.push('/login');
        return;
      }

      if (!hasRole(requiredRole)) {
        if (showMessage) {
          message.error(`Access denied. User Does Not Have Access Privilege!`);
        }
        history.push(redirectPath);
        return;
      }
    }
  }, [isAuthenticated, hasRole, requiredRole, loading, redirectPath, showMessage]);

  return {
    hasAccess: isAuthenticated && hasRole(requiredRole),
    loading,
  };
};

// Convenience hooks for specific roles
export const useMasterAccess = (redirectPath?: string) => {
  return useRoleAccess({ 
    requiredRole: ROLES.MASTER, 
    redirectPath 
  });
};

export const useAdminAccess = (redirectPath?: string) => {
  return useRoleAccess({ 
    requiredRole: ROLES.ADMIN, 
    redirectPath 
  });
};

export const useUserAccess = (redirectPath?: string) => {
  return useRoleAccess({ 
    requiredRole: ROLES.USER, 
    redirectPath 
  });
};
