import { useAuth } from '@/contexts/AuthContext';
import { useEffect } from 'react';
import { history } from 'umi';
import { message } from 'antd';
import { UseRoleAccessOptions } from '@/model';
import { accessRole } from '@/constant/config';

export const useRoleAccess = (options: UseRoleAccessOptions) => {
  const { requiredRole, redirectPath = '/', showMessage = true } = options;
  const { isAuthenticated, hasRole, loading } = useAuth();

  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated) {
        if (showMessage) {
          message.error('Please login to access this page');
        }
        history.push('/login');
        return;
      }

      if (!hasRole(requiredRole)) {
        if (showMessage) {
          message.error(`Access denied. User Does Not Have Access Privilege!`);
        }
        history.push(redirectPath);
        return;
      }
    }
  }, [isAuthenticated, hasRole, requiredRole, loading, redirectPath, showMessage]);

  return {
    hasAccess: isAuthenticated && hasRole(requiredRole),
    loading,
  };
};

// Convenience hooks for specific roles
export const useMasterAccess = (redirectPath?: string) => {
  return useRoleAccess({ 
    requiredRole: accessRole.MASTER, 
    redirectPath 
  });
};

export const useAdminAccess = (redirectPath?: string) => {
  return useRoleAccess({ 
    requiredRole: accessRole.ADMIN, 
    redirectPath 
  });
};

export const useUserAccess = (redirectPath?: string) => {
  return useRoleAccess({ 
    requiredRole: accessRole.USER, 
    redirectPath 
  });
};
